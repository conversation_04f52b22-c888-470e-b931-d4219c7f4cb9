.searchBox{
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
  
    input{
      padding: 0px 12px;
      height: 40px;
    }
  }

.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    max-height: 600px;

    &.editLinestbl{
        max-height: 480px;
    }

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

    table {
        width: 100%;
        white-space: nowrap;
        border-collapse: collapse;
        border-spacing: 0;

        thead {
            tr {

                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 4px 12px;
                    height: 35px;
                    position: sticky;
                    z-index: 9;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
                }
            }
        }

        tbody {
            background-color: #fff;
            position: relative;

            tr {
                margin: 0;
                position: relative;

                &:nth-child(even) {
                    background-color: #f2f2f2;
                    td{
                          &:first-child{
                          background-color: #f2f2f2;
                      }
                    }

                }

               
                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 8px 12px;
                    height: 42px;
                    position: relative;
                    &.spreadMarkup{
                        color:green;
                        span{
                            background: #d0ebd0;
                            padding: 4px 6px;
                            border-radius: 5px;
                        }
                    }
                    &.spreadMarkdown{
                        color:red;
                        span{
                            background: #ffc4c4;
                            padding: 4px 6px;
                            border-radius: 5px;
                        }
                    }
                    &.spreadZero{
                        color:grey;
                    }
                  
                }
            }
        }
    }
}

.noDataFound{
    text-align: center;
}


.note {
    text-align: center;
    font-size: 16px;
    margin-bottom: 15px;
    color: var(--primaryColor);
    font-weight: bold;
    text-align: left;
    margin-top: 0px;
}