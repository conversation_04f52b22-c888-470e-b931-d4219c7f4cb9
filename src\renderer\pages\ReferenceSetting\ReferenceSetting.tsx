import { useEffect } from "react";
import Loader from "../../components/common/Loader";
import styles from "./ReferenceSetting.module.scss";
import MatPopup from "../../components/common/MatPopup";
import { useImmer } from "use-immer";
import usePostUpdateReferenceCacheData from "../../hooks/usePostUpdateReferenceCacheData";
import { referecneDataType } from "../../utils/constant";


const ReferenceSetting = () => {
  
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [confirmationPopupData, setConfirmationPopupData] = useImmer<any>(null);

  const {
    mutate: sendReferenceDataType,
    data: updateReferenceDataCache,
    isLoading: isUpdateReferenceDataLoading,
  } = usePostUpdateReferenceCacheData();

  useEffect(() => {
    if (updateReferenceDataCache) {
      setApiResponseMessage(updateReferenceDataCache);
    }
  }, [updateReferenceDataCache]);


  const updateReferenceData = (type: string) => {
    setShowConfirmationPopup(true);
    let payload;
    if(type === referecneDataType.widgetServiceReferenceData){
      payload = {
        "data": {
            "type": referecneDataType.widgetServiceReferenceData
        }
      }
    }else if(type === referecneDataType.homepageReferenceData){
      payload = {
        "data": {
            "type": referecneDataType.homepageReferenceData
        }
      }
    }
    setConfirmationPopupData(payload)

  }

  const confirmationPopupYes = () => {
    if (confirmationPopupData) {
      sendReferenceDataType(confirmationPopupData);
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setConfirmationPopupData(null);
  };

  return (
    <div>
      {isUpdateReferenceDataLoading ? (
        <Loader />
      ) : (
        <div className={styles.settingPage}>
          <p className={styles.referenceText}>Reference Setting</p>
          <div className={styles.referenceDataText}>
            <label className={styles.referenceLabelText}>Widget Reference Data  </label>
            <button className={styles.submitBtn} onClick={() => updateReferenceData(referecneDataType.widgetServiceReferenceData)} > Update</button>
          </div>
          <div className={styles.referenceDataText}>
            <label className={styles.referenceLabelText}>HomePage Reference Data  </label>
            <button className={styles.submitBtn} onClick={() => updateReferenceData(referecneDataType.homepageReferenceData)} > Update</button>
          </div>
        </div>
      )}

      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Do you want to continue ?</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
    </div>
  );
};

export default ReferenceSetting;
