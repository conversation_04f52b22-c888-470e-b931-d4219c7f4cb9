import { LocalizationProvider, MobileDatePicker } from "@mui/x-date-pickers";
import styles from "./CustomDatePicker.module.scss";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { useEffect, useRef, useState } from "react";
import { ReactComponent as CalendarIcon } from '../../../../assests/images/calender.svg';
import clsx from "clsx";
interface CustomDatePickerProps{
    value?: dayjs.Dayjs | null | undefined,
    onChange?:((value: dayjs.Dayjs | null) => void) | undefined,
    disableDates?:((day: dayjs.Dayjs) => boolean), 
    format?:string, 
    openOnFocus?:boolean,
    onClose?:Function, 
    minDate?: dayjs.Dayjs | undefined,
    maxDate?: dayjs.Dayjs | undefined,
    label?: string 
}   

const CustomDatePicker = ({value, onChange, disableDates, format, openOnFocus, onClose, minDate, maxDate, label}:CustomDatePickerProps) => {
    const [openCalendar, setOpenCalendar] = useState(false);
    const calendarIconRef = useRef(null);
    useEffect(()=>{
        if(openOnFocus){
            setOpenCalendar(true);
        }
    },[openOnFocus])
    return (
        <div className={clsx(styles.datePickerContainer,"datePickerContainer")}>
            <button className={styles.calendarIcon} onClick={() => { setOpenCalendar((prev) => !prev) }} ref={calendarIconRef} ><CalendarIcon /></button>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
                <MobileDatePicker
                    className='datePicker'
                    value={value}
                    format={format}
                    shouldDisableDate={disableDates}
                    minDate={minDate}
                    maxDate={maxDate}
                    open={openCalendar}
                    label={label}
                    onClose={() => { setOpenCalendar(false); onClose && onClose()}}
                    onAccept={onChange}
            
                    slotProps={{
                        textField: {
                            onClick: (e) => { setOpenCalendar(true)}
                        },
                    }}
                />
            </LocalizationProvider>
        </div>
    )
}

export default CustomDatePicker;