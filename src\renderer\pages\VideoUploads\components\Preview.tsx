// @ts-nocheck
import React, { useEffect } from "react";
import styles from "../VideoUploads.module.scss";
import { useState, useRef } from "react";
import { CircularProgress } from "@mui/material";
import VideoPlayer from "../../../components/VideoPlayer";
import { ReactComponent as EyeIcon } from "../../../../assests/images/icon_eye.svg";
import { ReactComponent as PlayIcon } from "../../../../assests/images/VideoPlay-Icon.svg";
import { previewRadioButtonText } from "../../../utils/constant";
import clsx from "clsx";
interface PreviewProps {
  device: string;
  queryData: object;
}
import { ReactComponent as ShareVideo } from "../../../../assests/images/ShareVideo.svg"

function Preview({ device, queryData }: any) {
  
  const [videoData, setVideoData] = useState(null);
  const [tagList, setTagList] = useState(null);
  const [currVideo, setCurrVideo] = useState("");
  const [currTag , setCurrTag] = useState(null);
  const [currIndex , setCurrIndex] = useState(0);
  const videoRef = useRef();
  const [disabledNextBtn,setDisableNextBtn] = useState()
  const [currThumbnail,setCurrThumbnail] = useState("")
  const [isExpanded, setIsExpanded] = useState(false);
  const [currSubtitle , setCurrSubtitle] = useState('');
  const [subtitlProperty, setSubtitlProperty] = useState({});
  const [targetedVideo,setTargetedVideo] = useState(null)
  const characterLimit = device === previewRadioButtonText.mobile ? 120 : 130;
  const videoSectionPlayingRef = useRef()
  const [isNew , setIsNew] = useState(false);

  const shortTextLimit = 130;
  const fullTextLimit = 180;
  const isLongText = queryData.videoData[currTag?.query_param]?.[currIndex]?.description?.length > fullTextLimit;
  const truncatedText = queryData.videoData[currTag?.query_param]?.[currIndex]?.description?.length > shortTextLimit ? queryData.videoData[currTag?.query_param]?.[currIndex]?.description?.slice(0, shortTextLimit) + '...' : queryData.videoData[currTag?.query_param]?.[currIndex]?.description;
  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };
  
  useEffect(() => {
    if (queryData) {
      setVideoData(queryData.videoData);
      setTagList(queryData.tag);
    }
  }, [queryData]);

  useEffect(() => {
    if (currVideo && videoRef) {
      videoRef?.current.play();
    }
  }, [currVideo]);

  useEffect(() =>{
    if(device && currThumbnail){
      const selectedVideoThumbnail = (device === previewRadioButtonText.desktop) ? 
      queryData.videoData[currTag?.query_param][currIndex].thumbnail_s3_url_map?.electron_player : 
      queryData.videoData[currTag?.query_param][currIndex].thumbnail_s3_url_map?.intro_mobile
      setCurrThumbnail(selectedVideoThumbnail)
    }
  },[device])
  const playVideoWithRefresh = (videoUrl:string)=>{
    setCurrVideo();
    setTimeout(()=>{
      setCurrVideo(videoUrl);
    }, 0);
  }

  const playNextVideo = () => {
       const lengthOfCurrTagArray = queryData.videoData[currTag.query_param]?.length;
       const nextIndex = (currIndex + 1) % lengthOfCurrTagArray;
       setCurrIndex(nextIndex);
       setTargetedVideo(queryData.videoData[currTag?.query_param][nextIndex]);
       if(queryData.videoData[currTag?.query_param][nextIndex].video_s3_url){
        playVideoWithRefresh(videoData[currTag?.query_param][nextIndex].video_s3_url);   
        setCurrSubtitle(videoData[currTag?.query_param][nextIndex].subtitle_s3_url);

        if(videoData[currTag?.query_param][nextIndex]?.isNew) {
          setSubtitlProperty({"captionBlobUrl":videoData[currTag?.query_param][nextIndex].subtitle_s3_url})
        } else {
          setSubtitlProperty({"captionUrl":videoData[currTag?.query_param][nextIndex].subtitle_s3_url})
        }
         setCurrThumbnail("")    
       }else{
        setCurrVideo("")
        setCurrSubtitle();
        setIsNew(false);
        const selectedVideoThumbnail = (device === previewRadioButtonText.desktop) ? 
        queryData.videoData[currTag?.query_param][nextIndex].thumbnail_s3_url_map?.electron_player : 
        queryData.videoData[currTag?.query_param][nextIndex].thumbnail_s3_url_map?.intro_mobile
        setCurrThumbnail(selectedVideoThumbnail);
       }
  }

  const playVideo = (tag:any, index :number, value :any,query_param) => {
    setCurrTag(tag);
    setCurrIndex(index);
    setCurrSubtitle(value.subtitle_s3_url);
    setTargetedVideo(value)
    if(value?.isNew) {
      setSubtitlProperty({"captionBlobUrl":value.subtitle_s3_url})
    } else {
      setSubtitlProperty({"captionUrl":value.subtitle_s3_url})
    }
    if(value.video_s3_url){
      setCurrThumbnail("")
      playVideoWithRefresh(value.video_s3_url);
      setDisableNextBtn(!(videoData[query_param]?.length > 1))                                
      videoRef?.current?.load();
      videoRef?.current?.play();
    }else {
      setCurrThumbnail(
        device === previewRadioButtonText.desktop ? 
          value.thumbnail_s3_url_map?.electron_player : 
          value.thumbnail_s3_url_map?.intro_mobile
      )
      setCurrVideo("");
    }       
    setTimeout(()=>{videoSectionPlayingRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });},10);
  }

  return queryData ? (
    <div
      className={clsx( device === previewRadioButtonText.desktop ? styles.preview : styles.mobilepreview)}
    >
      <div className={styles.staticTitle} ref={videoSectionPlayingRef}>
        <h1>BRYZOS = TRUST</h1>
        <p>
          Here is your all-access pass to get to know the Bryzos team. Check out
          this behind-the-scenes view into the discussions and decisions made to
          optimize this product designed with you specifically in mind. Let us
          show you why this app will forever change the way you trade steel.
        </p>
      </div>
      {(currVideo && videoRef) && (
        <VideoPlayer videoRef={videoRef} autoPlay={true} url={currVideo} playNextVideo = {playNextVideo} disabled={disabledNextBtn} {...subtitlProperty}/>
      )}
      {
        (currThumbnail) && (
          <img src={currThumbnail} className={styles.previewThumbnail}></img>
        )
      }
      {
        ((currVideo || currThumbnail) &&  targetedVideo)  && (
          <div className={styles.currentVideoTitle}>
              <h1>{targetedVideo.title}</h1>
              {targetedVideo?.description && <div className={styles.descriptionVideo}>
                    <span>
                        {isExpanded || !isLongText ? targetedVideo?.description : truncatedText}
                  </span>
                    {isLongText && (
                      <button onClick={handleToggle} className={styles.showMoreLessBtn}>
                        {isExpanded ? 'Show Less' : 'Show More'}
                      </button>
                    )}
                  </div>
                  }    
            {(currVideo && targetedVideo) && (
              <div className={styles.viewsDiv}>
              {(targetedVideo.view_count > 0) && (
                  <div className={styles.views}>
                   <EyeIcon className={styles.eyeCurrentTitleIcon}/>
                   <p>{targetedVideo.view_count} Views</p> 
                   </div>
                )}
                {(targetedVideo.share_video_url?.trim().length > 0) && (
               <span className={styles.shareVideoIcon} ><ShareVideo/> Share</span>
            )} 
              </div>
            )} 
           
            </div>
        )
      }
      <div className={clsx( device === previewRadioButtonText.desktop ? styles.episodesThumbnailSection : styles.episodesThumbnailSectionMobile )}>
        {tagList &&
          tagList?.map((tag: any, index: number) => {
            let query_param = tag?.query_param;
            if (!tag?.show_on_app) {
              return null;
            }
            return (
              <div key={index}>
                {(videoData && videoData[query_param]?.length) && (
                  <div>
                  <div className={styles.previewContent}>
                    <h1>{tag?.display_title.toUpperCase()}</h1>{" "}
                    <p>{tag?.display_subtitle}</p>
                  </div>                
                <div className={styles.slider}>
                  {videoData &&
                    videoData[query_param]?.map((value: any, index) => {
                      return (
                        <div className={styles.ContentCarousel} key={index}>
                          <div className={styles.imageContainer}>
                            <img
                              src={value.thumbnail_s3_url_map?.thumbnail_app}
                              alt={"thumbnail"}
                              className={styles.ContentCarouselImage}
                              onClick={()=>{playVideo(tag,index,value,query_param)}}
                            />
                            {(!!value?.video_s3_url && !(tag == currTag && currIndex ==index)) &&  <PlayIcon className={styles.videoIcon}> </PlayIcon>}
                           
                          {(tag == currTag && currIndex == index && !currThumbnail )&& <div className={styles.nowPlayingOverlay}> <h1>NOW PLAYING</h1>  </div>}
                          {(tag == currTag && currIndex == index && currThumbnail )&& <div className={styles.nowPlayingOverlay}>  </div>}
                          </div>
                          <p>{value.title}</p>
                          {(value?.video_s3_url && value?.view_count > 0) && <div className={styles.views}>
                            <EyeIcon />
                            <p>{value.view_count} Views</p>
                          </div> }
                        </div>
                      );
                    })}
                </div>
                </div>
                )}
              </div>
            );
          })}
      </div>
    </div>
  ) : (
    <div className={styles.loader}>
      <CircularProgress />
    </div>
  );
}

export default Preview;
