import styles from "../EmailAttachments.module.scss";
import React, { useContext, useState } from "react";
import MatPopup from "../../../components/common/MatPopup";
import { ReactComponent as CloseIcon } from '../../../../assests/images/close-icon.svg';
import { splitAndFormatItem } from "../../../utils/helper";
import { ReactComponent as ShowExtractedInvoice} from '../../../../assests/images/show-dialog.svg';
import clsx from "clsx";
import { Tooltip } from "@mui/material";

const ExtractedFieldsData = ({ attachmentData , headerName }: any) => {
    const [attachmentId, setAttachmentId] = useState<string>('');

    const handleFileOnClick = (attachment: { id: string; }, event: React.MouseEvent<HTMLElement>) => {
        setAttachmentId(attachment.id)
    }
    const closePopup = () => {
        setAttachmentId('');
    };    
    return (
      <>
        {(attachmentData.invoice_line_items?.length) && (
          <>
             <Tooltip title = {headerName}>
            <span
              className={clsx(styles.showBtn,styles.showFile)}
              onClick={(e) => {
                handleFileOnClick(attachmentData, e);
              }}
            >
               <ShowExtractedInvoice/>
            </span>
            </Tooltip>
            {
              attachmentId === attachmentData.id && (
                <MatPopup
                  className={styles.orderContinuePopup}
                  open={attachmentId === attachmentData.id}
                  classes={{
                    paper: styles.extractedFieldsDataPopup,
                  }}
                >
                  <div className={styles.extractedFieldsContent}>
                    <div className={styles.extractedFieldstitle}>
                      Extracted Invoice Line Items
                    </div>
                    <button className={styles.closeBtn} onClick={closePopup}>
                      <CloseIcon />
                    </button>
                    <>
                      <div className={styles.extractedFieldsInfo}>
                        <div className={styles.tblExtractedFieldsTbl}>
                          {(attachmentData?.invoice_line_items?.length) && (
                            <table>
                              <thead>
                                <tr>
                                    {Object.keys(
                                      attachmentData.invoice_line_items[0]
                                    ).map((key) => (
                                      <th key={key}>
                                        {splitAndFormatItem(key, "_")}
                                      </th>
                                    ))}
                                </tr>
                              </thead>
                              <tbody>
                                {attachmentData?.invoice_line_items?.map(
                                  (lineItem: any, index: number) => (
                                    <tr key={index}>
                                      {Object.keys(lineItem).map((key) => (
                                        <td key={key}>
                                          {typeof lineItem[key] === "object" &&
                                          lineItem[key] !== null ? (
                                            <table className={styles.additionalItemsTable}>
                                              <tbody>
                                                {(Object.keys(lineItem[key])?.length > 0) && Object.keys(lineItem[key]).map(
                                                  (nestedKey) => {
                                                    const nestedItem = lineItem[key][nestedKey]
                                                    const additionalData = (Array.isArray(nestedItem) || (nestedItem !== null && typeof nestedItem === 'object'))
                                                    ? JSON.stringify(nestedItem) : nestedItem
                                                    return (
                                                       <tr key={nestedKey}>
                                                      <td>
                                                        {splitAndFormatItem(
                                                          nestedKey,
                                                          "_"
                                                        )}
                                                        :
                                                      </td>
                                                      <td>  
                                                      {decodeURIComponent(encodeURIComponent(additionalData))}
                                                      </td>
                                                    </tr>
                                                    )
                                                  }
                                                )}
                                              </tbody>
                                            </table>
                                          ) : (
                                            lineItem[key]
                                          )}
                                        </td>
                                      ))}
                                    </tr>
                                  )
                                )}
                              </tbody>
                            </table>
                          )}
                        </div>
                      </div>
                    </>
                  </div>
                </MatPopup>
              )
            }
          </>
        )}
      </>
    );
}
export default ExtractedFieldsData;