.updateDatabase{
    text-align: center;
    padding: 6px 23px;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    background-color: #343a40;
    font-size: 15px;
    color: #fff;
    cursor: pointer;
}

.orderContinuePopup {
    h2 {
        display: none;
    }
    .continuePopup {
        padding: 20px;
        text-align: center;
        width: 300px;
        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }
        .continuetext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
        }
        .yesAndnoBtn {
            display: flex;
            gap: 10px;
            .okBtn {
                width: 100%;
                height: 45px;
                border-radius: 6px;
                text-decoration: none;
                gap: 8px;
                border: none;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--primaryColor);
                color: #fff;
            }
        }
    }
}