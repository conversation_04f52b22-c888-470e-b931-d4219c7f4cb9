
.uploadBox {
    height: 250px;
    margin-top: 8px;
    border-radius: 4px;
    border: dashed 1px #1c40e7;
    background-color: #e9f3ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0px 12px;
    
    .uploadHeading {
        font-size: 14px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #0f0f14;
        text-transform: uppercase;
      }
  

      .uploadText {
        font-size: 12px;
        line-height: 1.6;
        text-align: center;
        color: #71737f;
        margin-top: 0px;
      }
  
    input {
      display: none;
    }
  }

  .mediaUploadWarningMsgDiv {
    color: #ff9b00;
    display: flex;
    margin-top: 6px;
    align-items:flex-start;
    svg{
      margin-right: 4px;
    }
    .mediaWarningMsg {
      font-size: 12px;
    }
  }

  
  .continuePopBtn {
    .continueBtn {
      padding: 8px 30px;
      border-radius: 6px;
      background-color: #16b9ff;
      font-size: 14px;
      font-weight: bold;
      line-height: 1.6;
      text-align: center;
      color: #fff;
      text-transform: uppercase;
      width: 100%;
      cursor: pointer;
      border: 0px;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      @media (max-width: 767px) {
        padding: 8px 20px;
        font-size: 10px;
      }
    }
  }

  
.uploadBoxImage {
    height: 250px;
    margin-top: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
    width: 100%;
    position: relative;
  
    video {
      width: 100%;
      height: 100%;
    }
  
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  
.closeIcon {
  position: absolute;
  right: -10px;
  cursor: pointer;
  z-index: 99;
  top: -12px;
  display: flex;
    svg {
      path {
        fill: #ff0000;
      }
    }
  }