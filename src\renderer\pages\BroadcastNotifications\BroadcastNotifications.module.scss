.sendNotificationMain {
  border: 1px solid #f1f1f1;
  padding: 20px;
  width: 100%;
  max-width: 100%;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 6px;

  @media (max-width:767px) {
    margin: 16px auto;
    padding: 16px;
  }

  .title {
    font-size: 18px;
    line-height: normal;
    margin-bottom: 40px;
    font-weight: 600;
    color: var(--primaryColor);

    @media (max-width:767px) {
      margin-bottom: 20px;
    }
  }


  label {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
  }
}

.approveRejectPopup {
  h2 {
    display: none;
  }

  .successfullyUpdated {
    padding: 20px;
    text-align: center;
    width: 300px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 240px;
    }

    .successfullytext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);
    }

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
    }

  }
}

.orderContinuePopup {
  z-index: 100000;
  h2 {
    display: none;
  }
}

.continuePopup {
  padding: 20px;
  text-align: center;
  width: 300px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 240px;
  }

  .continuetext {
    text-align: center;
    font-size: 20px;
    margin-bottom: 24px;
    color: var(--primaryColor);
  }

  .yesAndnoBtn {
    display: flex;
    gap: 10px;

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      gap: 8px;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
    }

  }
}

.btnSendNotif {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  button {
    height: 38px;
    color: #fff;
    background-color: var(--primaryColor);
    border-radius: 4px;
    cursor: pointer;
    padding: 6px 18px;
    border: 0px;
    font-size: 16px;
    line-height: normal;

    @media (max-width:767px) {
      font-size: 14px;
    }
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
.autocompleteDescPanel {
  border-radius: 4px;
  width: 100%;
  max-width: 350px;
  height: 48px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  padding-right: 4px;
  border-radius: 0px 0px 4px 4px;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 316px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
  }

  &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px #a8b2bb;
      border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
  }


  li {    
    font-size: 16px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #333;
    box-shadow: none;
    padding: 4px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;

    &:hover {
      border-radius: 2px;
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      // background-color: #EBF2FF;
      // color: #397aff;
      background-color: #fff;
      color: #000;
    }
  }
}
.selectDropdown {
  width: 100%;
  max-width: 350px;
  height: 48px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 100%;
  }
}
.InputFieldcss {
  width: 350px;
  height: 40px;
  padding-left: 20px;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 15px;
  outline: none;

  @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 100%;
  }

  &:focus-visible {
      border: 1px solid #000;
  }
}
.inputField{
  width: 350px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 100%;
  }
}

.customNotificationMain{
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ced4da;
  display: flex;
  gap: 20px;
  @media (max-width:1480px) {
    flex-direction: column;
  }
  .inputSection{
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    @media (max-width:480px) {
      flex-direction: column;
      margin-bottom: 16px;
      align-items: flex-start;
    }
    label.messageLbl{      
      font-size: 16px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #333;
      margin-right: 12px;
      width: 145px;
      @media (max-width:480px) {
        font-size: 14px;
        width: auto;
        margin-right: 0px;
        margin-bottom: 6px;
      }
    }

    .rightGrid{
      display: flex;
      align-items: center;
      input{
        margin-top: 0px;
      }
      @media (max-width:480px) {
        width: 100%;
      }
    }
  

    .lblTitle{      
      font-size: 16px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #333;
      margin-right: 12px;
      width: 145px;
      margin-top: 0px;
      margin-bottom: 0px;
      @media (max-width:480px) {
        font-size: 14px;
        width: auto;
        margin-right: 0px;
        margin-bottom: 6px;
      }
    }

      
    .lblRadio{
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4;
      text-align: left;
      color: #333;
      margin-right: 2px;
    }
    .radioInput{
      margin-right: 16px;
    }
  }
}

.sendToOptions.sendToOptions{
  .lblRadio{
    margin-right: 12px;
  }
  .radioInput{
    margin-right:6px;
  }
}

.InputBox{
  width: 280px;
  height: 36px;
  padding: 10px 8px 10px 12px;
  border-radius: 4px;
  border: 1px solid #ced4da;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.4;
  text-align: left;
  color: #333;
  @media (max-width:480px) {
    width: 100%;
  }
  &:focus{
    outline: none;
  }
}

input[type='radio'],
input[type='checkbox'] {
    width: 15px;
    height: 15px;
    margin-bottom: 0px;
}
.messageDiv{
  width: 40%;
  @media (max-width:480px) {
    width: 100%;
  }
}
.previewDiv{
  width: 60%;
  @media (max-width:480px) {
    width: 100%;
    overflow: auto;
  }
  .previewTostMessage{
    background-image: url('../../../assests/images/image.png');
    background-repeat: no-repeat;
    height: 335px;
    width: 598px;
    position: relative;
  }

  .previewMobileTostMessage{
    background-image: url('../../../assests/images/mob_preview.PNG');
    background-size: cover;
    position: relative;
    width: 375px;
    height: 700px;
    .snackbarContainer{
      width: 96%;
      left: 7px;
      top: 10px;
      height: 44px;
      align-items: center;
    }
  }
  
}
.previewSelectBtn{
  display: flex;
  .lblMobile{
    margin-left: 10px;
  }
}
.snackbarContainer {
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  width: 570px;
  position: absolute;
  right: 15px;
  z-index: 11000;
  display: flex;
  align-items: center;
  padding: 9px 10px 9px 10px;
  border-radius: 4px;
  transition: opacity 0.3s ease-in-out; 
  margin-top: 40px;

  .content {
      flex: 1;
      font-size: 14px;
      color: #fff;
      word-break: break-all;
      display: flex;
      align-items: center;
  }

  .actionBtn {
      background: #fff;
      color: #000;
      line-height: normal;
      height: 24px;
      width: 72px;
      border-radius: 3px;
      font-size: 12px;
      margin-left: 16px;
      border: none;
  }
  .closeBtn{
      display: flex;
      border-radius: 50%;
      position: relative;
      left: 5px;
      transition: all 0.1s;
      border: none;
      background: none;
      padding: 0px;
      height: 24px;
      svg{
          width: 24px;
          height: 24px;
      }
      &:hover{
          background-color: #fff;
          svg{
              path{
                  fill:#ff0000
              }
          }
      }
  }
}

.tblscroll.tblscroll {
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 35px;
  max-height: 700px;

  &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
  }

  &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px #a8b2bb;
      border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
  }

  table {
      width: 100%;
      overflow-x: auto;
      white-space: nowrap;
      margin-bottom: 10px;
      border-collapse: collapse;
      border-spacing: 0;

      thead {
          tr {

              th {
                  line-height: 1.2;
                  font-weight: 600;
                  font-size: 16px;
                  margin: 0;
                  text-align: left;
                  padding: 6px 12px;
                  color: #fff;
                  height: 35px;
                  position: sticky;
                  top: 0;
                  background: #676f7c;
                  color: #fff;

              }

              td {
                  line-height: 2.5;
                  font-weight: 600;
                  font-size: 16px;
                  margin: 0;

              }
          }
      }

      tbody {
          background-color: #fff;
          tr {
              margin: 0;


              &:nth-child(even) {
                  background-color: #f2f2f2;
              }

              td {
                  color: var(--primaryColor);
                  font-size: 16px;
                  margin: 0;
                  padding: 6px 12px;
                  height: 42px;
                  &.delRow{
                    color: #ff0000;
                    font-size: 16px;
                    cursor: pointer;
                  }
                  

              }
          }
      }
  }
}

.autoCompleteSendNotifi{
  width: 100%;
}

