import styles from "./FileDrop.module.scss"
import { ReactComponent as UploadImage } from '../../../../assests/images/icon-Upload-Files.svg';
import { useDropzone } from "react-dropzone";
import { useCallback, useEffect } from "react";
interface FileDropProps{
    title?:string,
    success?:Function,
    error?:Function, 
    label?:string, 
    buttonLabel?:string,
    types?:Array<string>,
    isDataAvailable?: boolean,
    setShowConfirmationPopup?: React.Dispatch<React.SetStateAction<boolean>>
    confirmationPopupYes?: boolean,
    setConfirmationPopupYes?: React.Dispatch<React.SetStateAction<boolean>>
}
const FileDrop = ({success=()=>{}, error=()=>{}, title="Drag and Drop", label="Drag and drop your files anywhere or", buttonLabel="Click here to browse", types=[], setShowConfirmationPopup, isDataAvailable=false, confirmationPopupYes=false, setConfirmationPopupYes}:FileDropProps)=>{
    const onDrop = useCallback((acceptedFiles: any) => {
        if (acceptedFiles.length) {
            
            const file = acceptedFiles[0];
            const ext = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();
            if (types.indexOf(ext) === -1) {
                error({file, message:"Unsupported extension", code:1})
                return;
            }
            success({file});
        }
    }, []);
    const { getRootProps, getInputProps, open } = useDropzone({ onDrop: onDrop, noClick: true });
    
    useEffect(()=>{
        if(confirmationPopupYes){
            open()
            if(setConfirmationPopupYes) setConfirmationPopupYes(false)
        }
    },[confirmationPopupYes])

    const fileDropOnClick = () => {
        if(isDataAvailable){
            if(setShowConfirmationPopup) setShowConfirmationPopup(true);
        }else{
            open()
        }
    }
    

    return <>
        <div className={styles.uploadBox} {...getRootProps()}>
            <UploadImage />
            {title?<p className={styles.uploadHeading}>{title}</p>:<p className={styles.uploadHeading}></p>}
            {label?<p className={styles.uploadText1}>{label}</p>:<p className={styles.uploadText1}></p>}
            <div className={styles.continuePopBtn}>
                <button className={styles.continueBtn} onClick={fileDropOnClick}>{buttonLabel}</button>
            </div>
            <input {...getInputProps()} multiple={false} />
        </div>
    </>
}

export default FileDrop