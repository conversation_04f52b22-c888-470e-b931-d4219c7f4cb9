import styles from "../MakeApayment.module.scss";
import { useContext, useEffect, useState } from "react";
import Autocomplete from "@mui/material/Autocomplete";
import { TextField, Tooltip } from "@mui/material";
import MatPopup from "../../../../components/common/MatPopup";
import { CASS_ADHOC_SELLER_UNIQUE_IDENTIFIER_LENGTH, CASS_ADHOC_SELLER_UNIQUE_IDENTIFIER_PREFIX, CASS_SELLER_UNIQUE_IDENTIFIER_PREFIX, SUPPLIER } from "../../../../utils/constant";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore 
import TrueVaultClient from 'truevault';
import axios from "axios";
import { CommonCtx } from "../../../AppContainer";
import { format2DecimalPlaces, maskData } from "../../../../utils/helper";
import ShortUniqueId from "short-unique-id";
import clsx from "clsx";

const AdHocPayment = ({ selectedPoNumber, watch, selectedPoObj, getValues, yesButtonClick, register, errors, referenceData, setValue, adhocCassTransaction, adhocCassSellerSetup, setShowLoader,
    saveAdhocSellerSetup, setNewlyCreatedUser, isValid, setError, clearErrors, isEmail, getSellerPaymentSetup,updateSellerAdhocBankDescriptor
}: any) => {
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const DISBURSEMENT_METHOD = referenceData?.reference_cass_disbursement_method?.[0]?.cass_code;
    const accountNumber = watch("adHocAccountNumber");
    const routingNumber = watch("adHocRoutingNumber");
    const amount = watch("adHocAmount");
    const sellerInvoiceNumberAH = watch("sellerInvoiceNumberAH");
    const statementDescriptor = watch("adHocStatementDescriptor");
    const statementEmailId = watch("adHocStatementEmailId");
    const internalNote = watch("adHocInternalNote");
    const selectedSeller = watch("selectedSeller");
    const createNewAccount = watch("createNewAccount");
    const cassDisbursementRoutingNumber = watch("cassDisbursementRoutingNumber");
    const cassDisbursementAccountNumber = watch("cassDisbursementAccountNumber");
    const companyName = watch("companyName")?.trim();

    const maskedAccountNumber = maskData(accountNumber, "x", 4);
    const maskedRoutingNumber = maskData(routingNumber, "x", 4);
    const maskedCassDisbursementAccountNumber = maskData(cassDisbursementAccountNumber, "x", 4);
    const maskedCassDisbursementRoutingNumber = maskData(cassDisbursementRoutingNumber, "x", 4);

    const disableGetData = !accountNumber ||  !(routingNumber?.length >= 9) || !statementDescriptor;
    const disableSaveData = !+amount || !selectedSeller;
    const [canUpdateDescriptor, setCanUpdateDescriptor] = useState(true)

    const submitPayment = async () => {
        if (!createNewAccount) {
            if (!selectedSeller) {
                return;
            }
            if(statementEmailId){
                const emails = statementEmailId.split(';');
                const isValid = (statementEmailId.length === 0) ? true : emails.every((email: string) => email.trim() && isEmail(email.trim()));
                if(!isValid){
                    setError('adHocStatementEmailId', {message:'Enter valid email'}, { shouldFocus: true })
                    return;
                }
            }
            let cassUniqueId = selectedSeller.cass_unique_id;
            if (selectedSeller.is_adhoc_seller) {
                cassUniqueId = getUniqueIdentifier(CASS_SELLER_UNIQUE_IDENTIFIER_PREFIX, selectedSeller.seller_id);
            }

            await adhocCassTransaction({
                "po_number": selectedPoNumber,
                "amount": +amount,
                "seller_invoice_number": (!sellerInvoiceNumberAH || sellerInvoiceNumberAH.length === 0) ? null : sellerInvoiceNumberAH,
                "statement_descriptor": statementDescriptor,
                "to_email": (!statementEmailId || statementEmailId.length === 0) ? null : statementEmailId,
                "cass_unique_id": cassUniqueId,
                "internal_note": internalNote ?? null,
                "is_cass_default_buyer": getValues("adHocBryzosBuyer"),
            });
            getSellerPaymentSetup(selectedPoNumber);
        }
    };

    const handleCrateNewUser = async () => {
        setValue("createNewAccount", false);
        try {
            setShowLoader(true);
            const truevaultDocumentId = await saveDataInTruevault();
            const uniqueIdentifier = await creationCassSupplier();
            if (uniqueIdentifier) {
                saveAdhocSellerSetup({
                    account_number: maskedCassDisbursementAccountNumber,
                    routing_number: maskedCassDisbursementRoutingNumber,
                    truevault_document_id: truevaultDocumentId,
                    cass_unique_id: uniqueIdentifier,
                    seller_company: companyName,
                });
                setShowLoader(false);
                setNewlyCreatedUser({ cass_unique_id: uniqueIdentifier, account_number: maskedCassDisbursementAccountNumber, routing_number: maskedCassDisbursementRoutingNumber, company_name: companyName });
                setValue("adHocAccountNumber", "")
                setValue("adHocRoutingNumber", "")
                
            } else {
                setShowLoader(false);
            }
        } catch (e) {
            setShowLoader(false);
        }
        clearCreateNewUserData();
    }

    const saveDataInTruevault = async () => {
        try {
            const formData = watch();

            const accessToken = (await axios.get(import.meta.env.VITE_API_SERVICE + '/user/getAccessToken'))?.data?.data;
            const sellerPaymentData = {
                "document": {
                    "company_name": formData.companyName,
                    "user_id": null,
                    "bank_name": formData.bankName,
                    "routing_number": formData.cassDisbursementRoutingNumber,
                    "account_number": formData.cassDisbursementAccountNumber,
                    "pgpm_mapping_id": null
                }
            }

            const client = new TrueVaultClient({ accessToken });

            try {
                const documentIdFromTruevault = (await client.createDocument(import.meta.env.VITE_TRUE_VAULT_ID_SELLER_VAULT_ID, null, sellerPaymentData))?.id
                return documentIdFromTruevault;

            } catch (error) {
                console.error(error);
            }
        } catch (error) {
            console.error(error);
        }
    }

    const creationCassSupplier = async () => {
        try {
            const cassAccessToken = (await axios.get(`${import.meta.env.VITE_API_SERVICE}/user/getCassAccessToken`))?.data?.data;

            if (!cassAccessToken) {
                showPopupFormAnyComponent(cassAccessToken?.error_message ?? "Sorry unable to get cass access token!");
                return;
            }

            const uniqueIdentifier = getUniqueIdentifier(CASS_ADHOC_SELLER_UNIQUE_IDENTIFIER_PREFIX);
            if (!uniqueIdentifier) {
                return;
            }
            const obj = {
                clientKey: import.meta.env.VITE_CASS_CLIENT_KEY,
                uniqueIdentifier: uniqueIdentifier,
                masterDataType: SUPPLIER,
                masterDataName: companyName?.slice(0, 40),
                disbursementMethod: DISBURSEMENT_METHOD ?? null,
                disbursementTransactionCode: import.meta.env.VITE_CASS_DISBURSEMENT_TRANSECTION_CODE,
                disbursementRoutingNumber: cassDisbursementRoutingNumber,
                disbursementAccountNumber: cassDisbursementAccountNumber,
                refundTransactionCode: import.meta.env.VITE_CASS_REFUND_TRANSECTION_CODE,
                refundRoutingNumber: cassDisbursementRoutingNumber,
                refundAccountNumber: cassDisbursementAccountNumber,
            };

            try {
                let url = `${import.meta.env.VITE_API_CASS_SAVE_MASTER_DATA}`;
                const response = await axios.post(url, obj, {
                    headers: {
                        accept: "application/json",
                        Authorization: `Bearer ${cassAccessToken}`,
                        "Content-Type": "application/json",
                        skip: "true",
                        skipInterceptor: "true",
                    },
                });

                if (response?.data?.validationErrors?.length) {
                    showPopupFormAnyComponent(JSON.stringify(response.data.validationErrors));
                } else {
                    return response?.data?.uniqueIdentifier;
                }
            } catch (error: any) {
                if (error?.response?.data?.validationErrors?.length) {
                    showPopupFormAnyComponent(JSON.stringify(error.response.data.validationErrors));
                } else if (error?.response?.data?.errors?.length) {
                    const errors = error.response.data.errors;
                    const _erros: any = [];
                    errors.forEach((error: any) => {
                        if (error.length) {
                            error.forEach((err: any) => { _erros.push({ errorMessage: err }); });
                        }
                    })
                    showPopupFormAnyComponent(JSON.stringify(_erros));
                } else {
                    showPopupFormAnyComponent(JSON.stringify([{ errorMessage: error?.message ?? error }]));
                }
            }
        } catch (error: any) {
            showPopupFormAnyComponent(error?.message ?? error)
        }
    };

    const getUniqueIdentifier = (prefix: string, suffix: string | undefined = undefined) => {
        if (!referenceData) {
            showPopupFormAnyComponent("Sorry unable to get reference data!");
            return null;
        }

        const cassSellerUniqueIdentifierPrefix = (referenceData.ref_general_settings.find((obj: any) => obj.name === prefix))?.value;
        if (suffix) {
            return `${cassSellerUniqueIdentifierPrefix}${suffix}`;
        } else {
            const cassSellerUniqueIdentifierTagLength = (referenceData.ref_general_settings.find((obj: any) => obj.name === CASS_ADHOC_SELLER_UNIQUE_IDENTIFIER_LENGTH))?.value;
            const { randomUUID } = new ShortUniqueId({ length: cassSellerUniqueIdentifierTagLength });
            return `${cassSellerUniqueIdentifierPrefix}${randomUUID()}`;
        }
    }

    const closeCreateNewAccountPopup = () => {
        setValue("createNewAccount", false);
        clearCreateNewUserData();
    }

    const clearCreateNewUserData = () => {
        setValue("companyName", null);
        setValue("cassDisbursementRoutingNumber", null);
        setValue("cassDisbursementAccountNumber", null);
    }

    const opencreateNewAccountPopup = () => {
        setValue("cassDisbursementRoutingNumber", routingNumber);
        setValue("cassDisbursementAccountNumber", accountNumber);
        setValue("createNewAccount", true);
    }

    return (
        <div
            className={clsx(
                styles.cassCollg3,
                styles.cassColmd6,
                styles.cassColsm12,
                styles.colum,
                styles.colum5,
                styles.cassMainTable
            )}
        >
            {!!selectedPoObj?.SALES_TAX_OVERLAY && (
                <div>
                    <p>{selectedPoObj?.OVERLAY_MESSAGE}</p>
                    <button onClick={() => yesButtonClick("SALES_TAX_OVERLAY")}>
                        Yes
                    </button>
                </div>
            )}

            <div className={clsx(styles.paymentHeader, styles.paymentHeader5)}>ADHOC</div>
            <table>
                <tbody>
                <tr>
                        <td colSpan={2}>
                            <div className={styles.getUserRow}>
                                <div className={clsx(errors.selectedSeller && "error_class", 'adHocAutoComplete')}>
                                    <Autocomplete
                                        options={adhocCassSellerSetup}
                                        {...register("selectedSeller")}
                                        value={watch("selectedSeller")}
                                        isOptionEqualToValue={(option: any, value) => option.cass_unique_id === value.cass_unique_id}
                                        disabled={!selectedPoNumber}
                                        groupBy={(option) => option.is_adhoc_seller ? "Current Seller" : "All Sellers"}
                                        renderGroup={(params) => {
                                            return (
                                                <li key={params.key} className="listAdHoc">
                                                    <div className="optionGrouplbl">{params.group}</div>
                                                    <ul>{params.children}</ul>
                                                </li>
                                            );
                                        }}
                                        getOptionLabel={(option: any) =>
                                            `${option.seller_name ? `${option.seller_name} (${option.company_name})` : option.company_name} ${option.account_number} ${option.routing_number}`
                                        }
                                        renderInput={(params) => (
                                            <TextField {...params} label="Select Users" />
                                        )}
                                        classes={{
                                            root: styles.autoCompleteDesc,
                                            popper: styles.autocompleteDescPanel,
                                            paper: styles.autocompleteDescInnerPanel,
                                            listbox: clsx(styles.listAutoComletePanel1,'adHoclistAutoComletePanel1')
                                          }}
                                        onChange={(e, data: any) => {
                                            setValue("selectedSeller", data ?? null);
                                            if(data?.email){
                                                setValue("adHocStatementEmailId", data?.email ?? null);
                                            }else{
                                                setValue("adHocStatementEmailId", null);
                                            }
                                        }}
                                        renderOption={(props, option: any) => (
                                            <span {...props} key={option.id}>
                                               <div className="compnyName"><span>Company:</span> {option.seller_name ? `${option.seller_name} (${option.company_name})` : option.company_name}</div>
                                                <div className="compnyName"><span>Account #:</span> {option.account_number}</div> 
                                                <div className="compnyName"><span>Routing #:</span> {option.routing_number}</div>
                                            </span>
                                        )}
                                        filterOptions={(options, state) =>
                                            options.filter((option) =>{
                                                let searchValues = (option.seller_name)?`${option.seller_name}${option.company_name}${option.cass_unique_id}`.toLocaleLowerCase():`${option.company_name}${option.cass_unique_id}`.toLocaleLowerCase()
                                                return searchValues.includes(state.inputValue.toLocaleLowerCase())
                                            }
                                            )
                                        }
                                    />
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Account Number:</td>
                        <td className={clsx(styles.accountNumberTD,errors.adHocAccountNumber && "error_class")}>
                            <input
                                type="number"
                                onWheel={(event) => event.currentTarget.blur()}
                                min={0}
                                step="0.0001"
                                disabled={!selectedPoNumber}
                                {...register("adHocAccountNumber")}
                                onChange={(e) => {
                                    setValue("adHocAccountNumber", e.target.value?.substring(0, 17))
                                    register("adHocAccountNumber").onChange(e)
                                }}
                            />
                        </td>
                    </tr>
                    <tr>
                        <td>Routing Number:</td>
                        <td className={clsx(styles.accountNumberTD,errors.adHocRoutingNumber && "error_class")}>
                            <input
                                type="number"
                                onWheel={(event) => event.currentTarget.blur()}
                                min={0}
                                step="0.0001"
                                disabled={!selectedPoNumber}
                                {...register("adHocRoutingNumber")}
                                onChange={(e) => {
                                    setValue("adHocRoutingNumber", e.target.value?.substring(0, 9))
                                    register("adHocRoutingNumber").onChange(e)
                                }}
                            />
                        </td>
                    </tr>
                    <tr>
                        <td>Enter Payment Amount:</td>
                        <td className={clsx(errors.adHocAmount && "error_class")}>
                            <span className={clsx(styles.dFlex, styles.aligncenter)}>
                                <span>$</span>
                                <input
                                    className={styles.paymentAmountInput}
                                    type="number"
                                    onWheel={(event) => event.currentTarget.blur()}
                                    min={0}
                                    step="0.0001"
                                    disabled={!selectedPoNumber}
                                    {...register("adHocAmount")}
                                />
                                <span>USD</span>
                            </span>
                        </td>
                    </tr>
                    <tr>
                    <td>Seller Invoice Number:</td>
                    <td>
                      <span className={clsx(styles.dFlex, styles.aligncenter)}>
                        <input
                          className={styles.paymentAmountInput}
                          disabled={!selectedPoNumber}
                          {...register("sellerInvoiceNumberAH")}
                            onChange={(e)=>{
                              register("sellerInvoiceNumberAH").onChange(e);
                              canUpdateDescriptor && updateSellerAdhocBankDescriptor("adHocStatementDescriptor", e.target.value);
                            }}
                        />
                      </span>
                    </td>
                  </tr>
                    <tr>
                        <td colSpan={2} className={clsx(errors.adHocBryzosBuyer && "error_class")}>
                            <input type="checkbox"  {...register("adHocBryzosBuyer")} />
                            <label className={styles.adHocBryzosBuyerLbl}>Bryzos Buyer</label>
                        </td>
                    </tr>
                    <tr>
                        <td>Total Paid Via Adhoc</td>
                        <td><span>$ {format2DecimalPlaces(selectedPoObj?.TOTAL_PAID_TO_ADHOC)}</span></td>
                    </tr>
                    <tr>
                        <td align="center" colSpan={2}>
                            <button className={styles.createNewAccount} onClick={opencreateNewAccountPopup} disabled={!selectedPoNumber || disableGetData}>
                                Create a new account
                            </button>
                        </td>

                    </tr>
                    <tr>
                        <td colSpan={2} className={clsx(errors.adHocStatementDescriptor && "error_class")}>
                            <input
                                placeholder="Bank Statement Descriptor"
                                disabled={!selectedPoNumber}
                                {...register("adHocStatementDescriptor")}
                                onChange={(e)=>{
                                    register("adHocStatementDescriptor").onChange(e);
                                    setCanUpdateDescriptor(false);
                                }}
                            />
                        </td>
                    </tr>
                    <tr>
                        <td colSpan={2} className={clsx(styles.textareaAdHoc, errors.adHocInternalNote && "error_class")}>
                            <textarea
                                placeholder="Enter Internal Note (optional)"
                                {...register("adHocInternalNote")}
                                disabled={!selectedPoNumber}
                            ></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td colSpan={2} className={clsx(errors.adHocStatementEmailId && "error_class")}>
                            <Tooltip
                                title={errors?.adHocStatementEmailId?.message}
                                placement="top-end"
                                classes={{
                                    popper: styles.errorStyle,
                                    tooltip: styles.tooltip,
                                }}
                            >
                                <input
                                    type="text"
                                    placeholder="Email Id (multiple separate with a semicolon)"
                                    disabled={!selectedPoNumber}
                                    {...register("adHocStatementEmailId")}
                                    onChange={(e)=>{
                                        register("adHocStatementEmailId").onChange(e);
                                        clearErrors('adHocStatementEmailId')
                                    }}
                                />
                            </Tooltip>
                        </td>
                    </tr>
                    <tr>
                        <MatPopup open={!!watch("createNewAccount")} className={clsx(styles.createNewAccountMain, 'createNewAccountPopup')}>
                            <button className={styles.crossBtn} onClick={closeCreateNewAccountPopup}>x</button>
                            <h1>Create New Account</h1>
                            <table className={styles.adhocMainTable}>
                                <tbody>
                                    <tr>
                                        <td className="lblcreateNewAccount">Company Name:</td>
                                        <td className={clsx(errors.companyName && "error_class")}>
                                            <input
                                                type="text"
                                                disabled={!selectedPoNumber}
                                                {...register("companyName")}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className="lblcreateNewAccount">Disbursement Account Number:</td>
                                        <td className={clsx(errors.cassDisbursementAccountNumber && "error_class")}>
                                            <input
                                                type="number"
                                                onWheel={(event) => event.currentTarget.blur()}
                                                min={0}
                                                step="0.0001"
                                                disabled={true}
                                                {...register("cassDisbursementAccountNumber")}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td className="lblcreateNewAccount">Disbursement Routing Number:</td>
                                        <td className={clsx(errors.cassDisbursementRoutingNumber && "error_class")}>
                                            <input
                                                type="number"
                                                onWheel={(event) => event.currentTarget.blur()}
                                                min={0}
                                                step="0.0001"
                                                disabled={true}
                                                {...register("cassDisbursementRoutingNumber")}
                                                onChange={(e) => {
                                                    setValue("cassDisbursementRoutingNumber", e.target.value?.substring(0, 9))
                                                    register("cassDisbursementRoutingNumber").onChange(e)
                                                }}
                                            />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div>
                                <button className="btnCreate" onClick={handleCrateNewUser} disabled={!selectedPoNumber || !companyName || disableGetData}>Create</button>
                            </div>
                        </MatPopup>
                    </tr>
                    <tr>
                        <td align="center" colSpan={2}>
                            <button className={styles.submitBtn}
                                onClick={submitPayment}
                                disabled={disableSaveData}
                            >
                                Submit Payment
                            </button>
                        </td>

                    </tr>
                </tbody>
            </table>
        </div>
    );
};
export default AdHocPayment;
