// Dropdown styles
.Dropdownpaper {
  max-height: 300px;
  overflow-y: auto;
}

.muiMenuList {
  padding: 0;
}

// Chat container styles
.messagesContainer {
  max-height: 500px;
  overflow-y: auto;
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Individual message styles
.message {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  margin-bottom: 8px;

  &.myMessage {
    align-self: flex-end;
    align-items: flex-end;
  }

  &.othersMessage {
    align-self: flex-start;
    align-items: flex-start;
  }
}

// Message header with timestamp and actions
.messageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding: 0 8px;
}

.messageTimestamp {
  font-size: 11px;
  color: #6c757d;
}

.messageActions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message:hover .messageActions {
  opacity: 1;
}

.menuButton {
  padding: 4px !important;
  color: #6c757d !important;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04) !important;
    color: #495057 !important;
  }

  .MuiSvgIcon-root {
    font-size: 16px;
  }
}

// Message input area styles
.messageInputContainer {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 12px;
  margin-top: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.formattingToolbar {
  display: flex;
  gap: 4px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 12px;

  .MuiIconButton-root {
    padding: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      transform: translateY(-1px);
    }

    .MuiSvgIcon-root {
      font-size: 18px;
      color: #495057;
    }
  }
}

.contentEditable {
  min-height: 60px;
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background-color: #fff;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  outline: none;
  resize: none;

  &:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  &:empty::before {
    content: attr(placeholder);
    color: #6c757d;
    pointer-events: none;
  }

  // Formatting styles within the editor
  strong, b {
    font-weight: bold;
  }

  em, i {
    font-style: italic;
  }

  u {
    text-decoration: underline;
  }
}

.sendButton {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 12px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  }

  &:disabled {
    background: #6c757d;
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.6;
  }
}