import { Autocomplete, Box, ClickAwayListener, Paper, Popper, TextField, Tooltip } from "@mui/material";
import clsx from "clsx";
import styles from "../CassMappingTransaction.module.scss";
import { HTMLAttributes, useEffect, useState } from "react";
import { useImmer } from "use-immer";
import { formatCurrency } from "@bryzos/giss-ui-library";
import Loader from "../../../../components/common/Loader";

const CassMappingTransactionAutoComplete = ({ cassMappingdata, options, selectedPos, setShowSaveBtn, setPoNumber, poNumber, setInputValue, inputValue, isEdit, totalPurchase, setTableData, tableData, getPoListData, defaultProbablePoData, setDisableSubmitBtn }: any) => {
    const id = cassMappingdata.id;
    const fundingAmount = +cassMappingdata.processed_amount ? +cassMappingdata.processed_amount : 0;
    const [noOptionstext, setNoOptionstext] = useState("");
    const [probablePos, setProbablePos] = useImmer<any>([]);
    const [open, setOpen] = useState(false);
    const [clickCheck, setClickCheck] = useState(false);
    const [disableClick,setDisableClick] = useState(-1);
    useEffect(() => {
        if (options?.length) {
            setProbablePos(showCheckedOrdersOnTop(options));
        }
    }, [options])

    useEffect(() => {
        if (disableClick >= 0) {
            setProbablePos((prev: any[]) => {
                prev[disableClick].isChecked = !prev[disableClick].isChecked;
                prev[disableClick].isEdit = isEdit;
                setInputText(prev[disableClick], id);
                setInputValue({});
                return prev;
            });
        }
    }, [disableClick])

    useEffect(()=>{
        setClickCheck(false);
        setDisableClick(-1);
        setDisableSubmitBtn(false);
    },[probablePos])

    const setInputText = (data: any, id: string) => {
        let poListData = {
            id: id,
            buyer_name:data?.buyer_name,
            seller_name:data?.seller_name,
            po_number: data?.po_number ?? null,
            user_purchase_order_id: data?.user_purchase_order_id ?? null,
            transfer_type: data?.transfer_type ?? null,
            buyer_company_name: data?.buyer_company_name ?? null,
            company_name: cassMappingdata?.company_name ?? null,
            is_changed: true,
            isEdit: true,
            is_checked: !!data?.isChecked,
            buyer_total_purchase: data?.buyer_total_purchase,
            mapped_company_name: cassMappingdata?.mapped_company_name ?? null,
            processed_amount: +fundingAmount?.toFixed(2),
            auto_close_order: false,
            is_mapped_name_changed: true,
        };
        setPoNumber((prev: any) => {
            if (!prev) {
                prev = {};
            }
            if (!Array.isArray(prev[id])) {
                prev[id] = []
            }
            const defaultData = defaultProbablePoData[id];

            if (poListData.is_checked) {
                setShowSaveBtn(false);
                let obj = prev[id].find((obj: any) => obj.po_number === poListData.po_number);
                /* in edit mode after user unselect po, we are not removing from object, 
                so check if not presetn then add otherwise update checked value */

                if (obj) {
                    obj.is_checked = true;
                } else {
                    prev[id] = [...prev[id], poListData];
                }
            } else {
                if (isEdit) {
                    // in edit mode, do not remove those row, who has no po selected, so then we can send this row to backend to remove mapping from this row
                    const obj = prev[id].find((obj: any) => obj.po_number === poListData.po_number);
                    if (obj) {
                        obj.is_checked = false;
                    }
                } else {
                    prev[id].forEach((obj: any, i: number) => {
                        if(obj.po_number === poListData.po_number){
                            const poData = getPoListData?.find((getPoData: { po_number: any; }) => getPoData.po_number === poListData.po_number);
                            if(poData)  prev[id][i].buyer_total_purchase = poData.buyer_total_purchase;
                            prev[id][i].is_checked = false;
                        }
                    });
                    if (defaultData && defaultData?.mapped_orders) {
                        const mappedOrdersData = JSON.parse(defaultData.mapped_orders);
                        if (mappedOrdersData.length > 0 && prev[id].length === 0) {
                            setShowSaveBtn(true)
                        }
                    }
                }
            }
            
            if (defaultData) {
                const selectedPoArray:any = [];
                const defaultPoArray:any = [];
                const set = new Set();
                const mappedOrdersData = defaultData?.mapped_orders ? JSON.parse(defaultData.mapped_orders) : [];
                mappedOrdersData.forEach((data) => {
                    defaultPoArray.push(data.po_number);
                });
                for (let i = 0; i < prev[id].length; i++) {

                    const obj = prev[id][i];
                    if(obj.is_checked){
                        selectedPoArray.push(obj.po_number);
                    }
                }
                const obj = prev[id].findIndex((obj: any) => obj.po_number === poListData.po_number);
                let isChangedValue = true;
                if(arraysAreEqual(selectedPoArray, defaultPoArray)){
                    isChangedValue = false;
                }
                const defaultPoPriceMapping: any = {};
                mappedOrdersData.forEach((order) => {
                    if(order){
                        defaultPoPriceMapping[order.po_number] = order.buyer_po_total_purchase;
                    }
                });
                prev[id].forEach((prevData) => {
                    if(Object.keys(defaultPoPriceMapping).length !== 0 && defaultPoPriceMapping[prevData.po_number] && prevData.buyer_total_purchase && defaultPoPriceMapping[prevData.po_number] !== prevData.buyer_total_purchase){
                        isChangedValue = true;
                    }
                    prevData.is_changed = isChangedValue
                });
            }
            const mappedData: any[] = [];
            prev[id].forEach(data => {
                const obj = {
                    "user_purchase_order_id": data.user_purchase_order_id,
                    "po_number": data.po_number,
                    "is_checked": data.is_checked,
                    "auto_close_order": data.auto_close_order,
                    "buyer_po_total_purchase": data.buyer_total_purchase,
                    "is_changed": data.is_changed
                }
                mappedData.push(obj);
            });
            const updateTableData = [...tableData];
            tableData.forEach((data: any, i: number) => {
                if(data.id === id){
                    updateTableData[i].mapped_orders = JSON.stringify(mappedData);
                }
            });
            setTableData(updateTableData);

            if (!prev[id]?.length) {
                delete prev[id];
            }
            return prev;
        })
    };

    function arraysAreEqual(arr1: any[], arr2: any[]) {
        if (arr1.length !== arr2.length) {
          return false;
        }
        // Sort both arrays
        const sortedArr1 = arr1.slice().sort();
        const sortedArr2 = arr2.slice().sort();
        // Compare sorted arrays
        for (let i = 0; i < sortedArr1.length; i++) {
          if (sortedArr1[i] !== sortedArr2[i]) {
            return false;
          }
        }
        return true;
    }

    function filterOptions(options: any[], state: any): any[] {
        if (state.inputValue.length < 3 && state.inputValue !== '') {
            setNoOptionstext("Enter 3 or more characters")
            return [];
        }
        setNoOptionstext("No Options")
        return options.filter(option =>
            (`${option.po_number}${option?.transfer_type ? ` (${option.transfer_type})` : ''}${option?.buyer_company_name ? ` (${option.buyer_company_name})` : ''}`).toLowerCase().includes(state.inputValue.toLowerCase())
        );
    }

    const showCheckedOrdersOnTop = (options: any[]) => {
        if (options?.length) {
            const checkedOrders: any[] = [];
            const unCheckedOrders: any[] = [];

            const selectedPosArr: { row_id: string, po_number: string, user_purchase_order_id: string }[] = [];

            Object.keys(poNumber ?? {}).forEach(key => {
                if (poNumber?.[key]?.length) {

                    poNumber[key].forEach((obj: any) => {
                        if (obj.is_checked) {
                            selectedPosArr.push({ row_id: key, po_number: obj.po_number, user_purchase_order_id: obj.user_purchase_order_id });
                        }
                    });
                }
            });
            options.forEach((option) => {
                let foundOption = null;

                foundOption = selectedPosArr.find(obj => id === obj.row_id && obj.po_number === option.po_number);

                if (foundOption) {
                    checkedOrders.push({ ...option, isChecked: foundOption?.row_id === id });
                } else {
                    unCheckedOrders.push(option);
                }
            });
            return [...checkedOrders, ...unCheckedOrders];
        }
        return [];
    }

    const checkBoxChangeHandler = (option: any) => {
        if(clickCheck){
            return;
        }
        const i = probablePos.findIndex((obj: any) => obj.po_number === option.po_number);
        setClickCheck(true);
        setDisableClick(i);
        setDisableSubmitBtn(true);
    }

    const handleClick = () => {
        setOpen(true);
    }

    const handleClickAway = () => {
        setOpen(false);
    }
    const CustomPaperComponent = (props: HTMLAttributes<HTMLElement>) => {
        return (
            <Paper elevation={4} {...props}>
                {props.children}
                <span className="cassDataTotal">Total = {totalPurchase}</span>
            </Paper>
        );
    }

    return (
            <Box sx={{ position: "relative" }}>
                {!open ? (
                    <button className={clsx(styles.cassMapptingDropdownBtn,styles.minWidth2)} type="button" onClick={handleClick}>
                        <span className={selectedPos?.length ? styles.dataSelected : styles.placeholder}>{selectedPos?.length ? selectedPos.map((obj: any) => obj.po_number).join(", ") : "Choose"}</span>
                        <span className={styles.arrowIcon}>
                            <svg focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDropDownIcon">
                                <path d="M7 10l5 5 5-5z"></path></svg>
                            <span></span>
                        </span>
                    </button>
                ) : (
                    open && (
                        <>
                            <Autocomplete
                                open={open}
                                className={clsx(styles.selectDropdown, 'cassMappingDropdown')}
                                noOptionsText={noOptionstext}
                                options={showCheckedOrdersOnTop(probablePos)}
                                getOptionLabel={(option: any) => option.po_number}
                                onBlur={handleClickAway}
                                renderOption={(props, option: any, {index}) => (
                                    <div key={index}>
                                    <span {...props} key={option.po_number + (option.user_purchase_order_id ?? "")} onClick={() => {checkBoxChangeHandler(option, index) }}>
                                        <span>
                                            <input type="checkbox" checked={!!option.isChecked} onChange={(e) => {  }} />
                                        </span>
                                        <span>
                                            <div className="compnyName"><span>PO Number:</span> {option.po_number}</div>
                                            {option?.transfer_type && <div className="compnyName"><span>Transfer Type #:</span> {option.transfer_type}</div>}
                                            {option?.buyer_company_name && <div className="compnyName"><span>Company Name #:</span> {option.buyer_company_name}</div>}
                                            <div className="compnyName"><span>Buyer Total</span> {formatCurrency(option.buyer_total_purchase)}</div>
                                        </span>
                                    </span>
                                    </div>
                                )}
                                inputValue={inputValue?.[id] ? inputValue[id] : ''}
                                renderInput={(params) => (
                                    <Tooltip title={poNumber?.[id] ? `${poNumber[id].po_number ? poNumber[id].po_number : ''}${poNumber[id]?.transfer_type ? ` (${poNumber[id].transfer_type})` : ''}${poNumber[id]?.buyer_company_name ? ` (${poNumber[id].buyer_company_name})` : ''}` : ''}
                                        placement='bottom-start'
                                        classes={{
                                            popper: styles.cassAutocompleteTooltipStyle,
                                            tooltip: styles.tooltip,
                                        }}
                                    >
                                        <TextField {...params} autoFocus={true} placeholder="Choose One" />
                                    </Tooltip>
                                )}
                                onChange={(e, data: any) => {
                                    setInputText(data, id)
                                    setInputValue({});
                                }}
                                onInputChange={(e, data) => {
                                    if (data !== 'undefined') {
                                        setInputValue((prev: any) => {
                                            prev[id] = data;
                                            return prev;
                                        });
                                    }
                                }}
                                classes={{
                                    root: styles.autoCompleteDesc,
                                    popper: styles.autocompleteDescPanel,
                                    paper: styles.autocompleteDescInnerPanel,
                                    listbox: clsx(styles.listAutoComletePanel, 'cassMappinglist'),
                                }}
                                isOptionEqualToValue={(option, value) => {
                                    return !value ? false : `${option.po_number}${option?.transfer_type ? ` (${option.transfer_type})` : ''}${option?.buyer_company_name ? ` (${option.buyer_company_name})` : ''}` === value;
                                }}
                                groupBy={(option) => option.group}
                                PaperComponent={CustomPaperComponent}
                                renderGroup={(params) => {
                                    return (
                                        <div className="" key={params.key}>
                                            {params.group === 'Probable POs' ?
                                                <p className="probablePoTxt">{params.group}</p>
                                                : (params.key != '0') &&
                                                        <hr style={{ borderTop: 'dashed 2px', width:'100%' }} />
                                            }
                                            <ul className="ulList">

                                                <span className="cassDataScrollContent">{params.children}</span>
                                            </ul>
                                        </div>
                                    );
                                }}
                                filterOptions={filterOptions}
                                PopperComponent={(props) => <CustomPopper {...props} isLoading={clickCheck} />}
                            />
                        </>
                    )
                )}
            </Box>
    );
}
export default CassMappingTransactionAutoComplete
const CustomPopper = (props) => {
    const { isLoading, ...other } = props;
    return (
        <Popper {...other} style={{ position: 'relative' }}>
                {props.children}
                {isLoading && 
                    <div className={styles.autoCompleteLoader} onClick={(ev)=> ev.stopPropagation()}>
                        <span>Please wait...</span>
                        {/* <Loader/> */}
                    </div>
                }
        </Popper>
    );
  };