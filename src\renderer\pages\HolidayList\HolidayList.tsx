import React, { useContext, useEffect, useState } from 'react';
import styles from "./HolidayList.module.scss";
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import clsx from 'clsx';
import Loader from '../../components/common/Loader';
import AddHolidayListPopup from './AddHolidayListPopup/AddHolidayList';
import useGetAllHolidayCalendar from '../../hooks/useGetAllHolidayCalendar';
import usePostSaveHolidayCalendar from '../../hooks/usePostSaveHolidayCalendar';
import { CommonCtx } from '../AppContainer';
import usePostRemoveHolidays from '../../hooks/usePostRemoveHolidays';
import ConfiramtionBox from '../../components/common/ConfiramtionBox';
import dayjs from 'dayjs';
import { Tooltip } from '@mui/material';
import MatPopup from '../../components/common/MatPopup';
import PreviewPopup from './PreviewPopup/PreviewPopup';

const HolidayList = () => {
    const {
        data: allHolidayDates,
        isLoading: isAllHolidayDatesLoading,
        isFetching: isAllHolidayDatesFetching,
    } = useGetAllHolidayCalendar();
    
    const {
        mutateAsync: saveHolidayCalendar,
        data: saveHolidayCalendarData,
        isLoading: isSaveHolidayCalendarLoading,
    } = usePostSaveHolidayCalendar();
    
    const {
        mutateAsync: removeHolidays,
        data: removeHolidaysData,
        isLoading: isRemoveHolidaysLoading,
    } = usePostRemoveHolidays();
    
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const [rowData, setRowData] = useState([]);
    const [showConfirmationPopup, setShowConfirmationPopup] = useState<boolean>(false);
    const [removeHolidayId, setRemoveHolidayId] = useState<string|null>(null);
    const [showPreviewPopup, setShowPreviewPopup] = useState<boolean>(false);

    useEffect(()=>{
        if (allHolidayDates) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const upcomingHolidays = [];
            const pastHolidays = [];

            allHolidayDates.forEach(holiday => {
                const holidayDate = new Date(holiday.holiday_date);
                holidayDate.setHours(0, 0, 0, 0); 

                if (holidayDate >= today) {
                    upcomingHolidays.push(holiday);
                } else {
                    pastHolidays.push(holiday);
                }
            });

            upcomingHolidays.sort((a, b) => new Date(a.holiday_date) - new Date(b.holiday_date));
            pastHolidays.sort((a, b) =>  new Date(b.holiday_date) - new Date(a.holiday_date));
            setRowData([...upcomingHolidays, ...pastHolidays])
        }
    },[allHolidayDates])

    useEffect(() => {
        if(saveHolidayCalendarData){
            showPopupFormAnyComponent(saveHolidayCalendarData);
        }
    },[saveHolidayCalendarData])

    useEffect(() => {
        if(removeHolidaysData){
            showPopupFormAnyComponent(removeHolidaysData);
        }
    },[removeHolidaysData])

    const deleteRow = (id) => {
        setShowConfirmationPopup(true);
        setRemoveHolidayId(id);
    }

    const columnDefs = [
        {
            headerName: 'List No',
            valueGetter: (params: any) => params.node.rowIndex + 1,
            sortable: false,
            minWidth: 100,
            cellClass: 'holidayListCell',
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Description',
            field: 'description',
            sortable: false,
            minWidth: 271,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader',
            cellRenderer: (props: any) => {
                return (
                    <div>
                        <Tooltip title={props?.data?.description}
                         classes={{
                            tooltip: styles.holidayTooltip,
                        }}
                        >
                            <p >
                                {props?.data?.description}
                            </p >
                        </Tooltip>
                    </div>
                );
            }
        },
        {
            headerName: 'Date',
            valueGetter: (params: any) => params.data?.holiday_date ? dayjs(params.data.holiday_date).format('MM/DD/YY') : '',
            sortable: false,
            minWidth: 170,
            flex: 1,
            cellClass: 'holidayListCell',
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Day',
            valueGetter: (params: any) => params.data?.holiday_date ? dayjs(params.data.holiday_date).format('dddd') : '',
            sortable: false,
            minWidth: 150,
            flex: 1,
            cellClass: 'holidayListCell',
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Days To Skip',
            valueGetter: (params: any) => params.data?.days_to_skip,
            sortable: false,
            minWidth: 170,
            flex: 1,
            cellClass: 'holidayListCell',
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Start Time',
            valueGetter: (params: any) => params.data?.holiday_start_time ? dayjs(params.data.holiday_start_time, "HH:mm:ss").format("hh A") : '',
            sortable: false,
            minWidth: 150,
            flex: 1,
            cellClass: 'holidayListCell',
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Holiday Status',
            valueGetter: (params: any) => {
                let holidayStatus = 'Holiday';
                if(params.data?.is_day_before) holidayStatus = 'Day before holiday'; 
                if(params.data?.is_day_after) holidayStatus = 'Day after holiday'; 
                return holidayStatus
            },
            sortable: false,
            minWidth: 170,
            flex: 1,
            cellClass: 'holidayListCell',
            headerClass: 'holidayListHeader'
        },
        {
            headerName:"Added By",
            field: 'added_by',
            sortable: false,
            minWidth: 300,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader',
            cellRenderer: (props: any) => {
                return (
                    <div>
                        <Tooltip title={props?.data?.added_by}
                         classes={{
                            tooltip: styles.holidayTooltip,
                        }}
                        >
                            <p >
                                {props?.data?.added_by}
                            </p >
                        </Tooltip>
                    </div>
                );
            }
        },
        {
            headerName: '',
            cellClass: 'holidayListCell',
            headerClass: 'holidayListHeader',
            cellRendererFramework: (params) => {
                return isPassedDate(params.data?.holiday_date) ? null : (
                    <button className={styles.delBtnTbl} onClick={() => deleteRow(params.data.id)}>Delete</button>
                );
            },
        }
    ];

    const isPassedDate = (date)=>{
        const todayCT = dayjs().startOf('day');
        return dayjs(date).startOf('day').isBefore(todayCT)
    }

    const getRowStyle = (params) => {
        if (isPassedDate(params.data?.holiday_date)) {
            return { opacity: 0.5 };  
        }
    
        return null;
    };
    
    const confirmationYes = () => {
        if(removeHolidayId){
            const payload = [removeHolidayId];
            removeHolidays(payload);
        }
        confirmationNo();
    }

    const confirmationNo = () => {
        setShowConfirmationPopup(false);
        setRemoveHolidayId(null);
    }

    const defaultColDef = {
        lockVisible: true
    };

    return (
        <div className={styles.holidayListMain}>
            {isAllHolidayDatesLoading || isAllHolidayDatesFetching || isSaveHolidayCalendarLoading || isRemoveHolidaysLoading ? (
                <div className={styles.noDataFound}>
                    <Loader />
                </div>
             ) :
             (
                <>
                <div className={styles.addHolidayBtnSection}>
                    <div>
                        <h3>Holiday List</h3>
                        <p>Note: The holiday list will impact delivery dates. If a scheduled delivery falls on a holiday or within a holiday period, it will be moved to the next non-holiday weekday.</p>
                    </div>
                    <div className={styles.btnSectionTop}>
                        <div className={styles.addBtnPopup}>
                          <button className={styles.addPopupBtn} onClick={()=>{setShowPreviewPopup(true)}} >Delivery Date Preview</button>
                        </div>
                        <div className={styles.addBtnPopup}>
                          <AddHolidayListPopup saveData={saveHolidayCalendar} addedHolidays={rowData} />
                        </div>
                    </div>
                   
                </div>
                <div className={clsx(styles.ag_theme_quartz, styles.agGridAdmin)} style={{
                    height: "calc(100vh - 250px)",
                    width: "100%",
                    minHeight: "400px",
                }}>
                    <AgGridReact
                        rowData={rowData}
                        columnDefs={columnDefs}
                        rowHeight={50}
                        headerHeight={32}
                        defaultColDef={defaultColDef}
                        getRowStyle={getRowStyle}
                        enableCellTextSelection={true}
                        suppressCellFocus={true}
                    />
                </div>
                </>
             )}
            <ConfiramtionBox
                openConfirmationPopup={showConfirmationPopup}
                confirmationYes={confirmationYes}
                confirmationNo={confirmationNo}
            />

            <MatPopup
                open={showPreviewPopup}
                classes={{
                    paper: styles.previewPopup,
                }}
            >
                <PreviewPopup setShowPreviewPopup={setShowPreviewPopup} />
            </MatPopup>
        </div>
    );
};

export default HolidayList;
