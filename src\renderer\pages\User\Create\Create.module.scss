.loginPage {
  border: 3px solid #f1f1f1;
  padding: 24px;
  width: 100%;
  max-width: 600px;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 7px;
  @media (max-width:767px) {
    padding:16px 12px;
  }

  .InputFieldcss {
    width: 60%;
    height: 40px;
    padding: 10px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    margin: 15px;

    @media (max-width: 767px) {
      margin:0px 0px 12px 0px;
    }

    &.MuiInputBase-root {
      &:hover {
        .MuiSelect-select {
          border-color: transparent;
        }

        fieldset {
          border-color: #397aff;
          border-width: 1px;
        }
      }

      &.Mui-focused {
        fieldset {
          border-color: #397aff;
          border-width: 1px;
          background-color: rgba(57, 122, 255, 0.1);
        }

        .MuiSelect-select {
          color: #397aff;
        }
      }
    }
  }

  .InputFieldPass {
    width: 60%;
    height: 40px;
    padding: 10px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    margin: 15px;
    margin-left: 55px;
  }

  label {

    font-size: 16px;
    font-weight: 600;
  }

  .loginBtn {
    width: 70px;
    height: 35px;
    color: #fff;
    background-color: var(--primaryColor);
    border-color: var(--primaryColor);
    border-radius: 5px;
    cursor: pointer;
  }

  .yesAndnoBtn {
    display: flex;
    gap: 5px;
    @media (max-width: 767px) {
      gap: 8px;
    }
    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      @media (max-width: 767px) {
        font-size: 14px;
        height: 40px;
      }
    }
  }
}

.inputFiledLoginPass {
  display: flex;
  width: 100%;
  align-items: center;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    display: block;
  }

  input {
    width: 300px;
  }

  .InputFieldcss {
    width: 320px;
    padding: 10px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    &.inputEmail{
      &:read-only{
        opacity: 0.5;
        cursor: not-allowed;
        &:focus{
          outline: none;
        }
      }
    }
   

    @media screen and (max-width: 768px) and (min-width: 320px) {
      // display: block;
      width: 100%;
      margin-left: 0px;
    }
  }

  .emailText {
    width: 200px;
    font-size: 16px;
    font-weight: 600;
    @media (max-width: 767px) {
      font-size: 14px;
      width: 100%;
      max-width: 100%;
    }
  }

  .loginBtn {
    width: 64px;
    height: 37px;
  }

  .yesAndnoBtn {
    display: flex;
    gap: 5px;

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
    }
  }
}

.approveRejectPopup {
  h2 {
    display: none;
  }

  .successfullyPop {
    padding: 20px;
    text-align: center;
    width: 300px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 240px;
    }

    .successfullytext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);
    }

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
    }

  }
}

.passCode {
  display: flex;
  gap: 15px;
  padding-left: 11px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    padding: 0px;
  }

  input {
    min-width: 41px;
    height: 34px;
    font-weight: 400;
    // line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 20px;
    border-radius: 7px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 100%;
      min-width: 12%;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    &::-webkit-inner-spin-button {
      display: none;
    }
  }
}

.errorStyle.errorStyle {
  .tooltip.tooltip {
    background-color: #ff5d47;
    color: #fff;
    font-family: Noto Sans;
    font-size: 10px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    text-transform: capitalize;
    margin-bottom: 5px;
  }

}

.emailNotvalidPopup {
  h2 {
    display: none;
  }

  .emailNotvalidbox {
    padding: 20px;
    text-align: center;
    width: 300px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 240px;
    }

    .emailText {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);

      @media screen and (max-width: 768px) and (min-width: 320px) {
        font-size: 16px;
      }
    }

    .yesAndnoBtn {
      display: flex;
      gap: 5px;

      .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;


      }
    }
  }
}

.loaderImg {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  margin-top: 200px;
}

.continuePopup1{
  padding: 0px 20px;
  @media (max-width:767px) {
    padding: 0px 12px;
  }
}

.editUserText {
  width: 100%;
  height: 40px;
  padding: 6px 10px;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-size: 16px;
  color: #495057;
  display: flex;
  align-items: center;
  flex: 1;
  opacity: 0.6;
  cursor: not-allowed;
  @media (max-width:767px) {
   font-size: 14px;
  }
}

.externalApiAdminDiv{
  display: flex;
    width: 100%;
    align-items: center;
  input[type="checkbox"] {
    width: 20px;
    height: 20px;
    cursor: pointer;
    accent-color: #007bff; /* Changes the checkbox color */
  }
  
}


.inputFiledPassword {
  display: flex;
  height: 40px;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #ced4da;
  margin-left: 15px;
  &:focus-within {
    border: 1px solid #000;
  }

  .inputFiledPasswordContainer{
    height: 100%;
  }
  .pass{
      width: 280px !important;
      border-top-right-radius: 0px !important;
      border-bottom-right-radius: 0px !important;
  }
  .showHidePass {
    width: 40px;
    height: 38px;
    border:none;
    cursor: pointer;
    outline: none;
    padding: 0;
    margin: 0;
    border-radius: 4px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-left: 1px solid #ced4da;
  }

  @media screen and (max-width: 768px) and (min-width: 320px) {
      display: block;
      margin-bottom: 10px;

  }

  .InputFieldcss {
      width: 320px;
      height: 100%;
      padding-left: 20px;
      font-weight: 400;
      line-height: 1.5;
      color: #495057;
      background-color: #fff;
      border-radius: 4px;
      font-size: 15px;
      margin: 0;
      border: none;

      @media screen and (max-width: 768px) and (min-width: 320px) {
          width: 265px;

      }

      &:focus {
         outline: none;
      }
  }

  .emailText {
      width: 200px;
      font-size: 16px;
      font-weight: 600;

      @media screen and (max-width: 768px) and (min-width: 320px) {
          margin-bottom: 10px;

      }
  }
}