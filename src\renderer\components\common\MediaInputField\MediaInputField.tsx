
import styles from "./MediaInputField.module.scss";
import { ReactComponent as UploadImage } from '../../../../assests/images/icon-Upload-Files.svg';
import useMediaDropzone from "../../../hooks/useMediaDropzone";
import { useEffect } from "react";
import { ReactComponent as CloseArrow } from '../../../../assests/images/closePop.svg';
import { ReactComponent as WarningIcon } from '../../../../assests/images/Warning.svg';
const MediaInputField = ({ width, height, type, setValue, name, watch, clearMediaData, setHasFieldValuesChanged }: any) => {
    const { dropzoneProps, media, error, setError, setMedia} = useMediaDropzone(width, height, type);

    useEffect(() => {
        if (media) {
            setValue(name, media, { shouldValidate: true, shouldDirty: true });
            if(setHasFieldValuesChanged) setHasFieldValuesChanged(true)
        }
    }, [media])

    useEffect(() => {
        if(watch(name)){
            getImageDimensions(watch(name))
        }
    },[watch(name)])

    const clearData = () => {
        setError('');
        setMedia(null);
        clearMediaData(name)
    }

    function getImageDimensions(url: any) {
        const img = new Image();
        img.onload = function () {
            if (!(img.width === width && img.height === height)) {
                setError("Current Resolution: " +img.width+" x " + img.height+ "<br>Recommended Resolution: "+ width + " x "+ height);
            }
        };
        img.onerror = function () {
            console.error("Failed to load image");
        };
        img.src = url;
    }

    return (
        <>
        {!watch(name) ? (
            <div className={styles.uploadBox} {...dropzoneProps.getRootProps()}>
                <UploadImage />
                <p className={styles.uploadHeading}>Drag and Drop</p>
                <p className={styles.uploadText}>Drag and drop your files anywhere or</p>
                <div className={styles.continuePopBtn}>
                    <button className={styles.continueBtn} onClick={dropzoneProps.open}>Click here to browse</button>
                </div>
                <input {...dropzoneProps.getInputProps()} multiple={false} />
            </div>
        ) : (
            <>
                <div className={styles.uploadBoxImage}>
                    <div className={styles.closeIcon} onClick={() => clearData()}>
                        <CloseArrow />
                    </div>
                    <img src={watch(name)} width={'50%'} height={'50%'} alt="" />
                </div>
                {error && <div className={styles.mediaUploadWarningMsgDiv}><WarningIcon /> <span className={styles.mediaWarningMsg} dangerouslySetInnerHTML={{__html:error}}></span></div>}
            </>
          )}
        </>
    )
}

export default MediaInputField;