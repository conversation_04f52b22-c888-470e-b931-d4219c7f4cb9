
import { useCallback, useContext, useEffect, useRef, useState } from "react";
import { CommonCtx } from "../../AppContainer";
import styles from "../VideoUploads.module.scss";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { VideoEditFromSchema, VideoEditFromSchemaType } from "../../../models/videoEdit.model";
import { useDropzone } from "react-dropzone";
import clsx from "clsx";
import { ReactComponent as UploadImage } from '../../../../assests/images/icon-Upload-Files.svg';
import { ReactComponent as CloseArrow } from '../../../../assests/images/closePop.svg';
import { uploadFileAndGetS3Url } from "@bryzos/giss-ui-library";
import Modal  from "./Modal";
import EditTagsAutoComplete from "./EditTagsAutoComplete";
import useGetVideoTags from "../../../hooks/useGetVideoTags";
import VideoPlayer from "../../../components/VideoPlayer";
import MatPopup from "../../../components/common/MatPopup";
import MultipleFilesSelection from "./MultipleFilesSelection";
import { defaultThumbnailFiles } from "../../../utils/constant";
import useGetAllVideoLibraryTags from "../../../hooks/useGetAllVideoLibraryTags";
import useGetAllVideoLibraryInternalTags from "../../../hooks/useGetAllVideoLibraryInternalTags";


const EditVideo = ({ confirmationPopupClose, updateVideo, setShowLoader, videoData, videoTags, setUploadVideoLoaderText , tagMap}: any) => {
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        getValues,
        formState: { isDirty, isValid, errors },
    } = useForm<VideoEditFromSchemaType>({
        defaultValues: videoData,
        resolver: yupResolver(VideoEditFromSchema),
        mode: "onSubmit",
    });

    const {
        data: videoLibraryTagsData,
    } = useGetAllVideoLibraryTags();

    const {
        data: videoLibraryInternalTagsData,
    } = useGetAllVideoLibraryInternalTags();

    const [acceptedFile, setAcceptedFile] = useState<any>("");
    const [file, setFile] = useState(null);
    const videoRef = useRef(null);
    const [snapShots, setSnapshots] = useState<any>([]);
    const [videoURL, setVideoURL] = useState<string | undefined>(undefined);
    const [open, setOpen] = useState(false);
    const [disableCaptureThumbnail, setDisableCaptureThumbnail] = useState(true);
    const [hasFieldValuesChanged, setHasFieldValuesChanged] = useState(false);
    const [associatedTagsData, setAssociatedTagsData] = useState<any>([]);
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const {data: associatedTags, error: associatedTagsError , isLoading: associatedTagsIsLoading} = useGetVideoTags(videoURL)
    const [isPlayingPrev , setIsPlayingPrev] = useState(true);
    const [openThumbnailSection, setOpenThumbnailSection] = useState(false);
    const [isFilledAllThumbnails, setIsFilledAllThumbnails] = useState<boolean>(false);
    const [isEdit, setIsEdit] = useState<boolean>(true);
    const [openConfirmationPopup, setOpenConfirmationPopup] = useState<boolean>(false);
    const [captionFile , setCaptionFile] = useState(undefined);
    const [havingBlob  , setHavingBlob] = useState(false);
    const captionRef = useRef(null);

    useEffect(() => {
        if (file) {
            setValue('videoFile', file, { shouldValidate: true, shouldDirty: true });
        }
    }, [file]);


    useEffect(()=>{
        if(videoData){
            setVideoURL(videoData.video_s3_url);
        }
    }, [videoData]);

    useEffect( () => {
        if(captionFile) {
            setValue('subtitleFile', captionFile , { shouldValidate: true });
        } else {
            setValue('subtitleFile', undefined , { shouldValidate: true });
        }
    }, [captionFile])

    useEffect(() => {
        if(associatedTags){
            setAssociatedTagsData(associatedTags?.data);
        }
    },[associatedTags])
    
    useEffect(() => {
        if (watch('thumbnailFiles.thumbnail_app') && watch('thumbnailFiles.thumbnail_safe') && watch('thumbnailFiles.intro_desktop') && watch('thumbnailFiles.electron_player') && watch('thumbnailFiles.intro_mobile') && watch('thumbnailFiles.intro_tablet')) {
            setIsFilledAllThumbnails(true);
        } else {
            setIsFilledAllThumbnails(false);
        }
    }, [watch('thumbnailFiles.thumbnail_app'), watch('thumbnailFiles.thumbnail_safe'), watch('thumbnailFiles.intro_desktop'), watch('thumbnailFiles.electron_player'), watch('thumbnailFiles.intro_mobile'), watch('thumbnailFiles.intro_tablet')])

    function uploadedVideoisVideo(item: { actual_filename: any; }) {
        // List of supported video file extensions
        const videoExtensions = ['.mp4', '.mov', '.wmv', '.flv'];
        // Extract the file extension from the filename and check if it's in the list
        const extension = item.actual_filename.split('.').pop().toLowerCase();
        return videoExtensions.includes(`.${extension}`);
    }

    const onDrop1 = useCallback((acceptedFiles: any) => {
        if (acceptedFiles.length) {
            const file = acceptedFiles[0];
            const ext = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();

            const allowedExtensions = ['.mp4', '.mov', '.wmv', '.flv', '.avi'];
            if (allowedExtensions.indexOf(ext) === -1) {
                setShowLoader(false);
                showPopupFormAnyComponent(`${file.name} is not supported.`, 'Ok');
                return;
            }

            setFile(file);

            if(file.size / Math.pow(10,9) >= 3.9) {
                setValue('is_large_file' , true , { shouldValidate: true });
            } else {
               setValue('is_large_file' , false , { shouldValidate: true });
            }

            setHasFieldValuesChanged(true);

            const isVideoType = uploadedVideoisVideo({ actual_filename: file.name });
            if (isVideoType) {
                const videoFileUrl = window.URL.createObjectURL(file);
                setAcceptedFile(videoFileUrl);
            } else {
                const reader = new FileReader();
                reader.readAsDataURL(acceptedFiles[0]);
                reader.onload = (e) => {
                    if (e?.target)
                        setAcceptedFile(e.target.result);
                };
            }
        }
    }, []);

    const onDrop2 = useCallback((acceptedFiles: any) => {
        if (acceptedFiles.length) {
            const file = acceptedFiles[0];
            const ext = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();

            const allowedExtensions = ['.png', '.jpeg', '.jpg', '.webp', '.heif'];
            if (allowedExtensions.indexOf(ext) === -1) {
                setShowLoader(false);
                showPopupFormAnyComponent(`${file.name} is not supported.`, 'Ok');
                return;
            }

            setHasFieldValuesChanged(true);
            const isVideoType = uploadedVideoisVideo({ actual_filename: file.name });
            if (isVideoType) {
                const videoFileUrl = window.URL.createObjectURL(file);
                setSnapshots(videoFileUrl);
            } else {
                const reader = new FileReader();
                reader.readAsDataURL(acceptedFiles[0]);
                reader.onload = (e) => {
                    if (e?.target)
                        setSnapshots(e.target.result);
                };
            }
        }
    }, []);

    const { getRootProps: getRootProps1, getInputProps: getInputProps1, open: open1 } = useDropzone({ onDrop: onDrop1, noClick: true });
    const { getRootProps: getRootProps2, getInputProps: getInputProps2, open: open2 } = useDropzone({ onDrop: onDrop2, noClick: true });

    const clearSelectedVideo = () => {
        setAcceptedFile("");
        setFile(null);
       setVideoURL('');
       setValue('videoFile', undefined, { shouldValidate: true, shouldDirty: true });
       setValue('video_s3_url', null, { shouldValidate: true, shouldDirty: true });
       setValue('subtitleFile', undefined, { shouldValidate: true });
       setValue('subtitle_s3_url', undefined, { shouldValidate: true });
       setDisableCaptureThumbnail(true);
       setHasFieldValuesChanged(true);
       setAssociatedTagsData([]);
       setValue('is_large_file', false , { shouldValidate: true, shouldDirty: true });
       setCaptionFile(undefined);   
       if(captionRef.current) {
           captionRef.current.value = '';
       }
    };

    const clearSelectedThumbnail = () => {
        setOpenConfirmationPopup(true)
    };

    const base64ToFile = (base64: string, filename: string): File => {
        const arr = base64.split(',');
        const mime = arr[0].match(/:(.*?);/)?.[1] || '';
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], filename, { type: mime });
    };

    const scaleFactor = 0.25;

    const capture = (video: any, scaleFactor: number | null) => {
        if (scaleFactor == null) {
            scaleFactor = 1;
        }
        const w = video.videoWidth * scaleFactor;
        const h = video.videoHeight * scaleFactor;
        const canvas = document.createElement('canvas');
        canvas.width = w;
        canvas.height = h;
        const ctx = canvas.getContext('2d');
        if (ctx) ctx.drawImage(video, 0, 0, w, h);
        return canvas;
    };

    const shoot = () => {
        const video = videoRef.current;
        const canvas = capture(video, scaleFactor);

        canvas.onclick = function () {
            window.open(this.toDataURL('image/jpg'));
        };
        const prevSnapshots = [...snapShots];
        const newSnapshots = [canvas, ...prevSnapshots];
        const snapshotConvertInBase64Format = newSnapshots.slice(0, 1)[0]?.toDataURL();
        const thumbnailFilesObj = {...watch('thumbnailFiles')}
        Object.keys(thumbnailFilesObj).forEach((thumbnailKey:string) => {
         setValue('thumbnailFiles.'+thumbnailKey, snapshotConvertInBase64Format,{ shouldValidate: true })
        });
        setSnapshots(snapshotConvertInBase64Format);
    };

    const clickOnSubmitBtn = async (data:any) => {
        try {
            confirmationPopupClose();
            setShowLoader(true);
            const videoFile = watch("videoFile");
            const thumbnailFile = watch("thumbnailFile");
            const subtitleFile = watch("subtitleFile");
            if (videoFile) {
                setUploadVideoLoaderText(true);
                const videoS3Url = await uploadFileAndGetS3Url(videoFile, import.meta.env.VITE_S3_UPLOAD_VIDEO_THUMBNAIL_BUCKET_NAME, '/', import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', 'video', import.meta.env.VITE_ENVIRONMENT);
                setValue('video_s3_url', videoS3Url, { shouldValidate: true });
            }

            if(data?.thumbnailFiles){
                setUploadVideoLoaderText(true); 
                const thumbnailS3UrlObj: any = {};
                for (const thumbnailFileKey of Object.keys(data.thumbnailFiles)) {
                    if(data?.thumbnailFiles?.[thumbnailFileKey] === data?.thumbnail_s3_url?.[thumbnailFileKey]){
                        thumbnailS3UrlObj[thumbnailFileKey] = data.thumbnail_s3_url[thumbnailFileKey];
                    }else{
                        const base64Thumbnail = data.thumbnailFiles[thumbnailFileKey];
                        const matches = base64Thumbnail.match(/^data:image\/([a-zA-Z+]+);base64,/);
                        const fileExtension = matches?.[1] ?? 'png'; 
                        const thumbnailFormatting = base64ToFile(base64Thumbnail, 'thumbnail.'+ fileExtension);
                        const thumbnailS3Url = await uploadFileAndGetS3Url(thumbnailFormatting, import.meta.env.VITE_S3_UPLOAD_VIDEO_THUMBNAIL_BUCKET_NAME, '/', import.meta.env.VITE_API_SERVICE+'/user/get_signed_url', 'thumbnail', import.meta.env.VITE_ENVIRONMENT )
                        thumbnailS3UrlObj[thumbnailFileKey] = thumbnailS3Url;
                    }
                }
                setValue('thumbnail_s3_url', thumbnailS3UrlObj, { shouldValidate: true })
            }
            
            if(subtitleFile){
                setUploadVideoLoaderText(true); 
                const captionS3Url = await uploadFileAndGetS3Url(subtitleFile, import.meta.env.VITE_S3_UPLOAD_VIDEO_THUMBNAIL_BUCKET_NAME, '/', import.meta.env.VITE_API_SERVICE+'/user/get_signed_url', 'subtitle', import.meta.env.VITE_ENVIRONMENT )
                setValue('subtitle_s3_url', captionS3Url , { shouldValidate: true })
            }

            submitData(watch());
            setShowLoader(false);

        } catch (error) {
            console.log(error);
            showPopupFormAnyComponent(error?.message);
            setShowLoader(false);
        }
    };

    const submitData = (data:any) => {
        const showOnUi = !!data.show_on_ui ? 1 : 0;
        const isFileLarge = !!data.is_large_file ? 1 : 0;
        const internalTags = data.videoInternalTags?.length ? data.videoInternalTags.join(',') : null ;
        const payload:{data:any[]} = {
            "data": [{
                "id":videoData.id,
                "title": data.title,
                "caption": data.caption,
                "description": data.description,
                "video_s3_url": data?.video_s3_url ?? null,
                "thumbnail_s3_url": data.thumbnail_s3_url,
                "tags": data.tags,
                "show_on_ui": showOnUi,
                "video_id": data.video_id,
                "sequence": Number(Number(data.sequence).toFixed(2)),
                "share_video_url":data.share_video_url,
                "is_large_file":isFileLarge,
                "subtitle_s3_url" : data.subtitle_s3_url ?? null,
                "internal_tags" : internalTags
            }]
        };
        const originalTags:any[] = associatedTagsData.length>0?associatedTagsData:defaultTag;
        if(data.video_s3_url && data?.videoTags?.length > originalTags?.length){ 
            const newTags:string[] = data.videoTags.filter((element:any) => !originalTags.includes(element));
            newTags?.forEach(tag=>{
                payload.data.push({
                    title: data.title,
                    caption: data.caption,
                    description: data.description,
                    video_s3_url: data.video_s3_url,
                    thumbnail_s3_url: data.thumbnail_s3_url,
                    tags: tag,
                    share_video_url:data.share_video_url,
                    is_large_file:isFileLarge,
                    subtitle_s3_url : data.subtitle_s3_url,
                    "internal_tags" : internalTags
                })
            })
        }
        updateVideo(payload);
    };
    const handleClickOpen = () => {
        setIsPlayingPrev(false);
        setOpen(true);
    };

    const handleClose = () => {
        setIsPlayingPrev(true);
        setOpen(false);
    }; 

    const handleOnPlay = () => {
        setDisableCaptureThumbnail(false);
    }

    const defaultTag = [videoData?.tags];

    
   const thumbnailPopupClose = () => {
    setOpenThumbnailSection(false);
   }

    const confirmationYes = () => {
        setSnapshots([]);
        setValue('thumbnailFiles', defaultThumbnailFiles, { shouldValidate: true, shouldDirty: true });
        setValue('thumbnail_s3_url', {}, { shouldValidate: true, shouldDirty: true });
        setHasFieldValuesChanged(true);
        confirmationNo()
    }

    const confirmationNo = () => {
        setOpenConfirmationPopup(false);
    }

    const clearSelectedSubtitle = () => {
        setValue('subtitleFile', undefined, { shouldValidate: true });
        setValue('subtitle_s3_url', undefined, { shouldValidate: true });
        setHasFieldValuesChanged(true);
        setHavingBlob(true);
        setCaptionFile(undefined);   
        if(captionRef.current) {
            captionRef.current.value = '';
        } 
    } 

    const captionBlobUrl = (captionFile:any) => {
        if(!captionFile) return null;
        const subtitleBlob = new Blob([captionFile], { type: 'text/vtt' });
        const subtitleUrl = window.URL.createObjectURL(subtitleBlob);
        return subtitleUrl;
    }

    const handleSelectedSubtitle = (e : any) => {
        const vttFile = e.target.files[0];
        const ext = vttFile?.name.substring(vttFile?.name.lastIndexOf(".")).toLowerCase();
        const allowedExtensions = ['.vtt'];
      
        if (allowedExtensions.indexOf(ext) === -1) {
            setShowLoader(false)
            showPopupFormAnyComponent(`${vttFile?.name} is not supported.`, 'Ok');
            return;
        }
        
        setHasFieldValuesChanged(true);
        setCaptionFile(vttFile);
    }    

    return (
        <div className={styles.continuePopup}>
            <div className={styles.overFlowForPop}>
                <div className={styles.col}>
                    <div className={styles.inputField}>
                        <span className={styles.lblInput}>Title<span className={styles.uploadVideoNoteReq}>*</span></span>
                        <input type="text" className={styles.inputBox} {...register('title')}
                        onChange={(e)=>{
                            register('title').onChange(e)
                            setHasFieldValuesChanged(true);
                        }}
                         />
                    </div>
                    <div className={styles.inputField}>
                        <span className={styles.lblInput}>Description</span>
                        <input type="text" className={styles.inputBox} {...register('description')}
                        onChange={(e)=>{
                            register('description').onChange(e)
                            setHasFieldValuesChanged(true);
                        }}
                         />
                    </div>
                </div>
                <div className={styles.col}>
                    <div className={styles.inputField}>
                        <span className={styles.lblInput}>Caption</span>
                        <input type="text" className={styles.inputBox} {...register('caption')}
                        onChange={(e)=>{
                            register('caption').onChange(e)
                            setHasFieldValuesChanged(true);
                        }}
                         />
                    </div>
                    <div className={styles.inputField}>
                        <span className={styles.lblInput}>Tags<span className={styles.uploadVideoNoteReq}>*</span></span>
                        <EditTagsAutoComplete setValue={setValue} disabledTags={associatedTagsData.length ? associatedTagsData : defaultTag} setHasFieldValuesChanged={setHasFieldValuesChanged} disableTagSelect={(videoURL || watch("videoFile")) ? false : true}  valueName={'videoTags'} tagsData={videoLibraryTagsData} />
                    </div>
                </div>
                <div className={styles.col}>
                <div className={styles.inputField}>
                    <span className={styles.lblInput}>Share Video URL</span>
                    <input type="text" className={styles.inputBox} {...register('share_video_url')} onChange={(e)=>{
                              register('share_video_url').onChange(e)
                              setHasFieldValuesChanged(true);
                            }} />
                </div>
                <div className={styles.inputField}>
                        <span className={styles.lblInput}>Sequence <span className={styles.uploadVideoNoteReq}>*</span></span>
                        <input
                            type="number"
                            className={styles.inputBox}
                            {...register('sequence')}
                            onChange={(e)=>{
                              register('sequence').onChange(e)
                              setHasFieldValuesChanged(true);
                            }}
                        />
                    </div>
            </div>
                <div className={styles.col}>
                    <div className={styles.inputField}>
                        <span className={styles.lblInput}>Show on UI</span>
                        <input
                              className= {styles.checkbox}
                              type="checkbox"
                              {...register('show_on_ui')}
                              onChange={(e)=>{
                                register('show_on_ui').onChange(e)
                                setHasFieldValuesChanged(true);
                              }}
                        />
                    </div>
                    <div className={styles.inputField}>
                        <span className={styles.lblInput}>Internal Tags </span>
                        <EditTagsAutoComplete setValue={setValue} disabledTags={null} defaultInternalTags={videoData?.internal_tags} setHasFieldValuesChanged={setHasFieldValuesChanged} disableTagSelect={false} valueName={'videoInternalTags'} tagsData={videoLibraryInternalTagsData} />
                    </div>
                </div>
                <div className={styles.uploadVideoSection}>
                    <div className={styles.uploadVideo}>
                        <span>Video</span>
                        {(!acceptedFile && !videoURL )? (
                            <div className={styles.uploadBox} {...getRootProps1()}>
                                <UploadImage />
                                <p className={styles.uploadHeading}>Drag and Drop</p>
                                <p className={styles.uploadText1}>Drag and drop your files anywhere or</p>
                                <div className={styles.continuePopBtn}>
                                    <button className={styles.continueBtn} onClick={open1}>Click here to browse</button>
                                </div>
                                <input {...getInputProps1()} multiple={false} />
                            </div>
                        ) : (
                            <div className={styles.uploadBoxImage}>
                                <div className={clsx(styles.newCarousel, styles.popupVideoPreview)}>
                                    <div className={styles.closeIcon} onClick={clearSelectedVideo}>
                                        <CloseArrow />
                                    </div>
                                    {videoURL ?
                                        <VideoPlayer
                                            url={videoURL}
                                            width={"320px"}
                                            height={"240px"}
                                            id='videoPopup'
                                            videoRef={videoRef}
                                            crossOrigin={'anonymous'}
                                            onPlay={handleOnPlay}
                                            isPlayingPrev = {isPlayingPrev}
                                            autoPlay={false}
                                            captionBlobUrl = {captionFile ? captionBlobUrl(captionFile) : undefined}
                                            captionUrl = {watch("subtitle_s3_url")}
                                        />
                                        :
                                        <VideoPlayer
                                            url={acceptedFile}
                                            width={"320px"}
                                            height={"240px"}
                                            id='videoPopup'
                                            videoRef={videoRef}
                                            onPlay={handleOnPlay}
                                            isPlayingPrev = {isPlayingPrev}
                                            autoPlay={false}
                                            captionBlobUrl = {captionFile ? captionBlobUrl(captionFile) : undefined}
                                            captionUrl = {watch("subtitle_s3_url")}
                                        />
                                    }
                                </div>
                            </div>
                        )}
                      <div className={clsx(styles.uploadVideoNote,styles.editVideoNote)}>**Please upload videos in MP4 format. MOV files may not work on some devices.**</div>
                    </div>
                    <div className={styles.uploadVideo}>
                        <span>Thumbnail<span className={styles.uploadVideoNote}>*</span></span>
                        {(!isFilledAllThumbnails) ? (
                            <>
                                <div className={styles.thumbnailMain}>
                                    {/* <div className={styles.uploadBox} {...getRootProps2()}>
                                    <UploadImage />
                                    <p className={styles.uploadHeading}>Drag and Drop</p>
                                    <p className={styles.uploadText1}>Drag and drop your files anywhere or</p>
                                    <div className={styles.continuePopBtn}>
                                        <button className={styles.continueBtn} onClick={open2}>Click here to browse</button>
                                    </div>
                                    <input {...getInputProps2()} multiple={false} />
                                </div> */}
                                    <div className={styles.captureThumbnail}>
                                        <button onClick={() => setOpenThumbnailSection(true)}>Choose Thumbnails </button>
                                    </div>
                                    <div className={styles.captureThumbnail}>
                                        <span> OR</span>
                                        <button onClick={shoot} disabled={disableCaptureThumbnail}>Capture for Thumbnail</button>
                                    </div>
                                </div>

                            </>
                        ) : (
                            <>
                              <div className={clsx(styles.uploadBoxImage,styles.uploadBoxThumbImage)}>
                                <div className={styles.closeIcon} onClick={clearSelectedThumbnail}>
                                    <CloseArrow />
                                </div>
                                <img src={watch('thumbnailFiles.electron_player')} width={'50%'} height={'50%'} alt="" />
                            </div>
                            <div className={styles.captureThumbnail}>
                              <button onClick={() => { setOpenThumbnailSection(true); setIsEdit(true); }}>Edit Thumbnails </button>
                           </div>
                            </>
                          
                        )}
                    </div>
                </div>
            </div>
            {(watch("video_s3_url") || acceptedFile) &&
                <div className={styles.addSubTitle}>
                    <div className={styles.captionTitle}>Closed Captioning <span>(Please upload *.vtt file only.)</span></div>
                    <div>
                    {(captionFile || watch("subtitle_s3_url")) ? <div className={styles.uploadFileMain}>
                        <div>{captionFile ? captionFile.name : watch("subtitle_s3_url") ? (watch("subtitle_s3_url")?.replace("https://extended-widget-video-library.s3.amazonaws.com/local/" , "")) : <div> No file Chosen </div> }</div>
                        <div className={styles.closeIcon1} onClick={clearSelectedSubtitle}>
                            <CloseArrow />
                        </div>
                    </div> :
                        <div className={styles.uploadContainer}>
                            <label htmlFor="fileUpload" className={styles.uploadLabel}>
                            Upload File
                            </label>
                            <input type="file" id="fileUpload" className={styles.fileInput} ref = {captionRef} onChange = {handleSelectedSubtitle}/>
                        </div>
                        }
                    </div>
                </div>}  
           
            <Modal
                        open={open}
                        handleClickOpen={handleClickOpen}
                        handleClose={handleClose}
                        extraVideo={watch("video_s3_url") ? watch("video_s3_url") :  acceptedFile}
                        extraTag={watch("tags")}
                        extraThumbnail={watch("thumbnailFiles")}
                        extraTitle={watch("title")}
                        videoId = {watch("video_id")}
                        extraSequence = {watch("sequence")}
                        extraShow = {watch("show_on_ui")}
                        extraAssociatedTags = {associatedTags}
                        extraSelectedTags = {watch("videoTags")}
                        extraViews = {watch("view_count")}
                        extraDescription= {watch("description")}
                        extraSubtitle = {watch("subtitle_s3_url") ? watch("subtitle_s3_url") : captionBlobUrl(captionFile)}
                        havingBlob = {havingBlob}
                        share_video_url={watch("share_video_url")}
        ></Modal>   
             {watch("video_s3_url") || acceptedFile ? <div className={clsx(styles.col,styles.largeVideoCheckbox)}>
                    <div className={styles.inputField}>
                        <span className={styles.lblInput}>Large Video</span>
                        <input
                              className= {styles.checkbox}
                              type="checkbox"
                              {...register('is_large_file')}
                              onChange={(e)=>{
                                register('is_large_file').onChange(e)
                                setHasFieldValuesChanged(true);
                              }}
                        />
                        <span className={styles.noteLargeVideo}>(Please check this if video is larger than 3.9 GB)</span>
                    </div>
                </div> : <></> }
         <button
                    onClick={handleClickOpen}
                    className={clsx(styles.previewBtnUploadVideo)}
                    disabled={(!isValid || !hasFieldValuesChanged)}
                >
                    Preview
        </button>
            <div className={styles.yesAndnoBtn}>
                <button className={styles.okBtn} onClick={handleSubmit(clickOnSubmitBtn)} 
                 disabled={(!isValid || !hasFieldValuesChanged)}
                >
                    Submit
                </button>
                <button
                    className={styles.okBtn}
                    onClick={() => { confirmationPopupClose() }}
                >
                    Cancel
                </button>
            </div>
            <MatPopup
                className={styles.orderConfirmationPopup}
                open={openThumbnailSection}
                classes={{
                    paper:clsx(styles.uploadVideoPopup,styles.multiFilesSelectionPopup)
                }}
            >
                <div className={styles.uploadVideoTitle}>Choose Thumbnails</div>
                <MultipleFilesSelection 
                    confirmationPopupClose={thumbnailPopupClose}
                    setValue={setValue}
                    watch={watch}
                    handleSubmit={handleSubmit}
                    isFilledAllThumbnails={isFilledAllThumbnails}
                    isEdit={isEdit}
                    getValues={getValues}
                    defaultThumbnailFiles={defaultThumbnailFiles}
                    setHasFieldValuesChanged={setHasFieldValuesChanged}
                    setIsFilledAllThumbnails={setIsFilledAllThumbnails}
                />
            </MatPopup>
            
            <MatPopup
                className={styles.confirmationPopup}
                open={openConfirmationPopup}
            >
                <div className={styles.continuePopup}>
                    <p className={styles.continuetext}>Do you want to continue ?</p>
                    <div className={styles.yesAndnoBtn}>
                        <button className={styles.okBtn} onClick={confirmationYes}>
                            Yes
                        </button>
                        <button className={styles.okBtn} onClick={confirmationNo}>
                            No
                        </button>
                    </div>
                </div>
            </MatPopup>
        </div>
    );
};

export default EditVideo;
