import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

const useGetSecurityData = () => {
  return useQuery(
    [reactQueryKeys.getSecurityData],
    async () => {
      try {
        const response = await axios.get(
            import.meta.env.VITE_API_SERVICE + '/reference-data/getSecurityToken'
        );
        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "err_message" in response.data.data
          ) {
            throw new Error(response.data.data.err_message);
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error: any) {
        console.log(error);
        throw new Error(error?.message);
      }
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      staleTime: 0,
    }
  );
};

export default useGetSecurityData;
