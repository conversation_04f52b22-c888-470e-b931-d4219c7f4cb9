import { Select, MenuItem, Tooltip } from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader/Loader";
import MatPopup from "../../../components/common/MatPopup";
import useApproveRejectBnpl from "../../../hooks/useApproveRejectBnpl";
import useGetAllBnplData from "../../../hooks/useGetAllBnplData";
import useRefreshCreditLimit from "../../../hooks/useRefreshCreditLimit";
import { filterArrray, format2DecimalPlaces, toTitleCase } from "../../../utils/helper";
import styles from "././Approve.module.scss";
import * as yup from "yup";
import { startCase, rest } from "lodash-es";
import { useForm, Controller } from "react-hook-form";
import useEditBnplData from "../../../hooks/useEditBnplData";
import clsx from "clsx";
import usePostBnplUpdateStatus from "../../../hooks/usePostBnplUpdateStatus";
import { BNPL_STATUS } from "../../../utils/constant";
import useGetAdminReferenceData from "../../../hooks/useGetAdminReferenceData";

const ApproveReject = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [filteredaBnpls, setFilteredaBnpls] = useImmer([]);
  const [modifiedBnpls, setModifiedBnpls] = useImmer([]);

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [editBnplData, setEditBnplData] = useImmer<any>(null);
  const [apiResponseMessage, setApiResponseMessage] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [showApprovePopup, setShowApprovePopup] = useState(false);
  const [approvePopupPayload, setApprovePopupPayload] = useState<any>(null);
  const [showBryzosCreditData] = useState(true);
  const [reasonForEditBnpl, setReasonForEditBnpl] = useState("");
  const [openEditBnplStatusModal, setOpenEditBnplStatusModal] = useState(false);
  const [bnplStatusData, setBnplStatusData] = useImmer<any>(null);
  const [bnplStatus, setBnplStatus] = useState(BNPL_STATUS.enabled);
  const [restrictAmount, setRestrictAmount] = useState("2500");

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredaBnpls.length / itemsPerPage);
  const disableApprove = !(+approvePopupPayload?.net_terms_days && +approvePopupPayload?.auth_amount_percentage && +approvePopupPayload?.charges_date && +approvePopupPayload?.bryzos_available_credit_limit);
  const { data: bnpls, isLoading: isBnplsLoading } = useGetAllBnplData();
  const { data: adminReferenceData, isLoading: isAdminReferenceDataLoading } = useGetAdminReferenceData();
  const {
    mutate: saveEditBnpl,
    data: editedBnplData,
    isLoading: isEditBnplDataLoading,
    error: editBnplError,
  } = useEditBnplData();
  const {
    mutate: approveRejectBnpl,
    data: approveRejectBnplData,
    isLoading: isApproveRejectBnplLoading,
    error: approveRejectBnplError,
  } = useApproveRejectBnpl();
  const {
    mutate: refreshCreditLimit,
    isLoading: iscreditLimitDataLoading,
    data: creditLimitData,
  } = useRefreshCreditLimit();

  const {
    mutate: updateBnplStatus,
    data: updateBnplStatusData,
    isLoading: isUpdateBnplStatusLoading,
  } = usePostBnplUpdateStatus();

  useEffect(() => {
    if (bnpls) {
      const _bnpls = bnpls.map((bnpl: any) => ({
        ...bnpl,
        status:
          bnpl.is_approved === 1
            ? "Approved"
            : bnpl.is_approved === 0
              ? "Rejected"
              : "Pending",
      }));

      setModifiedBnpls(_bnpls ? _bnpls : []);
      setFilteredaBnpls(_bnpls ? _bnpls : []);
    } else {
      setModifiedBnpls([]);
      setFilteredaBnpls([]);
    }
  }, [bnpls]);

  useEffect(() => {
    if (approveRejectBnplData && !isApproveRejectBnplLoading) {
      if (approveRejectBnplError) {
        setApiResponseMessage((approveRejectBnplError as Error).message);
      } else {
        setApiResponseMessage(approveRejectBnplData);
      }
    }
  }, [
    approveRejectBnplData,
    isApproveRejectBnplLoading,
    approveRejectBnplError,
  ]);

  useEffect(() => {
    if (creditLimitData !== undefined && !iscreditLimitDataLoading) {
      setShowPopup(true);
    }
  }, [creditLimitData, iscreditLimitDataLoading]);

  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
  }, [itemsPerPage]);

  useEffect(() => {
    if (editedBnplData) {
      setApiResponseMessage(editedBnplData);
    }
  }, [editedBnplData]);

  useEffect(() => {
    if (updateBnplStatusData) {
      setApiResponseMessage(updateBnplStatusData);
    }
  }, [updateBnplStatusData]);

  useEffect(() => {
    if (adminReferenceData?.bnpl_default_restricted_amount) {
      setRestrictAmount(formatCurrency(adminReferenceData.bnpl_default_restricted_amount));
    }
  }, [adminReferenceData]);

  const search = (event: any) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(event.target.value);
    if (event.target.value) {
      const _filterArrray = filterArrray(modifiedBnpls, event.target.value, [
        "first_name",
        "last_name",
        "company_name",
        "client_company",
        "requested_credit_limit",
        "bnpl_status",
      ]);
      if (_filterArrray?.length) {
        setFilteredaBnpls(_filterArrray);
      }
    } else {
      setFilteredaBnpls(modifiedBnpls);
    }
  };

  const onClickRejectHandler = (bnpl: any) => {
    approveRejectBnpl({
      data: {
        user_id: bnpl.user_id,
        is_approved: 0,
        credit_limit: bnpl.credit_limit ? +bnpl.credit_limit: null,
        requested_credit_limit: bnpl.requested_credit_limit ? +bnpl.requested_credit_limit: null,
        net_terms_days: bnpl.net_terms_days ? +bnpl.net_terms_days: null,
        auth_amount_percentage: bnpl.auth_amount_percentage ? +bnpl.auth_amount_percentage: null,
        charges_date: bnpl.charges_date ? +bnpl.charges_date: null,
      },
    });
  };

  const refreshCreditLimitHandler = (bnpl: any, approvePopup = false) => {
    setShowApprovePopup(approvePopup);
    setApprovePopupPayload(bnpl);
    refreshCreditLimit(bnpl.user_id);
  };

  const popupCloseHandler = () => {
    setShowPopup(false);
  };

  const cancelEditedBnplData = () => {
    setEditBnplData(null);
  };

  const saveEditedBnplEditData = () => {
    const netTermsDays = +editBnplData?.net_terms_days;
    const authAmountPercentage = +editBnplData?.auth_amount_percentage;
    const chargesDate = +editBnplData?.charges_date;
    const bryzosAvailableCreditLimit = +editBnplData?.bryzos_available_credit_limit;
    if (netTermsDays > 0 && authAmountPercentage > 0 && chargesDate > 0 && bryzosAvailableCreditLimit > 0 && reasonForEditBnpl?.length) {
      saveEditBnpl({
        data: {
          user_id: editBnplData.user_id,
          net_terms_days: netTermsDays,
          auth_amount_percentage: authAmountPercentage,
          charges_date: chargesDate,
          bryzos_credit_amount: bryzosAvailableCreditLimit,
          reason: reasonForEditBnpl,
        },
      });
      setEditBnplData(null);
      setReasonForEditBnpl("")
    } else {
      setApiResponseMessage("Net terms, Auth Amount, Charges date, Credit Amount, Reason mandatory");
    }
  };

  const handleEditPopupVlaueChange = (event: any, key: string) => {
    setEditBnplData((prev: any) => ({
      ...prev,
      [key]: event.target.value,
    }));
  };

  const handlePageClick = (event: any) => {
    const newOffset = (event.selected * itemsPerPage) % filteredaBnpls.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  const showApprovePopupYes = () => {
    popupCloseHandler();
    const t = setTimeout(() => {
      clearTimeout(t);
      setShowApprovePopup(false);
    }, 500);

    const data = approvePopupPayload;
    if (data) {
      approveRejectBnpl({
        data: {
          user_id: data.user_id,
          is_approved: 1,
          credit_limit: data.credit_limit ? +data.credit_limit : null,
          requested_credit_limit: data.requested_credit_limit ? +data.requested_credit_limit : null,
          net_terms_days: data.net_terms_days ? +data.net_terms_days : null,
          auth_amount_percentage: data.auth_amount_percentage ? +data.auth_amount_percentage : null,
          charges_date: data.charges_date ? +data.charges_date : null,
        },
      });
    }
  };

  const showApprovePopupNo = () => {
    popupCloseHandler();
    const t = setTimeout(() => {
      clearTimeout(t);
      setShowApprovePopup(false);
    }, 500);
    setApprovePopupPayload(null);
  };

  const handleEditBNPLStatus = (bnpl: any) => {
    setBnplStatusData(bnpl);
    setBnplStatus(bnpl.bnpl_status);
    if(bnpl?.max_restricted_amount && Number(bnpl.max_restricted_amount) > 0){
      setRestrictAmount(formatCurrency(bnpl.max_restricted_amount ));
    }
    setOpenEditBnplStatusModal(true);
  };

  const handleStatusChange = (event: any) => {
    setBnplStatus(event.target.value);
  };

  const handleRestrictAmountChange = (event: any) => {
    // Remove non-numeric characters for calculation
    const numericValue = event.target.value.replace(/[^0-9.]/g, '');
    
    // Only update if it's a valid number or empty
    if (numericValue === '' || !isNaN(numericValue)) {
      setRestrictAmount(numericValue);
    }
  };

  // Format currency with commas
  const formatCurrency = (value: string) => {
    if (!value) return '';
    
    // Parse the numeric value
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) return '';
    
    // Format with commas for thousands
    return numericValue.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const submitBnplStatusEdit = () => {
    updateBnplStatus({
      user_id: bnplStatusData.user_id,
      status: bnplStatus,
      restricted_amount: bnplStatus === BNPL_STATUS.restricted ? parseFloat(restrictAmount.replace(/,/g, '')) : null
    });
    setOpenEditBnplStatusModal(false);
    setRestrictAmount(formatCurrency(adminReferenceData.bnpl_default_restricted_amount));
  };

  const cancelBnplStatusEdit = () => {
    setOpenEditBnplStatusModal(false);
  };

  return (
    <div>
      {isBnplsLoading ||
        isApproveRejectBnplLoading ||
        iscreditLimitDataLoading ||
        isEditBnplDataLoading ||
        isUpdateBnplStatusLoading ||
        isAdminReferenceDataLoading ? (
        <div className={styles.loaderImg}>
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>

            <input
              className={styles.searchInput}
              type="text"
              onChange={search}
              placeholder="Search"
              value={inputSearchValue}
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>First Name</th>
                  <th>Last Name</th>
                  <th>Company Name</th>
                  <th>Company Entity/Location</th>
                  <th>Requested Credit Limit</th>
                  {showBryzosCreditData && (
                    <>
                      <th>Approved Limit</th>
                      <th>Available Credit</th>
                    </>
                  )}
                  <th colSpan={6}>Status</th>
                </tr>
              </thead>
              <tbody>
                {filteredaBnpls?.length ? (
                  filteredaBnpls
                    .slice(itemOffset, endOffset)
                    .map((bnpl: any) => (
                      <tr key={bnpl.user_id}>
                        <td>{bnpl.first_name}</td>
                        <td>{bnpl.last_name}</td>
                        <td>{bnpl.company_name}</td>
                        <td>{bnpl.client_company}</td>
                        <td>
                          {format2DecimalPlaces(bnpl.requested_credit_limit)}
                        </td>
                        {showBryzosCreditData && (
                          <>
                            <td>{format2DecimalPlaces(bnpl.bryzos_credit_limit)}</td>
                            <td>{format2DecimalPlaces(bnpl.bryzos_available_credit_limit)}</td>
                          </>
                        )}
                        <td>
                          {bnpl.bnpl_status === BNPL_STATUS.restricted ? (
                            <Tooltip 
                            title={bnpl.bnpl_status && (formatCurrency(bnpl.max_restricted_amount))}
                            placement="top-end"
                            arrow
                            classes={{
                              popper: styles.restrictedTooltip,
                              tooltip: styles.tooltip,
                              arrow:styles.arrowTooltip,
                            }}
                          >
                            <div>
                              {toTitleCase(bnpl.bnpl_status)}
                            </div>
                          </Tooltip>
                          ) : (
                            toTitleCase(bnpl.bnpl_status)
                          )}
                          </td>
                        <td>
                          <button
                            className={styles.approvalBtn}
                            onClick={() => setEditBnplData(bnpl)}
                          >
                            Edit
                          </button>
                        </td>
                        <td>
                          <button
                            className={styles.approvalBtn}
                            onClick={() => handleEditBNPLStatus(bnpl)}
                            disabled={bnpl.is_approved !== 1}
                          >
                            Edit BNPL Status
                          </button>
                        </td>
                        <td>
                          {bnpl.is_approved === null && (
                            <button
                              className={styles.approvalBtn}
                              onClick={() =>
                                refreshCreditLimitHandler(bnpl, true)
                              }
                            >
                              Approve
                            </button>
                          )}
                        </td>
                        <td>
                          {bnpl.is_approved === null && (
                            <button
                              className={styles.rejectBtn}
                              onClick={() => onClickRejectHandler(bnpl)}
                            >
                              Reject
                            </button>
                          )}
                        </td>
                        <td>
                          <button
                            className={styles.refreshCreditLimitHandler}
                            onClick={() => refreshCreditLimitHandler(bnpl)}
                          >
                            Get Credit Limit
                          </button>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={11} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
        </div>
      )}
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyPop}>
          <div
            className={styles.successfullytext}
            dangerouslySetInnerHTML={{ __html: apiResponseMessage }}
          ></div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
          className={clsx(styles.editApprovePopup,'editLinesDropdown')}
          open={!!openEditBnplStatusModal}
          dialogTitle={"Edit BNPL Status"}
        >
          {openEditBnplStatusModal && bnplStatusData && (
            <div className={styles.editRefPop}>
              <div className={styles.editRefDate}>
                <div className={styles.titleFiled}>
                  <div>
                    Available Limit <span>:</span>
                  </div>
                  <div>
                    Status <span>:</span>
                  </div>
                  {bnplStatus === BNPL_STATUS.restricted && (
                    <div>
                      Restrict Amount <span>:</span>
                    </div>
                  )}
                </div>
                <div className={styles.inputFiled}>
                  <div>
                    <input
                      value={format2DecimalPlaces(bnplStatusData.bryzos_available_credit_limit)}
                      readOnly
                      type="text"
                    />
                  </div>
                  <div>
                    <Select
                      className={styles.statusDropdown}
                      value={
                         bnplStatus !== null && bnplStatus !== undefined && bnplStatus !== '' && bnplStatus !== BNPL_STATUS.rejected && bnplStatus !== BNPL_STATUS.pending
                ? bnplStatus
                : 'Choose Status'
                      }
                      onChange={handleStatusChange}
                      fullWidth
                    >
                      <MenuItem value={'Choose Status'} disabled>
                        Choose Status
                      </MenuItem>
                      <MenuItem value={BNPL_STATUS.enabled}>Enabled</MenuItem>
                      <MenuItem value={BNPL_STATUS.onHold}>On Hold</MenuItem>
                      <MenuItem value={BNPL_STATUS.restricted}>Restricted</MenuItem>
                    </Select>
                  </div>
                  {bnplStatus === BNPL_STATUS.restricted && (
                    <div>
                      <input
                        value={restrictAmount}
                        onChange={handleRestrictAmountChange}
                        onBlur={() => setRestrictAmount(formatCurrency(restrictAmount.replace(/,/g, '')))}
                        type="text"
                        placeholder="Enter restrict amount"
                      />
                    </div>
                  )}
                </div>
              </div>
              <div className={styles.btnFiled}>
                <button onClick={submitBnplStatusEdit}>Submit</button>
                <button onClick={cancelBnplStatusEdit}>Cancel</button>
              </div>
            </div>
          )}
        </MatPopup>
      <MatPopup open={showPopup} disablePortal>
        <div className={styles.refreshPop}>
          <button className={styles.crossBtn} onClick={popupCloseHandler}>
            x
          </button>
          {creditLimitData && (
            <div>
              {showApprovePopup && <p>Approve?</p>}
              <table>
                <tbody>
                  <tr>
                    <td>Bryzos Available Credit Limit</td>
                    <td>
                      {format2DecimalPlaces(
                        creditLimitData?.balance_available_credit_limit
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Bryzos Credit Limit</td>
                    <td>
                      {format2DecimalPlaces(
                        creditLimitData?.balance_credit_limit
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Outstanding Amount</td>
                    <td>
                      {format2DecimalPlaces(
                        creditLimitData?.outstanding_amount
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Status</td>
                    <td>{creditLimitData?.status}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          )}
          <div className={styles.noDataFoundPopup}>
            {creditLimitData === null && (
              <div>
                <p className={styles.noDataFoundtext}>No data found</p>
              </div>
            )}
            {showApprovePopup ? (
              <div className={styles.yesNoBtn}>
                <button
                  className={styles.okBtn}
                  onClick={showApprovePopupYes}
                  disabled={disableApprove}
                >
                  Yes
                </button>
                <button
                  className={styles.okBtn}
                  onClick={showApprovePopupNo}
                >
                  No
                </button>
              </div>
            ) : (
              <button className={styles.okBtn} onClick={popupCloseHandler}>
                Ok
              </button>
            )}
          </div>
        </div>
      </MatPopup>
      <div>
        <MatPopup
          className={styles.editApprovePopup}
          open={!!editBnplData}
          dialogTitle={""}
        >
          {editBnplData && (
            <div className={styles.editRefPop}>
              <div className={styles.editRefDate}>
                <div className={styles.titleFiled}>
                  <div>
                    Net Terms Days <span>:</span>
                  </div>
                  <div>
                    Auth Amount Percentage <span>:</span>
                  </div>
                  <div>
                    Charges Date <span>:</span>
                  </div>
                  {showBryzosCreditData && (
                    <>
                      <div>
                        Credit Amount <span>:</span>
                      </div>
                      <div>
                        Reason <span>:</span>
                      </div>
                    </>
                  )}
                </div>
                <div className={styles.inputFiled}>
                  <div>
                    <input
                      value={editBnplData.net_terms_days}
                      onChange={(e) =>
                        handleEditPopupVlaueChange(e, "net_terms_days")
                      }
                      type="number"
                      min={1}
                    />
                  </div>
                  <div>
                    <input
                      value={editBnplData.auth_amount_percentage}
                      onChange={(e) =>
                        handleEditPopupVlaueChange(e, "auth_amount_percentage")
                      }
                      type="number"
                      min={1}
                    />
                  </div>
                  <div>
                    <input
                      value={editBnplData.charges_date}
                      onChange={(e) =>
                        handleEditPopupVlaueChange(e, "charges_date")
                      }
                      type="number"
                      min={1}
                    />
                  </div>
                  {showBryzosCreditData && (
                    <>
                      <div>
                        <input
                          value={editBnplData.bryzos_available_credit_limit}
                          onChange={(e) =>
                            handleEditPopupVlaueChange(e, "bryzos_available_credit_limit")
                          }
                          type="number"
                          min={1}
                        />
                      </div>
                      <div>
                        <input
                          value={reasonForEditBnpl}
                          onChange={(e) =>
                            setReasonForEditBnpl(e.target.value)
                          }
                          type="text"
                          min={1}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
              <div className={styles.btnFiled}>
                <button onClick={saveEditedBnplEditData}>Save</button>
                <button onClick={cancelEditedBnplData}>Cancel</button>
              </div>
            </div>
          )}
        </MatPopup>
      </div>
    </div>
  );
};

export default ApproveReject;
