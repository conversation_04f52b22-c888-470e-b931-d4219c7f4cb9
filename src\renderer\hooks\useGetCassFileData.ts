import { CommonStringDto } from "@bryzos/giss-common-lib";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const useGetCassFileData = () => {

  return useMutation(async (obj: any) => {
    try {
      const response: any = (await axios.get(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/cass/getFileData?cass_filename=${obj.cass_filename}`)).data;

      if (response.data) {
        return response.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message ?? "");
    }
  });
};

export default useGetCassFileData;
