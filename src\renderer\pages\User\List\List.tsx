import { Select, MenuItem } from "@mui/material";
import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader/Loader";
import useGetAllUser from "../../../hooks/useGetAllUser";
import { filterArrray } from "../../../utils/helper";
import styles from "./List.module.scss";
import MatPopup from "../../../components/common/MatPopup";
import clsx from "clsx";
import usePostUserData from "../../../hooks/usePostUserData";
import OTPInput from "react-otp-input";
import useResetPassword from "../../../hooks/useResetPassword";
import dayjs from "dayjs";
import usePostUpdateUserDiscount from "../../../hooks/usePostUpdateUserDiscount";
import DiscountPopup from "../../../components/DiscountPopup";
import useGetAdminReferenceData from "../../../hooks/useGetAdminReferenceData";
import { BUYER } from "../../../utils/constant";
import Create from "../Create";
import { CreateUserFromSchemaType } from "../../../models/createUser.model";
import usePostUserEditData from "../../../hooks/usePostUserEditData";
import checkIcon from '../../../../assests/images/check-icon.svg';
import notCheckIcon from '../../../../assests/images/Close.svg';
import { ReactComponent as ShowPass } from '../../../../assests/images/show-pass.svg';
import { ReactComponent as HidePass } from '../../../../assests/images/hide-pass.svg';

const List = () => {
  const [filteredaUsers, setFilteredaUsers] = useImmer<any>([]);

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [userData, setUserData] = useState<any>({});
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [showResetPasswordPopup, setShowResetPasswordPopup] = useImmer(false);
  const [confirmationPopupData, setConfirmationPopupData] = useImmer<any>(null);
  const [resetPasswordData, setResetPasswordData]= useImmer<any>(null);
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [resetPassword, setResetPassword]= useState("");
  const [disableResetPasswordBtn, setDisableResetPasswordBtn]= useState(true);
  const [showDiscountPopup, setShowDiscountPopup]= useState(false);
  const [discountUserData, setDiscountUserData] = useState<any>(null);
  const [userEditData, setUserEditData] = useState<CreateUserFromSchemaType | null>(null);
  const [showUserEditPopup, setShowUserEditPopup] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState(false)

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredaUsers.length / itemsPerPage);

  const { data: allUsers, isLoading: allUsersLoading, isFetching: usersFetching } = useGetAllUser();
  const { data: adminReferenceData, isLoading: adminReferenceDataLoading} = useGetAdminReferenceData();
  const {
    mutate: saveUserActive,
    data: saveUserActiveData,
    isLoading: isSaveUserActiveDataLoading,
  } = usePostUserData();
  const {
    mutate: editUser,
    data: editUserData,
    isLoading: isEditUserDataLoading,
  } = usePostUserEditData();
  const {
    mutate: saveUserResetPassword,
    data: saveUserResetPasswordData,
    isLoading: isSaveUserResetPasswordDataLoading,
  } = useResetPassword();
  const {
    mutate: updateUserDiscount,
    data: updatedUserDiscountData,
    isLoading: isUpdateUserDiscountLoading,
  } = usePostUpdateUserDiscount();

  useEffect(() => {
    if (allUsers) {
      setFilteredaUsers(allUsers);
    } else {
      setFilteredaUsers([]);
    }
  }, [allUsers]);

  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
  }, [itemsPerPage]);

  useEffect(() => {
    if (saveUserActiveData) {
      userData.is_active = userData.is_active === 1 ? 0 : 1 ;
      setFilteredaUsers([...filteredaUsers])
    }
  }, [saveUserActiveData]);
  useEffect(() => {
    if (saveUserResetPasswordData) {
      setApiResponseMessage(saveUserResetPasswordData);
    }
  }, [saveUserResetPasswordData]);
  useEffect(() => {
    if (updatedUserDiscountData) {
      setApiResponseMessage(updatedUserDiscountData);
    }
  }, [updatedUserDiscountData]);

  useEffect(()=>{
    if (editUserData) {
      setApiResponseMessage(editUserData);
    }
  },[editUserData])


  const search = (event: any) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(event.target.value);
    if (event.target.value) {
      const _filterArrray = filterArrray(allUsers, event.target.value, [
        "first_name",
        "last_name",
        "email_id",
        "company_name",
        "type",
        "onboarded_app",
      ]);
      if (_filterArrray?.length) {
        setFilteredaUsers(_filterArrray);
      } else {
        setFilteredaUsers([]);
      }
    } else {
      setFilteredaUsers(allUsers ? allUsers : []);
    }
  };

  const handlePageClick = (event: any) => {
    const newOffset = (event.selected * itemsPerPage) % filteredaUsers.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  const confirmationPopupYes = () => {
    if (confirmationPopupData) {
      saveUserActive(confirmationPopupData);
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setConfirmationPopupData(null);
    setShowResetPasswordPopup(false);
    setResetPassword("")
    setDisableResetPasswordBtn(true);
    setShowDiscountPopup(false);
    setShowUserEditPopup(false);
    setShowPassword(false);
  };

  const userActiveHandler = (user: any) => {
    setShowConfirmationPopup(true);
    setUserData(user);
    setConfirmationPopupData({
      data: {
        id: user.id,
        is_active: !Boolean(user.is_active),
      },
    });
  };

  const handleUserResetPassword = (emailId: string) => {
    setShowResetPasswordPopup(true);
    setResetPasswordData({
      "data": {
          "email_id": emailId,
          "password": ""
      }
  })
  }

  const handleResetPasswordOnChange = (val: string) =>{
    setResetPassword(val);
    if(val.length >= 6){
      setDisableResetPasswordBtn(false);
    }else{
      setDisableResetPasswordBtn(true);
    }
  }

  const submitUserResetPassword = () => {
    if (resetPasswordData) {
      let updateResetPasswordData =  resetPasswordData;
      updateResetPasswordData.data.password = resetPassword;
      saveUserResetPassword(updateResetPasswordData);
    }
    confirmationPopupClose();
    setResetPassword("")
  }

  const submitUserDiscount = (data: any) => {
    let payload: any = {
      "user_id": data.id,
    }
    const phaseoutStartDate = data?.discDiscountPhaseoutStartdate ? dayjs(data.discDiscountPhaseoutStartdate).format('YYYY-MM-DD') : null;
    payload.is_discount = data.discIsDiscounted
    payload.discount_percentage = data.discDiscountRate
    payload.discount_period = data.discDiscountPeriod
    payload.discount_phaseout_startdate = phaseoutStartDate
    payload.discount_phaseout_period = data.discDiscountPhaseoutPeriod
    payload.discount_pricing = 'Neutral_Pricing'//data.discDiscountPricingColumn
    payload.is_overriden = data.discIsDiscountVarOverriden
    payload.seller_spread_percentage = data.spreadRate
    updateUserDiscount({data: payload})
    confirmationPopupClose();

  }

  const handleUserDiscount = (userData: any) => {
    setDiscountUserData(userData);
    setShowDiscountPopup(true);
  }

  const handleUserEdit = (userData: any) => {
    setShowUserEditPopup(true);
    setUserEditData(userData);
  }

  const handleUserEditSubmit = (userData: any) => {
    setShowUserEditPopup(false);
    const payload = {
      "data": {
        "user_id": userData.userId,
        "first_name": userData.firstName,
        "last_name": userData.lastName,
        "company_name": userData.companyName,
        "client_company": userData.companyEntity,
        "email": userData.emailAddress,
        "user_type": userData.type,
        "onboard_source": userData.onboardSource,
        "is_external_api_company_admin":userData.isExternalApiAdmin
      }
    }
    editUser(payload)
  }


  return (
    <div>
      {allUsersLoading ||
      isSaveUserActiveDataLoading ||
      isSaveUserResetPasswordDataLoading ||
      isUpdateUserDiscountLoading ||
      adminReferenceDataLoading ||
      usersFetching ||
      isEditUserDataLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <input
              className={styles.searchInput}
              type="text"
              onChange={search}
              placeholder="Search"
              value={inputSearchValue}
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>First Name</th>
                  <th>Last Name</th>
                  <th>Email</th>
                  <th>Company name</th>
                  <th>Date Of Joining</th>
                  <th>Type</th>
                  <th>Onboard Source</th>
                  <th>External API Company Admin</th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {filteredaUsers?.length ? (
                  filteredaUsers
                    .slice(itemOffset, endOffset)
                    .map((user: any, index: number) => (
                      <tr key={user.id}>
                        <td>{user.first_name}</td>
                        <td>{user.last_name}</td>
                        <td>{user.email_id}</td>
                        <td>{user.company_name}</td>
                        <td>{user.joining_date}</td>
                        <td>{user.type}</td>
                        <td>{user.onboarded_app}</td>
                        <td className={styles.externalApiAdminStatus}>{user?.is_external_api_company_admin ?<span><img src={checkIcon}/></span> : <span><img src={notCheckIcon}/></span>}</td>
                        <td>
                          <button
                            className={styles.resetPassBtn}
                            onClick={() => {
                              handleUserEdit(user);
                            }}
                          >
                            Edit
                          </button>
                        </td>
                        <td>
                          {user.type === BUYER &&
                            <button
                              className={styles.resetPassBtn}
                              onClick={() => {
                                handleUserDiscount(user);
                              }}
                            >
                              Spread
                            </button>
                          }
                        </td>
                        <td>
                          <button
                            className={styles.resetPassBtn}
                            onClick={() =>
                              handleUserResetPassword(user.email_id)
                            }
                          >
                            Reset Password
                          </button>
                        </td>
                        <td>
                          <label className={styles.switch}>
                            <input
                              type={"checkbox"}
                              checked={user.is_active === 1}
                              onChange={() => userActiveHandler(user)}
                            />
                            <span
                              className={clsx(styles.slider, styles.round)}
                            ></span>
                          </label>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={8} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
        </div>
      )}

      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Do you want to continue ?</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showResetPasswordPopup}
      >
        <div className={styles.tblscrollPop}>
          <div className={styles.continuePopup1}>
            <p className={styles.continuetext}>Enter New Password.</p>
            <div className={styles.inputFiledResetPass}>
              <input 
                type={showPassword ? "text" : "password"}
                value={resetPassword} 
                onChange={(e) => handleResetPasswordOnChange(e.target.value)} 
                className={clsx(styles.InputFieldcss, styles.pass)}
                placeholder="Enter New Password"
              />
              <button type="button" className={styles.showHidePass} onClick={() => setShowPassword(x => !x)}>
                {showPassword ? <HidePass /> : <ShowPass />}
              </button>
            </div>
            <div className={styles.yesAndnoBtn}>
              <button
                className={styles.okBtn}
                onClick={submitUserResetPassword}
                disabled={disableResetPasswordBtn}
              >
                Submit
              </button>
              <button className={styles.okBtn} onClick={confirmationPopupClose}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showDiscountPopup}
      >
        <DiscountPopup pageName={"user"} popupHeading={"Spread"} discountData={discountUserData}  pricingColumnList={adminReferenceData?.pricing_columns} defaultValues={adminReferenceData?.discount_default_values} submitUserDiscount={submitUserDiscount} confirmationPopupClose={confirmationPopupClose}  />
      </MatPopup>
      <MatPopup
        className={'orderContinuePopup pendingUserPopup editUserPopupMain'}
        open={showUserEditPopup}
      >
        <div className={styles.tblscrollPop}>
          <div className={styles.listUserEditPopup}>
            <p className={styles.continuetext}>Edit User</p>
            
            <Create isUserEdit = {userEditData} popupClose={confirmationPopupClose} handleNewUserData={handleUserEditSubmit} setShowNewUserPopup={setShowUserEditPopup} />
          </div>
        </div>
      </MatPopup>

    </div>
  );
};

export default List;
