import { useEffect } from "react";
import { useImmer } from "use-immer";
import Loader from "../../components/common/Loader";
import styles from "./RestartNodeMessageCron.module.scss";
import LoginPopup from "../../components/LoginPopup";
import MatPopup from "../../components/common/MatPopup";
import useGetRestartNodeMessageCron from "../../hooks/useGetRestartNodeMessageCron";
import { logoNameList } from "../../utils/constant";

const RestartNodeMessageCron = () => {
  const [isUserReAuthenticated, setIsUserReAuthenticated] = useImmer(false);
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);

  const {
    mutate: restartNodeMessageCronServer,
    data: restartNodeMessageCronResponse,
    isLoading: isGetRestartNodeMessageCronLoading,
  } = useGetRestartNodeMessageCron();

  useEffect(() => {
    if (restartNodeMessageCronResponse) {
      setApiResponseMessage("The Message cron node service has been restarted. It will take upto 2 mins to complete.");
    }
  }, [restartNodeMessageCronResponse]);

  const handleRestartNodeMessageCron = () => {
    setShowConfirmationPopup(true);
  }
  const confirmationPopupYes = () => {
    restartNodeMessageCronServer({})
    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
  };
  return (
    <div>
      {isGetRestartNodeMessageCronLoading ? (
        <div className={styles.loaderImg}>
          <Loader />
        </div>
      ) : isUserReAuthenticated ? (
        <div>
          <div className={styles.settingPage}>
          <div className={styles.referenceDataText}>
            <label className={styles.referenceLabelText}>Message cron node  </label>
            <button className={styles.submitBtn} onClick={handleRestartNodeMessageCron} > Restart</button>
          </div>
        </div>
          <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Are you sure want to Restart ?</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup> 
        </div>
      ) : (
        <LoginPopup
          isUserReAuthenticated={isUserReAuthenticated}
          setIsUserReAuthenticated={setIsUserReAuthenticated}
          logoName={logoNameList.bryzosLogo}
        />
      )}
    </div>
  );
};

export default RestartNodeMessageCron;
