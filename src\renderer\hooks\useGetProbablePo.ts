import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

const useGetProbablePo = (itemsPerPage: number, currentPage: number, search: string, showMatchedTransaction: boolean) => {
  return useQuery([reactQueryKeys.getProbablePo, itemsPerPage, currentPage, search, showMatchedTransaction], async () => {
    try {
      const response = (await axios.get(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/cass/getProbablePo?page=${currentPage}&limit=${itemsPerPage}&search=${search}&showMatchedTransaction=${showMatchedTransaction}`)).data;
      if (response.data) {
        if (typeof response.data === "object" && "error_message" in response.data) {
          throw new Error(response.data.error_message);
        } else {
          return response;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  }, {retry: false});
};

export default useGetProbablePo;
