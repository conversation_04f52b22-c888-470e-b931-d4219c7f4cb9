.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 20px;
    max-height: 670px;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }


    table {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        border-collapse: collapse;
        border-spacing: 0;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }

        thead {
            tr {
                border: 1px solid gray;

                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 6px 12px;
                    color: #fff;
                    height: 35px;
                    position: sticky;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
                    z-index: 99;

                    &:nth-child(2) {
                        width: 350px;
                    }

                    &.selectedPosTh{
                        width: 200px;
                    }

                }

                td {
                    line-height: 2.5;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;

                    &:nth-child(even) {
                        background-color: #f2f2f2;

                    }

                  
                }
            }
        }

        tbody {
            background-color: #fff;

            tr {
                margin: 0;

                &:nth-child(even) {
                    background-color: #f2f2f2;

                }

                &.highlightTransaction{
                    background-color: bisque;
                }

                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;
                    white-space: pre-wrap;
                    text-align: left;

                    &.selectedPosTd{
                        white-space: nowrap;
                        span{
                            color: #ff5d47;
                        }
    
                     }

                    &.noDataFoundTd {
                        text-align: center;
                    }

                    
                    .messageText {
                        box-shadow: none;
                        outline: none;
                        height: 38px;
                        width: 180px;
                        padding: 6px;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 1.5;
                        color: #495057;
                        background-color: #fff;
                        background-clip: padding-box;
                        border: 1px solid #ced4da;
                        border-radius: 0.25rem;
                        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }



                }
            }
        }
    }
}

.showAllTranChk.showAllTranChk{
 display: flex;
 align-items: center;
 margin-right: 12px;
 label{
    span{
        top:-9px
    }
 }
}

.searchBox {
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        display: flex;
        flex-direction: column;
    }

    .showdropdwn {
        width: 82px;
        height: 38px;
        padding: 4px;
    }

    .searchInput {
        box-shadow: none;
        outline: none;
        height: 38px;
        padding: 6px 12px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    .rightEditSection {
        display: flex;
        margin-left: auto;

        .editBtn {
            width: 70px;
            height: 38px;
            font-size: 14px;
            color: #fff;
            background-color: var(--primaryColor);
            border-color: #122b40;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 12px;
            border: 0px;
        }
    }
}

.backDrop{
    position: fixed;
    background-color: rgba(0, 0, 0, 0.3);
    width: 100%;
    height: 100%;
    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;
    z-index: 999;
}

.loaderImg {
    text-align: center;
    margin-left: 50%;
    margin-right: auto;
    margin-top: 36vh;
    width: auto;
    position: absolute;
    z-index: 1000;
}

.loaderPosition {
    display: flex;
    justify-content: center;
}

.saveButton {
    width: 70px;
    height: 38px;
    font-size: 14px;
    color: #fff;
    background-color: var(--primaryColor);
    border-color: #122b40;
    border-radius: 5px;
    cursor: pointer;
    border: 0px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        margin-left: 0px;
        margin-top: 10px;
    }
    &:disabled{
        opacity: 0.5;
    }
}

.saveBtnf {
    width: 70px;
    height: 38px;
    color: #fff;
    background-color: var(--primaryColor);
    border-color: #122b40;
    border-radius: 5px;
    cursor: pointer;
    border: 0px;
    margin-right: 12px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        position: unset;
        margin-top: 10px;
    }
    &:disabled{
        opacity: 0.5;
    }
}

.noteText {
    font-size: 18px;
    line-height: normal;
    margin-bottom: 20px;
    font-weight: 600;
    color: var(--primaryColor);
    text-align: center;
    color: red
}

.errorStyle.errorStyle {
    .tooltip.tooltip {
        background-color: #ff5d47;
        color: #fff;
        font-family: Noto Sans;
        font-size: 10px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.6;
        letter-spacing: normal;
        text-align: left;
        color: #fff;
        margin-bottom: 5px;
    }

}

.cassFileNameDiv {
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;

    .cassFileNameExclamation {
        background: #0099ff;
        border-radius: 50%;
        color: #fff;
        cursor: pointer;
        display: flex;
        height: 18px;
        width: 18px;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }

    .cassFileName {
        line-height: 1;
    }

    .cassFileInfo {
        position: absolute;
        background-color: #fff;
        z-index: 1;
        padding: 20px;
        font-size: 12px;
        max-height: 325px;

        .cassFileInfo1 {
            height: 100%;
            max-height: 251px;
            overflow-y: scroll;

            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }

            &::-webkit-scrollbar-track {
                box-shadow: inset 0 0 6px #a8b2bb;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #a8b2bb;
                border-radius: 4px;
            }

        }

        .cassFileBtn {
            display: flex;
            align-items: center;
            justify-content: right;

            button {
                background-color: var(--primaryColor);
                color: #fff;
                border-radius: 4px;
                height: 30px;
                width: 60px;
                border: none;
            }
        }
    }
}

.autocompleteDescPanel {
    border-radius: 4px;
    width: 100%;
    max-width: 300px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    background-color: #fff;
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    padding-right: 0px;
    border-radius: 0px 0px 4px 4px;
    overflow: hidden;
}

.listAutoComletePanel.listAutoComletePanel {
    width: 100%;
    overflow: auto;
    padding: 0px;
    max-height: 250px;
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
    
      &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
      }
    
      &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
      }
    
    li {
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #333;
        box-shadow: none;
        padding: 4px 8px;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 4px;

        &:hover {
            border-radius: 2px;
            background-color: #fff;
            color: #000;
        }

        &[aria-selected="true"] {
            background-color: #fff;
            color: #000;
        }
    }
}

.listAutoComletePanel1.listAutoComletePanel1 {
    width: 100%;
    overflow: auto;
    padding: 0px;
    max-height: 320px;
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
    
      &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
      }
    
      &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
      }
    li {
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #333;
        box-shadow: none;
        padding: 4px 8px;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 4px;

        &:hover {
            border-radius: 2px;
            background-color: #fff;
            color: #000;
        }

        &[aria-selected="true"] {
            background-color: #fff;
            color: #000;
        }
    }
}

.selectDropdown {
    width: 100%;
    max-width: 300px;
    height: 40px;
    min-width: 300px;
    font-family: Noto Sans;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        width: 100%;
    }
}

.cassFileInfoContent {
    height: 100%;
    padding: 12px;

    .cassLoaderImg {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }


    .cassFileBtn {
        text-align: right;

        .btnCopy {
            width: 60px;
            height: 30px;
            color: #fff;
            background-color: var(--primaryColor);
            border-color: #122b40;
            border: 0px;
            border-radius: 4px;
            cursor: pointer;
        }

    }


    .cassFileInfo {
        height: 100%;

        .cassFileInfo1 {
            height: calc(100% - 40px);
            overflow: auto;
            font-size: 14px;
            white-space: pre-line;
            border: 1px solid #eee;
            border-radius: 4px;
            margin: 10px 0px 0px 0px;
            padding: 12px;

            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }

            &::-webkit-scrollbar-track {
                box-shadow: inset 0 0 6px #a8b2bb;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #a8b2bb;
                border-radius: 4px;
            }
        }
    }

}


.orderContinuePopup {
    h2 {
        display: none;
    }

    .editPopup {
        max-width: 1200px;
        width: 100%;
    }

    .searchInputMain {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 12px;
        padding: 20px 20px 0px 20px;

        input {
            margin-right: 8px;
            box-shadow: none;
            outline: none;
            height: 38px;
            padding: 6px 12px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            width: 200px;
        }

        button {
            width: 70px;
            height: 38px;
            font-size: 14px;
            color: #fff;
            background-color: var(--primaryColor);
            border-color: #122b40;
            border-radius: 5px;
            cursor: pointer;
            border: 0px;
        }
    }

    .editTableContent {
        padding:0px 20px 0px 20px;
        width: 100%;
    }

    .yesAndnoBtn {
        display: flex;
        justify-content: center;
        gap: 10px;
        border-top: 1px solid #eee;
        padding: 20px 0px;

        .okBtn {
            width: 160px;
            height: 40px;
            border-radius: 6px;
            text-decoration: none;
            gap: 8px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }

    }
}

.cassAutocompleteTooltipStyle {
    .tooltip.tooltip {
        color: #fff;
        font-family: Noto Sans;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        margin-top: 6px;
    }
}

.closeOrderPopup {
    .mainContent {
        width: 100%;
        max-width: 880px;
        padding: 24px 16px;
        font-family: Noto Sans, sans-serif;

        .popupHeader {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;

            p {
                font-size: 20px;
                font-weight: bold;
                margin: 0px;
            }

            button {
                cursor: pointer;
                background-color: transparent;
                padding: 0px;
                border: 0px;
                svg{
                    width: 24px;
                    height: 24px;
                    path{
                        fill: var(--primaryColor);
                    }
                }
            }
        }

        .scrollBox {
            overflow: auto;
            max-height: 400px;
            padding-right: 8px;
            margin-bottom: 24px;
            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }

            &::-webkit-scrollbar-track {
                box-shadow: inset 0 0 6px #a8b2bb;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #a8b2bb;
                border-radius: 4px;
            }
            .closeOrderPopupBox {
                margin-bottom: 10px;
                border-bottom: 1px solid #9e9e9e;
                &:first-child{
                    border-top:1px solid #9e9e9e;
                }

                .companyName {
                        font-size: 15px;
                        color: var(--primaryColor);
                        display: block;
                        padding: 8px 0px;
                        text-align: left;
                        line-height: normal;
                        font-weight: 600;
                }

                .closeOrderBox {
                    display: flex;
                    align-items: center;
                    column-gap: 16px;
                    font-family: Noto Sans;

                    .chkMain {
                        display: flex;
                        flex-direction: column;
                        width: 100%;
                        table{
                            width: 100%;
                            border-spacing: 0px;
                            tr{
                                
                        th {
                            font-family: Noto Sans;
                            font-size: 13px;
                            text-transform: uppercase;
                            color: var(--primaryColor);
                            font-weight: 600;
                            line-height: 1.4;

                        }
                                th,td{
                                    padding: 5px 4px;
                                    background-color: #eee;
                                    text-align: left;
                                }
                                td{
                                    vertical-align: baseline;
                                    font-family: Noto Sans;
                                    font-size: 14px;
                                    color: var(--primaryColor);
                                }
                            }
                        }

                        .chkBoxMain{
                            display: flex;
                            .chkBox {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                font-family: Noto Sans;
                                font-size: 14px;
                                color: var(--primaryColor);
                                flex: 0 0 100px;
                                .closed{
                                    color: #ff5d47;
                                }
    
                                input {
                                    margin-left: 4px;
                                    margin-top: 0px;
                                }
    
                                p {
                                    margin: 0px;
                                }
                            }
                        }

                    
                    }

                    .AmtPaidByBuyer{
                        display: flex;
                        flex-direction: column;

                        input {
                            width: 110px;
                            height: 26px;
                            font-family: Noto Sans;
                            font-size: 14px;
                            color: var(--primaryColor);
                            border: 1px solid #ced4da;
                            border-radius: 2px;
                            padding: 0px 6px;
                            &:focus{
                                outline: none;
                            }
                        }

                        .invalidInput {
                            border-color: red;
                        }

                        .errorMessage{
                            color: red;
                            font-size: 12px;
                            height:12px;
                        }

                    }

                    .fundingAmt {
                        display: flex;
                        flex-direction: column;

                        span {
                            font-family: Noto Sans;
                            font-size: 14px;
                            text-transform: uppercase;
                            color: var(--primaryColor);

                            &:first-child {
                                font-weight: 600;
                            }

                        }
                    }

                }
            }
        }

        .errorText{
            font-family: Noto Sans;
            color:red;
            font-size: 12px;
            margin-top: -30px;
        }

        .submitBtn {
            font-family: Noto Sans;
            background-color: var(--primaryColor);
            color: #fff;
            font-size: 18px;
            padding: 8px 12px;
            border-radius: 4px;
            border: 0px;
            cursor: pointer;
            &[disabled]{
               opacity: 0.5;
               cursor: not-allowed;
            }
        }

        .noPoToCloseText {
            font-size: 18px;
            text-align: center;
            margin: 0px 0px 15px 0px;
        }
    }
}
.minWidth1{
    min-width: 230px;
}
.minWidth2{
    min-width: 300px;
}
.cassMapptingDropdownBtn{
    font-family: Noto Sans;
    width: 100%;
    max-width: 300px;
    height: 40px;
    font-weight: 400;
    line-height: 1.4375em;
    letter-spacing: 0.00938em;
    text-align: left;
    padding:9px ;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.87);
    border: solid 1px rgba(98, 107, 132,0.42);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    .placeholder{
        opacity: 0.42;
    }
    .dataSelected{
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 75%;
    }
    .arrowIcon{
        padding: 0px;
        border: 0px;
        display: flex;
        background-color: transparent;
        svg{
            width: 22px;
            height: 22px;
            path{
                fill{
                    color: currentColor;
                }
            }
        }
    }
}

.autoCompleteLoader {
    position: absolute;
    inset: 0;
    background-color: #fff;
    z-index: 100000;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.7;
    color: #000;
    font-size: 20px;
   font-weight: bold;
}

.cassFileNameExclamation {
    background: #ff0000;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: flex;
    height: 18px;
    width: 18px;
    align-items: center;
    justify-content: center;
    line-height: 1;
}
.goToPageNumberInput{
    display: flex;
    align-items: center;
    column-gap: 12px;
    input{
        width: 140px;
        height: 40px;
        border: 1px solid #a8b2bb;
        border-radius: 4px;
        padding: 6px 12px;
    }
    .goBtn{
        background-color: var(--primaryColor);
        color: #fff;
        font-size: 16px;
        border-radius: 4px;
        padding: 6px 20px;
        height: 40px;
        border: 0px;
        cursor: pointer;
        &[disabled]{
            opacity: 0.5;
            cursor: not-allowed;
        }
    }
}