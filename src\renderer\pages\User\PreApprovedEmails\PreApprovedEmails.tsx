import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import { useContext, useEffect, useState } from "react";
import styles from "./PreApprovedEmails.module.scss";
import clsx from "clsx";
import { Checkbox, MenuItem, Select } from "@mui/material";
import { ClearIcon } from "@mui/x-date-pickers";
import useGetPreApprovedEmails from "../../../hooks/useGetPreApprovedEmails";
import { useDebouncedValue } from "@mantine/hooks";
import { useImmer } from "use-immer";
import ReactPaginate from "react-paginate";
import MatPopup from "../../../components/common/MatPopup";
import AddEmails from "./AddEmails";
import usePostSaveEmails from "../../../hooks/usePostSaveEmails";
import Loader from "../../../components/common/Loader";
import { CommonCtx } from "../../AppContainer";
import { convertUtcToCtTimeUsingDayjs } from "@bryzos/giss-ui-library";



const PreApprovedEmails = () => {
  
  const showPopupFormAnyComponent = useContext(CommonCtx);
  const [showAddEmailsPopup, setShowAddEmailsPopup] = useState(false);
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue, 1000);
  const [meta, setMeta] = useImmer<any>(null);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [rowData, setRowData] = useState([  ]);
  const [colDefs, setColDefs] = useState([
    {
      field: "is_signed_up",
      headerName: "User Signed Up",
      minWidth: 50,
      width:170,
      cellRenderer: (props: any) => {
        return (
          <div className={styles.poLineMatchMain}>
          <Checkbox checked={props?.data?.is_signed_up === 1}
            disabled />
          </div>
        );
      },
    },
    {
      field: "email_id",
      headerName: "Approved Emails",
      minWidth: 220,
      flex: 1,
      cellRenderer: (props: any) => {
        return (
          <div className={styles.poLineMatchMain}>{props?.data?.email_id}</div>
        );
      },
    },
    {
      field: "cohort",
      headerName: "Cohort Name",
      minWidth: 220,
      flex: 1,
      cellRenderer: (props: any) => {
        return (
          <div className={styles.poLineMatchMain}>{props?.data?.cohort}</div>
        );
      },
    },
    {
      field: "added_by",
      headerName: "Added By",
      minWidth: 220,
      flex: 1,
      cellRenderer: (props: any) => {
        return (
          <div className={styles.poLineMatchMain}>{props?.data?.added_by}</div>
        );
      },
    },
    {
      field: "created_date",
      headerName: "Added On",
      minWidth: 220,
      flex: 1,
      cellRenderer: (props: any) => {
        return (
          <div className={styles.poLineMatchMain}>{convertUtcToCtTimeUsingDayjs(props?.data?.created_date)}</div>
        );
      },
    },
  ]);
  const {
    mutateAsync: saveEmails,
    data: saveEmailData,
    isLoading: isSaveEmailLoading,
  }  = usePostSaveEmails();

  const {
    data: preApprovedEmailsData,
    isLoading: isPreApprovedEmailsDataLoading,
    refetch : refetchEmails
  } = useGetPreApprovedEmails(
    itemsPerPage,
    currentPage,
    debouncedInputSearchValue
  );

  const gridOptions = {
    icons: {
      sortAscending: '<span class="custom-sort-asc sorted"></span>',
      sortDescending: '<span class="custom-sort-desc sorted"></span>',
      sortUnSort: '<span class="custom-sort-none"></span>',
    },
  };

  useEffect(()=>{
    if(!isSaveEmailLoading){
      if(saveEmailData){
        showPopupFormAnyComponent(saveEmailData, 'Ok');
      }
    }
  }, [isSaveEmailLoading, saveEmailData])

  useEffect(() => {
    if (isPreApprovedEmailsDataLoading) {
      return;
    }
    if (preApprovedEmailsData?.meta) {
      setMeta(preApprovedEmailsData.meta);
    }

    if (preApprovedEmailsData?.data?.length) {
      let invoiceEmailAttachmentsRowData = []
      invoiceEmailAttachmentsRowData = preApprovedEmailsData.data;
      setRowData(invoiceEmailAttachmentsRowData);
    } else {
      setRowData([])
    }
  }, [preApprovedEmailsData, isPreApprovedEmailsDataLoading]);

  const defaultColDef = {
    sortable: false,
    lockVisible: true,
    unSortIcon: true,
    cellStyle: { flex: 1 },
    wrapHeaderText: true,
    autoHeaderHeight: true,
    lockPinned: true
  };

  const closePopup = ()=>{
    setShowAddEmailsPopup(false);
  }

  const addEmails = async(data:any)=>{
    closePopup();
    await saveEmails(data);
    refetchEmails();
  }

  const onGridReady = (event: any) => {
    
  }

  const search = (searchValue: string) => {
    setCurrentPage(1);
    setInputSearchValue(searchValue);
  };

  const clearInput = () => {
    setInputSearchValue('');
  };

  const handlePageClick = (event: any) => {
    setCurrentPage(event.selected + 1);
  };

  return <div className={styles.emailscontainer}>
    <div className={styles.searchBox}>
      <div className={styles.sortDataSection}>
        <Select
          className="editLinesDropdown emailAttachDropdown"
          MenuProps={{
            classes: {
              paper: styles.Dropdownpaper,
              list: styles.muiMenuList,
            },
          }}
          value={itemsPerPage}
          onChange={(event) => {
            setItemsPerPage(+event.target.value);
          }}
        >
          {perPageEntriesOptions.map((item, index) => (
            <MenuItem key={index} value={item}>
              <span>{item}</span>
            </MenuItem>
          ))}
        </Select>
      </div>
      <div className={styles.SortRightSection}>
        <div className={styles.searchContainer}>
          <button className={styles.addEmailsBtn} onClick={() => setShowAddEmailsPopup(true)} >Add Emails</button>
          <input
            className={styles.searchInput}
            type="text"
            onChange={(e) => search(e.target.value)}
            placeholder="Search on Approved Emails"
            value={inputSearchValue}
          />
          {inputSearchValue && (
            <button className={styles.clearInputIcon} onClick={clearInput}>
              <ClearIcon />
            </button>
          )}
        </div>
      </div>
    </div>
    {isSaveEmailLoading || isPreApprovedEmailsDataLoading ? (
        <div className={styles.noDataFound}>
            <Loader />
        </div>
      ) : (
        <div
        className={clsx(styles.ag_theme_quartz, styles.agGridAdmin)}
        style={{
          height: "calc(100vh - 250px)",
          width: "100%",
          minHeight: "400px",
        }}
      >
      <AgGridReact
        rowData={rowData}
        columnDefs={colDefs}
        sideBar={true}
        suppressCellFocus={true}
        rowHeight={70}
        headerHeight={42}
        gridOptions={gridOptions}
        enableCellTextSelection={true}
        ensureDomOrder={true}
        defaultColDef={defaultColDef}
        embedFullWidthRows={true}
        onGridReady={onGridReady}
      /></div>)}
    <div className={"PaginationNumber"}>
      {meta && (
        <ReactPaginate
          breakLabel="..."
          nextLabel=">"
          onPageChange={handlePageClick}
          pageRangeDisplayed={5}
          pageCount={meta.totalPages}
          previousLabel="<"
          renderOnZeroPageCount={(props) =>
            props.pageCount > 0 ? undefined : null
          }
          forcePage={meta.currentPage > 0 ? meta.currentPage - 1 : undefined}
        />
      )}
    </div>
    <MatPopup
      classes={{
        root: styles.addEmailPopup,
        paper: styles.addEmailContent,
      }}
      open={showAddEmailsPopup}
    >
      <AddEmails closePopup = {closePopup} addEmails = {addEmails}/>
    </MatPopup>
  </div>
};
export default PreApprovedEmails;