import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import dayjs from "dayjs";

const usePostGetDeliveryDatePreview = () => {

  return useMutation(async (payload: any) => {
    try {
      const timestamp = dayjs(`${payload.checkoutDate}${payload.checkoutTime}`).format('YYYY-MM-DDTHH:mm:ss');
      const id = payload.user;
      const response = await axios.get(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/order/get-delivery-dates?id=${id}&timestamp=${encodeURIComponent(timestamp)}`);


      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostGetDeliveryDatePreview;
