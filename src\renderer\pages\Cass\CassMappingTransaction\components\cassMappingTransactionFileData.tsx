import { <PERSON><PERSON><PERSON><PERSON>Listener, Popper } from "@mui/material";
import Loader from "../../../../components/common/Loader";
import styles from "../CassMappingTransaction.module.scss";
import React, { useContext, useEffect, useState } from "react";
import { useImmer } from "use-immer";
import useGetCassFileData from "../../../../hooks/useGetCassFileData";
import { CommonCtx } from "../../../AppContainer";

const CassMappingTransactionFileData = ({cassMappingdata}: any) => {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const [cassFileId, setCassFileId] = useState<string>('');
    const [fileData, setFileData] = useImmer<any>({});
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const {
        mutate: getCassFileMutate,
        data: getCassFileData,
        isLoading: isGetCassFileLoading,
    } = useGetCassFileData();


    useEffect(() => {
        if (getCassFileData?.error_message){
            setCassFileId("")
            showPopupFormAnyComponent(getCassFileData.error_message)
        }
        else if (typeof getCassFileData === 'string') {
            setFileData((prev: { [x: string]: any; }) => {
                prev[cassFileId] = getCassFileData;
                return prev;
            })
        }
    }, [getCassFileData])
    const handleFileOnClick = (cassMapData: { id: string; cass_filename: any; }, event: React.MouseEvent<HTMLElement>) => {
        setCassFileId(cassMapData.id)
        getCassFileMutate({ cass_filename: cassMapData.cass_filename })
        setAnchorEl(event.currentTarget);
    }

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text).catch(err => console.error('Failed to copy:', err));
        setFileData({})
        setCassFileId('');
    };

    const handleClickAway = () => {
        setCassFileId('');
    };

    return (
        <>
            <span className={styles.cassFileNameExclamation} onClick={(e) => { handleFileOnClick(cassMappingdata, e) }}>!</span>
            {cassFileId === cassMappingdata.id &&
                <ClickAwayListener onClickAway={handleClickAway}>
                    <Popper className="cassMappingData"
                        placement="right-start"
                        open={cassFileId === cassMappingdata.id} anchorEl={anchorEl} >
                        <div className={styles.cassFileInfoContent}>
                            {isGetCassFileLoading ? (
                                <div className={styles.cassLoaderImg}>
                                    <Loader />
                                </div>
                            ) : (
                                <>
                                    <div className={styles.cassFileInfo}>
                                        <div className={styles.cassFileBtn}>
                                            <button className={styles.btnCopy} onClick={() => { copyToClipboard(fileData[cassMappingdata.id]) }}>
                                                Copy
                                            </button>
                                        </div>
                                        <p className={styles.cassFileInfo1}>{fileData[cassMappingdata.id]}</p>
                                    </div>
                                </>
                            )}
                        </div>
                    </Popper >
                </ClickAwayListener>
            }
        </>
    )
}
export default CassMappingTransactionFileData;