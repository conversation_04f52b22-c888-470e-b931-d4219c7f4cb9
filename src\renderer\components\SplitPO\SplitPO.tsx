import React, { useContext, useEffect, useRef, useState } from "react";
import styles from "./SplitPO.module.scss";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Logo from "../../../assests/images/pdfLogo.png";
import GreenArrow from "../../../assests/images/arrow.png";
import { ReactComponent as LeftArrow } from '../../../assests/images/icon-chevron-left.svg';
import { ReactComponent as AddRow } from '../../../assests/images/add.svg';
import { ReactComponent as RemoveIcon } from '../../../assests/images/close-icon.svg';
import { ReactComponent as CloseIcon } from '../../../assests/images/close-icon.svg';
import clsx from "clsx";
import _ from "lodash";
import { axios, dateTimeFormat, formatToTwoDecimalPlaces, getFloatRemainder, getUnitPostScript, orderIncrementPrefix, priceUnits, removeCommaFromCurrency } from "@bryzos/giss-ui-library";
import { getOrderIncrementUnit, getProductMapping, updatedAllProductsData } from "../../utils/helper";
import { useImmer } from "use-immer";
import MatPopup from "../common/MatPopup";
import { Tooltip } from "@mui/material";
import useGetInvoiceData from "../../hooks/useGetInvoiceData";
import usePostSplitInvoiceData from "../../hooks/usePostSplitInvoiceData";
import { CommonCtx } from "../../pages/AppContainer";
import CustomDatePicker from "../common/CustomDatePicker";
import dayjs from "dayjs";
import Loader from "../common/Loader";
import MatSelect from "../common/MatSelect";
import useGetReferenceData from "../../hooks/useGetReferenceData";
import usePostFetchProductsPrices from "../../hooks/usePostFetchProductsPrices";
import { useDebouncedValue } from "@mantine/hooks";

// Helper function to allow only numeric input with decimal points
const allowNumericOnly = (value: string): string => {
  return value.replace(/[^\d.]/g, '');
};

const formatValue = (value: number | string): string => {
  if (value === 'Exempt' || !value) return '0.00';
  const numberValue = value.toString().replace(/[\$,]/g, '');
  return formatToTwoDecimalPlaces(numberValue);
};

type Props = {
  // Add your props here
  order: any;
  onClose: () => void;
  referenceProducts: any;
  invoiceEmailRecipients: any;
};

const SplitPoSchema = yup.object().shape({
  activeInvoice: yup.object().shape({
    lineItems: yup.array().of(yup.object().shape({
      description: yup.string().required("Description is required"),
      description_line_1: yup.string(),
      description_other_part: yup.string(),
      domestic_material_only: yup.boolean().nullable(),
      po_line: yup.number(),
      qty_type: yup.string(),
      price_type: yup.string(),
      quantity: yup.string().required("Quantity is required"),
      line_weight: yup.string(),
      line_weight_unit: yup.string(),
      product_tag: yup.string().nullable(),
      price_per_unit: yup.string().required("Unit price is required"),
      extended: yup.string().required("Total is required"),
      product_id: yup.number(),
      buyer_calculation_price_per_unit: yup.string(),
    })),
    invoice_details: yup.object().shape({
      invoice_number: yup.string().required("Invoice number is required"),
      buyer_po_number: yup.string().required("Sales order is required"),
      buyer_internal_po_number: yup.string().required("Buyer PO is required"),
      invoice_date: yup.string().required("Invoice date is required"),
      payment_due_date: yup.string().required("Payment due date is required"),
      invoice_version: yup.string(),
    }),
    fulfilled_by: yup.object().shape({
      company_name: yup.string(),
      name: yup.string(),
      address: yup.object().shape({
        line1: yup.string(),
        line2: yup.string().nullable(),
        city: yup.string(),
        state: yup.string(),
        zip: yup.string(),
      }),
      email: yup.string(),
    }),
    bill_to: yup.object().shape({
      company_name: yup.string().required("Company name is required"),
      name: yup.string().required("Name is required"),
      address: yup.object().shape({
        line1: yup.string().required("Address line 1 is required"),
        line2: yup.string().nullable(),
        city: yup.string().required("City is required"),
        state: yup.string().required("State is required"),
        zip: yup.string().required("Zip is required"),
      }),
      email: yup.string().email("Invalid email format").required("Email is required"),
    }),
    shipping_details: yup.object().shape({
      line1: yup.string(),
      line2: yup.string().nullable(),
      city: yup.string(),
      state_id: yup.string(),
      zip: yup.string(),
    }),
    details: yup.object().shape({
      sales_tax: yup.string().required("Sales tax is required"),
      total_purchase: yup.string().required("Total purchase is required"),
      material_total: yup.string().required("Material total is required"),
      payment_due_date: yup.string().required("Payment due date is required"),
      deposit: yup.string().nullable(),
      payment_method: yup.string(),
      credit: yup.string().nullable(),
      restock_fee: yup.string().nullable(),
      freight_fee: yup.string().nullable(),
    }),
    payment_instructions: yup.object().shape({
      company_name: yup.string().required("Seller company name is required"),
      bank_name: yup.string().required("Bank name is required"),
      account_number: yup.string().required("Account number is required"),
      routing_number: yup.string().required("Routing number is required"),
      account_name: yup.string().required("Account name is required"),
      bank_address: yup.string().required("Bank address is required"),
    })
  }),
  splitInvoice: yup.object().shape({
    invoice_details: yup.object().shape({
      invoice_number: yup.string().required("Invoice number is required"),
      buyer_po_number: yup.string().required("Sales order is required"),
      buyer_internal_po_number: yup.string().required("Buyer PO is required"),
      invoice_date: yup.string().required("Invoice date is required"),
      payment_due_date: yup.string().required("Payment due date is required"),
      invoice_version: yup.string(),
    }),
    bill_to: yup.object().shape({
      company_name: yup.string().required("Company name is required"),
      name: yup.string().required("Name is required"),
      address: yup.object().shape({
        line1: yup.string().required("Address line 1 is required"),
        line2: yup.string().nullable(),
        city: yup.string().required("City is required"),
        state: yup.string().required("State is required"),
        zip: yup.string().required("Zip is required"),
      }),
      email: yup.string().email("Invalid email format").required("Email is required"),
    }),
    fulfilled_by: yup.object().shape({
      company_name: yup.string(),
      name: yup.string(),
      address: yup.object().shape({
        line1: yup.string(),
        line2: yup.string().nullable(),
        city: yup.string(),
        state: yup.string(),
        zip: yup.string(),
      }),
      email: yup.string().email("Invalid email format"),
    }),
    lineItems: yup.array().of(yup.object().shape({
      description: yup.string().test({
        name: 'conditional-required',
        test: function(value) {
          // If description is empty, no other validations will trigger
          return true;
        }
      }),
      description_line_1: yup.string(),
      description_other_part: yup.string(),
      domestic_material_only: yup.boolean().nullable(),
      po_line: yup.number(),
      line_no: yup.number(),
      qty_type: yup.string(),
      price_type: yup.string(),
      quantity: yup.string().when('description', {
        is: (val: string) => val && val.length > 0,
        then: (schema) => schema.required("Quantity is required"),
        otherwise: (schema) => schema
      }),
      line_weight: yup.string(),
      line_weight_unit: yup.string(),
      product_tag: yup.string().nullable(),
      price_per_unit: yup.string().when('description', {
        is: (val: string) => val && val.length > 0,
        then: (schema) => schema.required("Unit price is required"),
        otherwise: (schema) => schema
      }),
      extended: yup.string().when('description', {
        is: (val: string) => val && val.length > 0,
        then: (schema) => schema.required("Total is required"),
        otherwise: (schema) => schema
      }),
      product_id: yup.number(),
      buyer_calculation_price_per_unit: yup.string(),
    })),
    details: yup.object().shape({
      sales_tax: yup.string().required("Sales tax is required"),
      total_purchase: yup.string().required("Total purchase is required"),
      material_total: yup.string().required("Material total is required"),
      payment_due_date: yup.string().required("Payment due date is required"),
      deposit: yup.string().nullable(),
      payment_method: yup.string(),
      credit: yup.string().nullable(),
      restock_fee: yup.string().nullable(),
      freight_fee: yup.string().nullable(),
    }),
    payment_instructions: yup.object().shape({
      company_name: yup.string().required("Seller company name is required"),
      bank_name: yup.string().required("Bank name is required"),
      account_number: yup.string().required("Account number is required"),
      routing_number: yup.string().required("Routing number is required"),
      account_name: yup.string().required("Account name is required"),
      bank_address: yup.string().required("Bank address is required"),
    }),
    shipping_details: yup.object().shape({
      line1: yup.string(),
      line2: yup.string().nullable(),
      city: yup.string(),
      state_id: yup.string(),
      zip: yup.string(),
    })
  })  
});

export type SchemaType = yup.InferType<
  typeof SplitPoSchema
>;

const SplitPO: React.FC<Props> = ({ order, onClose, referenceProducts, invoiceEmailRecipients }) => {
  const showPopupFormAnyComponent = useContext(CommonCtx);
  
  // Add refs for the adjustment input fields
  const activeCreditInputRef = useRef<HTMLInputElement>(null);
  const activeRestockInputRef = useRef<HTMLInputElement>(null);
  const activeFreightInputRef = useRef<HTMLInputElement>(null);
  const splitCreditInputRef = useRef<HTMLInputElement>(null);
  const splitRestockInputRef = useRef<HTMLInputElement>(null);
  const splitFreightInputRef = useRef<HTMLInputElement>(null);
  const activeInvoiceRef = useRef<HTMLDivElement>(null);
  const splitInvoiceRef = useRef<HTMLDivElement>(null);
  
  // Helper function for precise decimal subtraction
  const preciseSubtract = (a: number, b: number): number => {
    // Convert to strings and determine the number of decimal places
    const strA = a.toString();
    const strB = b.toString();
    const decimalPlacesA = strA.includes('.') ? strA.split('.')[1].length : 0;
    const decimalPlacesB = strB.includes('.') ? strB.split('.')[1].length : 0;
    
    // Use the maximum decimal places between the two numbers
    const maxDecimalPlaces = Math.max(decimalPlacesA, decimalPlacesB);
    
    // Calculate and round to avoid floating point errors
    const result = Number((a - b).toFixed(maxDecimalPlaces));
    return result;
  };

  const [poNumber, setPoNumber] = useState("");
  // Add state to track original quantities
  const [originalQuantities, setOriginalQuantities] = useState<Record<number, string>>({});
  
  // State for tracking invoice history popup
  const [showInvoiceHistoryPopup, setShowInvoiceHistoryPopup] = useState(false);
  
  // Add state for storing invoice history
  const [invoiceHistory, setInvoiceHistory] = useState<any[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  
  // Add state for tracking adjustment visibility
  const [activeAdjustments, setActiveAdjustments] = useState({
    creditReturn: false,
    restockFee: false,
    freightFee: false
  });
  
  const [splitAdjustments, setSplitAdjustments] = useState({
    creditReturn: false,
    restockFee: false,
    freightFee: false
  });
  
  const [activeAdjustmentValues, setActiveAdjustmentValues] = useState({
    creditReturn: "",
    restockFee: "",
    freightFee: ""
  });
  
  const [splitAdjustmentValues, setSplitAdjustmentValues] = useState({
    creditReturn: "",
    restockFee: "",
    freightFee: ""
  });
  const [productMapping, setProductMapping] = useState<any>({});
  const [multiplierChangeInBuyerPricing, setMultiplierChangeInBuyerPricing] = useImmer<number>(1);
  const [salesTaxCounter, setSalesTaxCounter] = useState(0);
  const {
    register,
    control,
    handleSubmit,
    setError,
    clearErrors,
    reset,
    getValues,
    setValue,
    watch,
    formState: { errors, isValid, isDirty, isSubmitting },
  } = useForm<SchemaType>({
    resolver: yupResolver(SplitPoSchema),
    defaultValues: {},
    mode: "onChange",
  });
  const [splitDataByPage, setSplitDataByPage] = useState([2]);
  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);
  const [sendEmail, setSendEmail] = useState(false);

  // Add a state variable to track sales tax calculation
  const [isSalesTaxCalculating, setIsSalesTaxCalculating] = useState(false);

  const [hasOriginalActiveTotalPurchase, setHasOriginalActiveTotalPurchase] = useState(true);
  const [hasLineItemStateChanged, setHasLineItemStateChanged] = useState(false);
  const [states, setStates] = useState([]);
  const [isPerformingAction, setIsPerformingAction] = useState(false);

  const { data: invoiceData, isLoading: isInvoiceDataLoading } = useGetInvoiceData(order?.buyer_po_number || "");
  const { data: referenceData, isLoading: isGetReferenceDataLoading } = useGetReferenceData();
  const { mutate: postSplitInvoiceData, isLoading: isPostSplitInvoiceDataLoading, data: postSplitInvoiceDataResponse } = usePostSplitInvoiceData();
  const { mutateAsync: postFetchProductsPrices } = usePostFetchProductsPrices();

  // Function to handle file downloads with custom filename
  const handleFileDownload = async (fileUrl: string, fileName: string) => {
    try {
      // Sanitize filename to remove problematic characters
      const sanitizedFileName = sanitizeFileName(fileName);
      
      // Fetch the file
      const response = await fetch(fileUrl);
      
      // Check if the request was successful
      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.status} ${response.statusText}`);
      }
      
      const blob = await response.blob();
      
      // Verify we have content
      if (blob.size === 0) {
        throw new Error("Downloaded file is empty");
      }
      
      // Create a Blob URL
      const blobUrl = window.URL.createObjectURL(blob);
      
      // Create download link
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = sanitizedFileName;
      
      // Append to body and trigger download programmatically
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(blobUrl);
      }, 100);
    } catch (error) {
      console.error("Error downloading file:", error);
      alert(`Failed to download file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Function to sanitize filenames
  const sanitizeFileName = (fileName: string): string => {
    if (!fileName) return "download";
    
    // Replace characters that might cause issues in filenames
    return fileName
      .replace(/[/\\?%*:|"<>]/g, '-') // Replace illegal filename chars with dash
      .trim();                        // Remove leading/trailing whitespace
  };

  // Update the disableEmailSendButton condition to include sales tax calculation state
  const disableEmailSendButton = !watch('splitInvoice.lineItems')?.some(item => 
    item.description && 
    (item.quantity !== '' && item.quantity !== undefined && !isNaN(Number(item.quantity))) &&
    (item.price_per_unit !== '' && item.price_per_unit !== undefined && !isNaN(Number(item.price_per_unit)))
  ) || !watch('splitInvoice.invoice_details.invoice_number') ||
  isSalesTaxCalculating;

  const disableActiveSendButton =  !isSalesTaxCalculating && hasOriginalActiveTotalPurchase && (!hasLineItemStateChanged || watch('activeInvoice.lineItems')?.some(item => (isNaN(Number(item.quantity))) || item.price_per_unit?.length === 0));
  
  useEffect(() => {
    if(referenceProducts){
        const getAllProducts = updatedAllProductsData(referenceProducts);
        const productMappingData = getProductMapping(getAllProducts)
        setProductMapping(productMappingData);
    }
},[referenceProducts])

useEffect(()=>{
  if (referenceData) {
    setStates(referenceData?.ref_states);
}
},[referenceData])

  useEffect(()=>{
    // Initialize form with order data if available
    if (invoiceData) {
      // Store original quantities
      const origQty: Record<number, string> = {};
      if (invoiceData.items && invoiceData.items.length > 0) {
        invoiceData.items.forEach((item: any, index: number) => {
          origQty[index] = item.quantity?.toString() || '0';
        });
      }
      setOriginalQuantities(origQty);
      
      setActiveAndSplitInvoiceData(invoiceData);
      
      // Store original total purchase value
      if (invoiceData.details && invoiceData.details.total_purchase) {
        setHasOriginalActiveTotalPurchase(true);
      }
      
      let _splitData = [2];
      const products = invoiceData.items;
      if(products.length > 2){
        if(products.length <= 4){
          _splitData = [2,products.length-2]
        }
        else {
          _splitData = [4]
          let itemLength = products.length - 4;
          while(itemLength > 0){
            if(itemLength - 8 > 0){
              _splitData.push(8);
              itemLength -= 8;
            }
            else{
              const lastPageitems = itemLength - 7;
              _splitData.push(Math.min(7,itemLength))
              if(lastPageitems>0)
              _splitData.push(lastPageitems);
              itemLength = 0;
            }
          }
        }
      }
      setSplitDataByPage(_splitData);
    }
  },[invoiceData, setValue])

  useEffect(()=>{
    if(postSplitInvoiceDataResponse){
      console.log(postSplitInvoiceDataResponse);
      showPopupFormAnyComponent(postSplitInvoiceDataResponse)
      onClose();
    }
  },[postSplitInvoiceDataResponse])
  
  const calculateTotalWeight = (orders: any) => {
    let totalOrderWeights = 0;
    orders?.forEach((data: any) => {
        let lineWeight = 0;
        const qty = data.quantity ? parseFloat(data.quantity) : 0;
        const productData = productMapping[data.product_id];
        if(productData && Object.keys(productData).length > 0 && qty > 0 && data.description){
            if (productData) {
              const qtyUnit = data.qty_type ? data.qty_type : productData.QUM_Dropdown_Options.split(",")[0];
              lineWeight = calculateLineWeight(qty, qtyUnit, productData);
            }
        }
        totalOrderWeights = +(formatToTwoDecimalPlaces(totalOrderWeights).replace(/[\$,]/g, "")) + +(formatToTwoDecimalPlaces(lineWeight).replace(/[\$,]/g, ""));
    });
    return totalOrderWeights;
  }

  const calculateLineWeight = (qty: number, qtyUnit: string, product: any) => {
    const updateUnit = (qtyUnit === priceUnits.ea ? priceUnits.pc : qtyUnit).toLowerCase();
    const orderIncrementLb = parseFloat(product[`${orderIncrementPrefix}${priceUnits.lb}`].replace(/[\$,]/g, '')) 
    const orderIncrementFt = parseFloat(product[`${orderIncrementPrefix}${priceUnits.ft}`].replace(/[\$,]/g, ''));
    const lbsPerFt = orderIncrementLb / orderIncrementFt
    const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
    const actualOrderIncrement = parseFloat(product[`${orderIncrementPrefix}${updateUnit}`].replace(/[\$,]/g, ''));
    return orderIncrementFtPrice * qty / actualOrderIncrement;
  }

  const getProductPriceObj = async (product: any) => {
    let productPriceObj: Record<string, any> = {};
    const activeTotalWeight = calculateTotalWeight(watch('activeInvoice.lineItems') || []);
    const splitTotalWeight = calculateTotalWeight(watch('splitInvoice.lineItems') || []);

    const totalWeight = activeTotalWeight + splitTotalWeight;
    const cartItemsList = [
      {
          "po_line": product.po_line,
          "product_id": product.product_id
      }
    ]
    const productPriceMapping  = await fetchProductPrices(cartItemsList, totalWeight);
    productPriceObj = {...productPriceMapping};
    const productMappingSpread = { ...productMapping };
    Object.keys(productPriceObj).forEach((key: string) => {
      const productId = productPriceObj[key].product_id;
      productMappingSpread[productId].price = productPriceObj[key].prices;
    });
    setProductMapping(productMappingSpread);
  }

  const fetchProductPrices = async (productIdList: any, totalWeight: any) => {
    const productPriceMapping = await postFetchProductsPrices({
      "po_number": invoiceData?.invoice_details?.buyer_po_number,
      "cart_items": productIdList, // array of number
      "order_size": totalWeight // can be string|or number but string should also be numeric 
    });
    return productPriceMapping;
  }

  // Update setActiveAndSplitInvoiceData to store original quantities
  const setActiveAndSplitInvoiceData = (order: any) => {
    // Store original quantities
    const origQty: Record<number, string> = {};
    if (order.items && order.items.length > 0) {
      order.items.forEach((item: any, index: number) => {
        origQty[index] = item.quantity?.toString() || '0';
      });
    }
    setOriginalQuantities(origQty);
    
    // Active Invoice Data
    setValue('activeInvoice.invoice_details', order.invoice_details);
    setValue('activeInvoice.bill_to', order.bill_to);
    const lineItems = order.items.map((item:any, index:number)=>{
      return {...item, line_no: index+1}; 
    })
    setValue('activeInvoice.lineItems', lineItems || []);
    setValue('activeInvoice.details', order.details);
    setValue('activeInvoice.payment_instructions', order.payment_instructions);
    setValue('activeInvoice.shipping_details', order.shipping_details);

    // Check if ledger adjustments exist in the order data and activate them
    if (order.details) {
      // For Credit
      if (order.details.credit && parseFloat(order.details.credit) >= 0) {
        setActiveAdjustments(prev => ({...prev, creditReturn: true}));
        setActiveAdjustmentValues(prev => ({...prev, creditReturn: order.details.credit}));
      }
      
      // For Restock Fee
      if (order.details.restock_fee && parseFloat(order.details.restock_fee) >= 0) {
        setActiveAdjustments(prev => ({...prev, restockFee: true}));
        setActiveAdjustmentValues(prev => ({...prev, restockFee: order.details.restock_fee}));
      }
      
      // For Freight Fee
      if (order.details.freight_fee && parseFloat(order.details.freight_fee) >= 0) {
        setActiveAdjustments(prev => ({...prev, freightFee: true}));
        setActiveAdjustmentValues(prev => ({...prev, freightFee: order.details.freight_fee}));
      }
    }

    if(order.order_fulfilled_by){
      const fulfilledBy = typeof order.order_fulfilled_by === 'object' && !Array.isArray(order.order_fulfilled_by) 
        ? order.order_fulfilled_by 
        : (Array.isArray(order.order_fulfilled_by) && order.order_fulfilled_by.length > 0 
          ? order.order_fulfilled_by[0] 
          : null);
      
      if (fulfilledBy) {
        // Map email_id to email if needed
        if (fulfilledBy.email_id && !fulfilledBy.email) {
          fulfilledBy.email = fulfilledBy.email_id;
        }
        setValue('activeInvoice.fulfilled_by', fulfilledBy);
      }
    }

    // Split Invoice Data
    const splitInvoiceDetails = {
      ...order.invoice_details,
      payment_due_date: (dayjs(order.invoice_details.invoice_date).isValid() && order.details.payment_method !== 'ach_credit')
        ? dayjs(order.invoice_details.invoice_date).add(30, 'days').format('M/D/YY') 
        : order.invoice_details.payment_due_date
    };
    setValue('splitInvoice.invoice_details', splitInvoiceDetails);
    setValue('splitInvoice.bill_to', order.bill_to);
    setValue('splitInvoice.payment_instructions', order.payment_instructions);
    setValue('splitInvoice.shipping_details', order.shipping_details);
    
    if(order.order_fulfilled_by){
      const fulfilledBy = typeof order.order_fulfilled_by === 'object' && !Array.isArray(order.order_fulfilled_by) 
        ? order.order_fulfilled_by 
        : (Array.isArray(order.order_fulfilled_by) && order.order_fulfilled_by.length > 0 
          ? order.order_fulfilled_by[0] 
          : null);
      
      if (fulfilledBy) {
        // Map email_id to email if needed
        if (fulfilledBy.email_id && !fulfilledBy.email) {
          fulfilledBy.email = fulfilledBy.email_id;
        }
        setValue('splitInvoice.fulfilled_by', fulfilledBy);
      }
    }
    
    // Initialize split invoice details with zeroed financial values
    const zeroedDetails = { ...order.details };
    zeroedDetails.material_total = "0.00";
    zeroedDetails.total_purchase = "0.00";
    zeroedDetails.deposit = "0.00";
    zeroedDetails.sales_tax = "0.00";
    zeroedDetails.payment_due_date = (dayjs(order.invoice_details.invoice_date).isValid()  && order.details.payment_method !== 'ach_credit') 
      ? dayjs(order.invoice_details.invoice_date).add(30, 'days').format('MMMM D, YYYY') 
      : order.invoice_details.payment_due_date
    
    // If there are other numeric fields that need to be reset to zero, add them here
    if (zeroedDetails.credit !== undefined) zeroedDetails.credit = "0.00";
    if (zeroedDetails.restock_fee !== undefined) zeroedDetails.restock_fee = "0.00";
    if (zeroedDetails.freight_fee !== undefined) zeroedDetails.freight_fee = "0.00";
    
    setValue('splitInvoice.details', zeroedDetails);
    
    // Initialize split invoice line items with same length but empty data
    if (order.items && order.items.length > 0) {
      const emptyLineItems = order.items.map((item:any, index:number) => ({
        description: '',
        description_line_1: '',
        description_other_part: '',
        domestic_material_only: null,
        po_line: item.po_line,
        line_no: index+1,
        product_tag: '',
        qty_type: '',
        price_type: '',
        quantity: '',
        line_weight: '',
        line_weight_unit: '',
        price_per_unit: '',
        extended: '0.00',
        product_id: 0,
        buyer_calculation_price_per_unit: "0",
      }));
      setValue('splitInvoice.lineItems', emptyLineItems);
    }
  }
  // Add function to add active invoice line item to split invoice
  const addLineItemToSplitInvoice = async(line_no: string) => {
    setIsPerformingAction(true);
    setHasOriginalActiveTotalPurchase(false);
    const index = Number(line_no) - 1;
    const activeItem = watch(`activeInvoice.lineItems.${index}`);
    await getProductPriceObj(activeItem);
    if (!activeItem) return;
    
    const currentSplitLineItems = getValues('splitInvoice.lineItems') || [];
    const updatedSplitLineItems = [...currentSplitLineItems];
    
    // Get the original item for quantity reference
    const originalItem = invoiceData?.items[index];
    const originalQuantity = originalItem ? parseFloat(originalItem.quantity) : 0;
    
    // Default the split quantity to the full original quantity
    // This will move the entire line item to the split invoice
    const splitQuantity = originalQuantity;
    const remainingQuantity = 0;
    
    // Create the split item with the original item data but updated quantity
    updatedSplitLineItems[index] = {
      ...activeItem,
      quantity: splitQuantity.toString()
    };
    // Update the extended price for the split invoice item
    const product = productMapping[activeItem?.product_id || 0];
    const splitExtended = (getExtendedPricing('split', splitQuantity, activeItem.qty_type || '', product)).toFixed(2);
    updatedSplitLineItems[index].extended = splitExtended;
    
    // Set the updated line items in the split invoice
    setValue('splitInvoice.lineItems', updatedSplitLineItems);
    if (product && splitQuantity > 0 && product.price.lb) {
      setValue(`splitInvoice.lineItems.${index}.quantity`, watch(`activeInvoice.lineItems.${index}.quantity`));
    
      // Update the extended price for the split invoice item
      setValue(`splitInvoice.lineItems.${index}.price_per_unit`, watch(`activeInvoice.lineItems.${index}.price_per_unit`))
      setValue(`splitInvoice.lineItems.${index}.extended`, watch(`activeInvoice.lineItems.${index}.extended`));
      
      // Update line_weight for split invoice (set to 0 since quantity is 0)
      setValue(`splitInvoice.lineItems.${index}.line_weight`, watch(`activeInvoice.lineItems.${index}.line_weight`));
    }
    // Set the split invoice item quantity to 0
    setValue(`activeInvoice.lineItems.${index}.quantity`, remainingQuantity.toString());
    
    // Update the extended price for the active invoice item
    const activeExtended = (getExtendedPricing('active', remainingQuantity, activeItem.qty_type || '', product)).toFixed(2);
    setValue(`activeInvoice.lineItems.${index}.extended`, activeExtended);
    
    // Update line_weight for active invoice (set to 0 since quantity is 0)
    setValue(`activeInvoice.lineItems.${index}.line_weight`, "0");
    
    // Calculate line_weight for split invoice using product data if available
    calculateActiveOrderTotals();
    calculateOrderTotalPurchase();
  };

  // Format date for display
  const formatDate = (date: any) => {
    if (!date) return '';
    return typeof date === 'object' ? date.toLocaleDateString() : date;
  };

  // Fix handleAddCreditReturn function to properly handle toggling
  const handleAddCreditReturn = (type: 'active' | 'split') => {
    if (type === 'active') {
      const newState = !activeAdjustments.creditReturn;
      setActiveAdjustments({...activeAdjustments, creditReturn: newState});
      
      // Set to 0 when toggling on or off
      const newValue = newState ? "0.00" : "0.00";
      setActiveAdjustmentValues({...activeAdjustmentValues, creditReturn: ''});
      
      // Update total purchase directly
      setTimeout(() => {
        const activeInvoiceDetails = getValues('activeInvoice.details');
        const materialTotal = parseFloat(activeInvoiceDetails.material_total) || 0;
        const salesTax = parseFloat(activeInvoiceDetails.sales_tax) || 0;
        const deposit = parseFloat(activeInvoiceDetails.deposit) || 0;
        const restockFee = parseFloat(activeAdjustmentValues.restockFee) || 0;
        const freightFee = parseFloat(activeAdjustmentValues.freightFee) || 0;
        
        // When turning off credit, ensure it's not subtracted from total
        const credit = newState ? 0 : 0;
        
        // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
        const totalPurchase = materialTotal + salesTax + deposit + restockFee + freightFee - credit;
        setValue('activeInvoice.details.total_purchase', totalPurchase.toFixed(2));
        setHasOriginalActiveTotalPurchase(false);
        
        // Focus on the input field after the update
        if (newState && activeCreditInputRef.current) {
          setTimeout(() => {
            activeCreditInputRef.current?.focus();
          }, 0);
        }
      }, 0);
    } else {
      const newState = !splitAdjustments.creditReturn;
      setSplitAdjustments({...splitAdjustments, creditReturn: newState});
      
      // Set to 0 when toggling on or off
      const newValue = newState ? "0.00" : "0.00";
      setSplitAdjustmentValues({...splitAdjustmentValues, creditReturn: ''});
      
      // Update total purchase directly
      setTimeout(() => {
        const splitInvoiceDetails = getValues('splitInvoice.details');
        const materialTotal = parseFloat(splitInvoiceDetails.material_total) || 0;
        const salesTax = parseFloat(splitInvoiceDetails.sales_tax) || 0;
        const deposit = parseFloat(splitInvoiceDetails.deposit) || 0;
        const restockFee = parseFloat(splitAdjustmentValues.restockFee) || 0;
        const freightFee = parseFloat(splitAdjustmentValues.freightFee) || 0;
        
        // When turning off credit, ensure it's not subtracted from total
        const credit = newState ? 0 : 0;
        
        // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
        const totalPurchase = materialTotal + salesTax + deposit + restockFee + freightFee - credit;
        setValue('splitInvoice.details.total_purchase', totalPurchase.toFixed(2));
        
        // Focus on the input field after the update
        if (newState && splitCreditInputRef.current) {
          setTimeout(() => {
            splitCreditInputRef.current?.focus();
          }, 0);
        }
      }, 0);
    }
  };

  // Fix handleAddRestockFee function to properly handle toggling
  const handleAddRestockFee = (type: 'active' | 'split') => {
    if (type === 'active') {
      const newState = !activeAdjustments.restockFee;
      setActiveAdjustments({...activeAdjustments, restockFee: newState});
      
      // Set to 0 when toggling on or off
      const newValue = newState ? "0.00" : "0.00";
      setActiveAdjustmentValues({...activeAdjustmentValues, restockFee: ''});
      
      // Update total purchase directly
      setTimeout(() => {
        const activeInvoiceDetails = getValues('activeInvoice.details');
        const materialTotal = parseFloat(activeInvoiceDetails.material_total) || 0;
        const salesTax = parseFloat(activeInvoiceDetails.sales_tax) || 0;
        const deposit = parseFloat(activeInvoiceDetails.deposit) || 0;
        const credit = parseFloat(activeAdjustmentValues.creditReturn) || 0;
        const freightFee = parseFloat(activeAdjustmentValues.freightFee) || 0;
        
        // When turning off restock fee, ensure it's not added to total
        const restockFee = newState ? 0 : 0;
        
        // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
        const totalPurchase = materialTotal + salesTax + deposit + restockFee + freightFee - credit;
        setValue('activeInvoice.details.total_purchase', totalPurchase.toFixed(2));
        setHasOriginalActiveTotalPurchase(false);
        
        // Focus on the input field after the update
        if (newState && activeRestockInputRef.current) {
          setTimeout(() => {
            activeRestockInputRef.current?.focus();
          }, 0);
        }
      }, 0);
    } else {
      const newState = !splitAdjustments.restockFee;
      setSplitAdjustments({...splitAdjustments, restockFee: newState});
      
      // Set to 0 when toggling on or off
      const newValue = newState ? "0.00" : "0.00";
      setSplitAdjustmentValues({...splitAdjustmentValues, restockFee: ''});
      
      // Update total purchase directly
      setTimeout(() => {
        const splitInvoiceDetails = getValues('splitInvoice.details');
        const materialTotal = parseFloat(splitInvoiceDetails.material_total) || 0;
        const salesTax = parseFloat(splitInvoiceDetails.sales_tax) || 0;
        const deposit = parseFloat(splitInvoiceDetails.deposit) || 0;
        const credit = parseFloat(splitAdjustmentValues.creditReturn) || 0;
        const freightFee = parseFloat(splitAdjustmentValues.freightFee) || 0;
        
        // When turning off restock fee, ensure it's not added to total
        const restockFee = newState ? 0 : 0;
        
        // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
        const totalPurchase = materialTotal + salesTax + deposit + restockFee + freightFee - credit;
        setValue('splitInvoice.details.total_purchase', totalPurchase.toFixed(2));
        
        // Focus on the input field after the update
        if (newState && splitRestockInputRef.current) {
          setTimeout(() => {
            splitRestockInputRef.current?.focus();
          }, 0);
        }
      }, 0);
    }
  };

  // Fix handleAddFreightFee function to properly handle toggling
  const handleAddFreightFee = (type: 'active' | 'split') => {
    if (type === 'active') {
      const newState = !activeAdjustments.freightFee;
      setActiveAdjustments({...activeAdjustments, freightFee: newState});
      
      // Set to 0 when toggling on or off
      const newValue = newState ? "0.00" : "0.00";
      setActiveAdjustmentValues({...activeAdjustmentValues, freightFee: ''});
      
      // Update total purchase directly
      setTimeout(() => {
        const activeInvoiceDetails = getValues('activeInvoice.details');
        const materialTotal = parseFloat(activeInvoiceDetails.material_total) || 0;
        const salesTax = parseFloat(activeInvoiceDetails.sales_tax) || 0;
        const deposit = parseFloat(activeInvoiceDetails.deposit) || 0;
        const credit = parseFloat(activeAdjustmentValues.creditReturn) || 0;
        const restockFee = parseFloat(activeAdjustmentValues.restockFee) || 0;
        
        // When turning off freight fee, ensure it's not added to total
        const freightFee = newState ? 0 : 0;
        
        // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
        const totalPurchase = materialTotal + salesTax + deposit + restockFee + freightFee - credit;
        setValue('activeInvoice.details.total_purchase', totalPurchase.toFixed(2));
        setHasOriginalActiveTotalPurchase(false);
        
        // Focus on the input field after the update
        if (newState && activeFreightInputRef.current) {
          setTimeout(() => {
            activeFreightInputRef.current?.focus();
          }, 0);
        }
      }, 0);
    } else {
      const newState = !splitAdjustments.freightFee;
      setSplitAdjustments({...splitAdjustments, freightFee: newState});
      
      // Set to 0 when toggling on or off
      const newValue = newState ? "0.00" : "0.00";
      setSplitAdjustmentValues({...splitAdjustmentValues, freightFee: ''});
      
      // Update total purchase directly
      setTimeout(() => {
        const splitInvoiceDetails = getValues('splitInvoice.details');
        const materialTotal = parseFloat(splitInvoiceDetails.material_total) || 0;
        const salesTax = parseFloat(splitInvoiceDetails.sales_tax) || 0;
        const deposit = parseFloat(splitInvoiceDetails.deposit) || 0;
        const credit = parseFloat(splitAdjustmentValues.creditReturn) || 0;
        const restockFee = parseFloat(splitAdjustmentValues.restockFee) || 0;
        
        // When turning off freight fee, ensure it's not added to total
        const freightFee = newState ? 0 : 0;
        
        // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
        const totalPurchase = materialTotal + salesTax + deposit + restockFee + freightFee - credit;
        setValue('splitInvoice.details.total_purchase', totalPurchase.toFixed(2));
        
        // Focus on the input field after the update
        if (newState && splitFreightInputRef.current) {
          setTimeout(() => {
            splitFreightInputRef.current?.focus();
          }, 0);
        }
      }, 0);
    }
  };

  const handleActiveAdjustmentChange = (field: string, value: string) => {
    // Apply numeric validation
    value = allowNumericOnly(value);
    setActiveAdjustmentValues({
      ...activeAdjustmentValues,
      [field]: value
    });
    
    // Update total purchase directly when adjustment values change
    const activeInvoiceDetails = getValues('activeInvoice.details');
    const materialTotal = parseFloat(activeInvoiceDetails.material_total) || 0;
    const salesTax = parseFloat(activeInvoiceDetails.sales_tax) || 0;
    const deposit = parseFloat(activeInvoiceDetails.deposit) || 0;
    
    // Create a temporary object with updated values
    const tempAdjustments = {...activeAdjustmentValues, [field]: value};
    
    // Parse values from temporary object
    const credit = parseFloat(tempAdjustments.creditReturn) || 0;
    const restockFee = parseFloat(tempAdjustments.restockFee) || 0;
    const freightFee = parseFloat(tempAdjustments.freightFee) || 0;
    
    // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
    const totalPurchase = materialTotal + salesTax + deposit + restockFee + freightFee - credit;
    
    // Update the total purchase value
    setValue('activeInvoice.details.total_purchase', totalPurchase.toFixed(2));
    setHasOriginalActiveTotalPurchase(false);
  };
  
  const handleSplitAdjustmentChange = (field: string, value: string) => {
    // Apply numeric validation
    value = allowNumericOnly(value);
    setSplitAdjustmentValues({
      ...splitAdjustmentValues,
      [field]: value
    });
    
    // Update total purchase directly when adjustment values change
    const splitInvoiceDetails = getValues('splitInvoice.details');
    const materialTotal = parseFloat(splitInvoiceDetails.material_total) || 0;
    const salesTax = parseFloat(splitInvoiceDetails.sales_tax) || 0;
    const deposit = parseFloat(splitInvoiceDetails.deposit) || 0;
    
    // Create a temporary object with updated values
    const tempAdjustments = {...splitAdjustmentValues, [field]: value};
    
    // Parse values from temporary object
    const credit = parseFloat(tempAdjustments.creditReturn) || 0;
    const restockFee = parseFloat(tempAdjustments.restockFee) || 0;
    const freightFee = parseFloat(tempAdjustments.freightFee) || 0;
    
    // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
    const totalPurchase = materialTotal + salesTax + deposit + restockFee + freightFee - credit;
    
    // Update the total purchase value
    setValue('splitInvoice.details.total_purchase', totalPurchase.toFixed(2));
  };

  const syncRowHeights = () => {
    setTimeout(() => {
      const activeRows = document.querySelectorAll('.activeInvoiceTable tbody tr');
      const splitRows = document.querySelectorAll('.splitInvoiceTable tbody tr');
      
      // Reset heights first
      [...activeRows, ...splitRows].forEach(row => {
        (row as HTMLElement).style.height = 'auto';
      });
      
      // Sync heights for each row pair
      const maxRows = Math.max(activeRows.length, splitRows.length);
      for (let i = 0; i < maxRows; i++) {
        if (activeRows[i] && splitRows[i]) {
          const activeHeight = (activeRows[i] as HTMLElement).offsetHeight;
          const splitHeight = (splitRows[i] as HTMLElement).offsetHeight;
          const maxHeight = Math.max(activeHeight, splitHeight);
          
          (activeRows[i] as HTMLElement).style.height = `${maxHeight}px`;
          (splitRows[i] as HTMLElement).style.height = `${maxHeight}px`;
        }
      }
    }, 100);
  };

  useEffect(() => {
    // Initial sync
    syncRowHeights();
    
    // Sync on window resize
    window.addEventListener('resize', syncRowHeights);
    
    // Sync when line items change
    const observer = new MutationObserver(syncRowHeights);
    const activeTable = document.querySelector('.activeInvoiceTable tbody');
    const splitTable = document.querySelector('.splitInvoiceTable tbody');
    
    if (activeTable && splitTable) {
      observer.observe(activeTable, { childList: true, subtree: true });
      observer.observe(splitTable, { childList: true, subtree: true });
    }
    
    return () => {
      window.removeEventListener('resize', syncRowHeights);
      observer.disconnect();
    };
  }, []);

  let activeLineItemIndex = 0;
  let splitLineItemIndex = 0;


  // edit line code
const qtyChangeHandler = async (index: number, product:any, qtyVal?: number) => {
  if(product){
      const activeItem = getValues(`activeInvoice.lineItems.${index}`);
      const originalQuantity = Number(invoiceData?.items[index]?.quantity || 0);
      let splitQty = qtyVal ?? +(watch(`splitInvoice.lineItems.${index}.quantity`) ?? 0);
      
      // Calculate remaining active quantity with precision handling
      const remainingActiveQty = (splitQty > originalQuantity) ? 0 : preciseSubtract(originalQuantity, splitQty);
      setValue(`activeInvoice.lineItems.${index}.quantity`, remainingActiveQty.toString());

      await getProductPriceObj(watch(`splitInvoice.lineItems.${index}`));

      // Update active invoice extended price
      const activeExtended = (getExtendedPricing('active', remainingActiveQty, activeItem.qty_type || '', product)).toFixed(2);
      setValue(`activeInvoice.lineItems.${index}.extended`, activeExtended);
      
      // Calculate active invoice line_weight 
      if (remainingActiveQty > 0 && product.price.lb) {
        const activeLineWeight = calculateLineWeight(remainingActiveQty, activeItem.qty_type || '', product);
        setValue(`activeInvoice.lineItems.${index}.line_weight`, activeLineWeight.toFixed(2));
      } else {
        setValue(`activeInvoice.lineItems.${index}.line_weight`, "0");
      }
      
      const qtyUnit = (watch(`splitInvoice.lineItems.${index}.qty_type`) ?? '').toLowerCase();
      const orderIncrement = product[`${orderIncrementPrefix}${qtyUnit === priceUnits.ea ? priceUnits.pc : qtyUnit}`];
      
      if(splitQty){
          const buyerExtendedValue = getExtendedPricing('split', splitQty, qtyUnit, product);
          const lineWeight = calculateLineWeight(splitQty, qtyUnit, product);
          setValue(`splitInvoice.lineItems.${index}.line_weight`, lineWeight.toFixed(2));
          setValue(`splitInvoice.lineItems.${index}.extended`, buyerExtendedValue.toFixed(2));
      }
      else{
          if(splitQty && getFloatRemainder(splitQty, orderIncrement)) setError(`splitInvoice.lineItems.${index}.quantity`,{ message:`Quantity can only be multiples of ${orderIncrement}`});
          setValue(`splitInvoice.lineItems.${index}.line_weight`, "0");
          setValue(`splitInvoice.lineItems.${index}.extended`, "0");
      }
      setMultiplierChangeInBuyerPricing(1);
      priceUnitChangeHandler(product, index, true, 'split');
      calculateUnitPrice("split", 1, index, product);
      setTimeout(()=>{
      // Calculate active invoice material total and total purchase
        calculateActiveOrderTotals();
        calculateOrderTotalPurchase(); // Calculate split invoice totals
      },500)
  }
}
const qtyChangeActiveHandler = async(index: number, product:any, qtyVal?: number) => {
  if(product){
    setIsPerformingAction(true);
    const splitItem = getValues(`splitInvoice.lineItems.${index}`);
    const originalQuantity = Number(invoiceData?.items[index]?.quantity || 0);
    let activeQty = qtyVal ?? +(watch(`activeInvoice.lineItems.${index}.quantity`) ?? 0);
    
    // Calculate remaining split quantity with precision handling
    const remainingSplitQty = (activeQty > originalQuantity) ? 0 : preciseSubtract(originalQuantity, activeQty);
    setValue(`splitInvoice.lineItems.${index}.quantity`, remainingSplitQty.toString());
    await getProductPriceObj(watch(`activeInvoice.lineItems.${index}`));
      
      if(splitItem.description){
        // Update active invoice extended price
        const splitExtended = (getExtendedPricing('split', remainingSplitQty, splitItem.qty_type || '', product)).toFixed(2);
        setValue(`splitInvoice.lineItems.${index}.extended`, splitExtended);
        // Calculate active invoice line_weight 
        if (remainingSplitQty > 0 && product.price.lb) {
          const splitLineWeight = calculateLineWeight(remainingSplitQty, splitItem.qty_type || '', product);
          setValue(`splitInvoice.lineItems.${index}.line_weight`, splitLineWeight.toFixed(2));
        } else {
          setValue(`splitInvoice.lineItems.${index}.line_weight`, "0");
        }
      }
      
      const qtyUnit = (watch(`activeInvoice.lineItems.${index}.qty_type`) ?? '').toLowerCase();
      const orderIncrement = product[`${orderIncrementPrefix}${qtyUnit === priceUnits.ea ? priceUnits.pc : qtyUnit}`];
      
      if(activeQty){
          const activeBuyerExtendedValue = getExtendedPricing('active', activeQty, qtyUnit, product);
          const activeLineWeight = calculateLineWeight(activeQty, qtyUnit, product);
          setValue(`activeInvoice.lineItems.${index}.line_weight`, activeLineWeight.toFixed(2));
          setValue(`activeInvoice.lineItems.${index}.extended`, activeBuyerExtendedValue.toFixed(2));
      }
      else{
          if(activeQty && getFloatRemainder(activeQty, orderIncrement)) setError(`activeInvoice.lineItems.${index}.quantity`,{ message:`Quantity can only be multiples of ${orderIncrement}`});
          setValue(`activeInvoice.lineItems.${index}.line_weight`, "0");
          setValue(`activeInvoice.lineItems.${index}.extended`, "0");
      }
      setMultiplierChangeInBuyerPricing(1);
      priceUnitChangeHandler(product, index, true, 'active');
      calculateUnitPrice("active", 1, index, product);
      setTimeout(()=>{
      // Calculate active invoice material total and total purchase
        calculateActiveOrderTotals();
        calculateOrderTotalPurchase(); // Calculate split invoice totals
      },500)
  }
}

// Add new function to calculate active order totals
const calculateActiveOrderTotals = async () => {
  try {
    const activeLineItems = watch('activeInvoice.lineItems') || [];
    if (activeLineItems.length > 0) {
      // Calculate material total
      let materialTotal = 0;
      let totalWeight = 0;
      
      activeLineItems.forEach((item) => {
        const extended = typeof item.extended === 'string' ? parseFloat(item.extended.replace(/[$,]/g, '')) : 0;
        const lineWeight = typeof item.line_weight === 'string' ? parseFloat(item.line_weight) : 0;
        materialTotal += extended || 0;
        totalWeight += lineWeight || 0;
      });
      
      // Update material total
      setValue('activeInvoice.details.material_total', materialTotal.toFixed(2));
      
      // Calculate sales tax using the same method as split invoice
      const salesTax = await calculateActiveSalesTax(materialTotal) ?? 0;
      
      // Get active invoice details
      const activeInvoiceDetails = getValues('activeInvoice.details');
      
      // Calculate final total purchase directly
      const deposit = activeInvoiceDetails?.deposit ? parseFloat(activeInvoiceDetails.deposit) : 0;
      const credit = parseFloat(activeAdjustmentValues.creditReturn) || 0;
      const restockFee = parseFloat(activeAdjustmentValues.restockFee) || 0;
      const freightFee = parseFloat(activeAdjustmentValues.freightFee) || 0;
      
      // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
      const totalPurchase = materialTotal + parseFloat(salesTax) + deposit + restockFee + freightFee - credit;
      setValue('activeInvoice.details.total_purchase', totalPurchase.toFixed(2));
    }
  } catch (error) {
    console.error("Error calculating active order totals:", error);
  }
};

// Add a function to calculate sales tax for active invoice
const calculateActiveSalesTax = async (matTotal: number) => {
  if (!matTotal) {
    setValue('activeInvoice.details.sales_tax', parseFloat('0').toFixed(2));
    return '0';
  }
  
  setIsSalesTaxCalculating(true);
  
  const taxCounter = salesTaxCounter + 1;
  setSalesTaxCounter(taxCounter);
  
  const activeOrders = watch('activeInvoice.lineItems');
  const payloadData: any = {
    cart_items: [],
    freight_term: "Delivered",
    shipping_details: watch('activeInvoice.shipping_details'),
    price: matTotal.toString(),
    salesTaxCounter: salesTaxCounter + 1,
    buyer_id: order.buyer_id
  };
  
  activeOrders?.forEach((order: any) => {
    if (order?.extended) {
      const item: any = {
        extended: Number((order.extended || '0').toString().replace(/[$,]/g, '')) || 0,
      };
      payloadData.cart_items.push(item);
    }
  });
  
  const payload = {
    data: payloadData,
  };
  
  try {
    const salesResponse = await axios.post(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/order/salesTaxCalculate`, payload);
    const salesTaxCounterResponse = salesResponse.data.data.salesTaxCounter;
    
    if (salesTaxCounterResponse === taxCounter && getValues('activeInvoice.details.material_total')) {
      const taxValue = String(parseFloat(salesResponse.data.data.tax));
      setValue('activeInvoice.details.sales_tax', taxValue);
      return taxValue;
    }
    return '0';
  } catch (error) {
    console.error("Error calculating active sales tax:", error);
    return '0';
  } finally {
    setIsSalesTaxCalculating(false);
  }
};

const powerTo4 = Math.pow(10, 4);
const calculateOrderTotalPurchase = async () => {
  const orders = watch('splitInvoice.lineItems') ?? [];
  if(orders.length > 0){
      let buyerExt = 0;
      orders.forEach((order, index) => {
        if (order && order.extended) {
          buyerExt += getFloorMultipleOfTwo(Number(String(order.extended).replace(/[$,]/g, '')));
        }
      })
      buyerExt = buyerExt / powerTo4;
      setValue('splitInvoice.details.material_total', buyerExt.toFixed(2));
      const salesTax = await calculateSalesTax(buyerExt) ?? 0;
      
      // Calculate total purchase directly
      const splitInvoiceDetails = getValues('splitInvoice.details');
      const materialTotal = buyerExt;
      const deposit = parseFloat(splitInvoiceDetails.deposit) || 0;
      const credit = parseFloat(splitAdjustmentValues.creditReturn) || 0;
      const restockFee = parseFloat(splitAdjustmentValues.restockFee) || 0;
      const freightFee = parseFloat(splitAdjustmentValues.freightFee) || 0;
      
      // Total purchase = material total + sales tax + deposit + restock fee + freight fee - credit
      const totalPurchase = materialTotal + parseFloat(salesTax) + deposit + restockFee + freightFee - credit;
      setValue('splitInvoice.details.total_purchase', totalPurchase.toFixed(2));
  }
}


const calculateSalesTax = async (matTotal: number) => {
  if (!matTotal) {
      setValue('splitInvoice.details.sales_tax', parseFloat('0').toFixed(2));
      return;
  }
  
  setIsSalesTaxCalculating(true);
  
  const taxCounter = salesTaxCounter + 1;
  setSalesTaxCounter(taxCounter)
  const orders = watch('splitInvoice.lineItems');
  const payloadData:any = {
      cart_items: [],
      freight_term: "Delivered",
      shipping_details: watch('splitInvoice.shipping_details'),
      price: matTotal.toString(),
      salesTaxCounter: salesTaxCounter + 1,
      buyer_id: order.buyer_id
  };
  orders?.forEach((order:any, index: number) =>{
    const item:any = {
        extended : Number(order.extended.replace(/[$,]/g, '')) ??0,
    };
    payloadData.cart_items.push(item);
  })
  const payload = {
      data: payloadData,
  };
  
  try {
    const salesResponse = await axios.post(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/order/salesTaxCalculate`, payload);
    const salesTaxCounterResponse = salesResponse.data.data.salesTaxCounter;
    if (salesTaxCounterResponse === taxCounter && watch('splitInvoice.details.material_total')) {
      setValue('splitInvoice.details.sales_tax', String(parseFloat(salesResponse.data.data.tax)));
      return salesResponse.data.data.tax;
    }
  } catch (error) {
    console.error("Error calculating sales tax:", error);
  } finally {
    setIsSalesTaxCalculating(false);
  }
}

const getFloorMultipleOfTwo = (value: number | null | undefined): number => {
  if(value){
      const precisionMultiple = value * powerTo4;
      const floorValue = Math.floor(precisionMultiple);
      return floorValue;
  }
  else return 0;
}
const priceUnitChangeHandler = (product: any, index: number, resetMulti?: boolean, type?: string) => {
  const buyer_multiplier = resetMulti ? 1 :multiplierChangeInBuyerPricing;
  if(type === 'active'){
    const activePriceUnit = watch((`activeInvoice.lineItems.${index}.price_type`)) ?? null;
    const precision = activePriceUnit?.toLowerCase() === priceUnits.lb? 4 : 2;
    const activeBuyerPrice = getProductPricePerUnit(type, product, activePriceUnit);
    setValue(`activeInvoice.lineItems.${index}.price_per_unit`, (activeBuyerPrice * buyer_multiplier).toFixed(precision));
    setValue(`splitInvoice.lineItems.${index}.price_per_unit`, (activeBuyerPrice * buyer_multiplier).toFixed(precision));
  }
  else{
    const priceUnit = watch((`splitInvoice.lineItems.${index}.price_type`)) ?? null;
    const precision = priceUnit?.toLowerCase() === priceUnits.lb ? 4 : 2;
    const buyerPrice = getProductPricePerUnit('split', product, priceUnit);
    setValue(`splitInvoice.lineItems.${index}.price_per_unit`, (buyerPrice * buyer_multiplier).toFixed(precision));
    setValue(`activeInvoice.lineItems.${index}.price_per_unit`, (buyerPrice * buyer_multiplier).toFixed(precision));
  }
}

const calculateUnitPrice = (type: string, changeOfValueInPercentageMultiplier: number, index: number, _product: any)=>{
  const qtyUnit = (type === 'active') ? watch((`activeInvoice.lineItems.${index}.qty_type`)) ?? null : watch((`splitInvoice.lineItems.${index}.qty_type`)) ?? null;
  const target = 'buyer_calculation_price_per_unit';
  // Use type assertion to fix TypeScript error
  if(type === 'active'){
    const calcPricePerUnit = getProductPricePerUnit(type, _product, qtyUnit);
    setValue(`activeInvoice.lineItems.${index}.${target}` as any, +(calcPricePerUnit * changeOfValueInPercentageMultiplier).toFixed(2));
  }
  else{
    const calcPricePerUnit = getProductPricePerUnit(type, _product, qtyUnit);
    setValue(`splitInvoice.lineItems.${index}.${target}` as any, +(calcPricePerUnit * changeOfValueInPercentageMultiplier).toFixed(2));
  }
}

const getProductPricePerUnit = (type: string, product: any, priceUnit: string | null)=> {
  // const postUnit = getUnitPostScript(priceUnit ?? "");
  return product.price[priceUnit?.toLowerCase() ?? ''];
}



const getExtendedPricing = (type: string, qty: number, qtyUnit: string, product: any) => {
  const pricePerQtyUnit =  product.price[qtyUnit.toLowerCase()];
  return qty * pricePerQtyUnit;
}


  const onPriceChange = (target: 'activeInvoice.lineItems' | 'splitInvoice.lineItems', value: number, index: number, product: any) => {
    
    const qty = Number(watch(`${target}.${index}.quantity`));
    const qtyUnit = watch(`${target}.${index}.qty_type`) ?? '';
    const type = target === 'activeInvoice.lineItems' ? 'active' : 'split';
    if (qty) {
      const actualPricePerUnit = getProductPricePerUnit(type, product, watch((`${target}.${index}.price_type`)) || null);
      const changeOfValueInPercentageMultiplier = (value / actualPricePerUnit);
      setMultiplierChangeInBuyerPricing(changeOfValueInPercentageMultiplier);
      const actualExtendedPricing = getExtendedPricing(type, qty, qtyUnit, product);
      setValue(`${target}.${index}.extended`, (actualExtendedPricing * changeOfValueInPercentageMultiplier).toFixed(2));
      //calculate price per according to selected qty unit unit for server calculation
      calculateUnitPrice(type, changeOfValueInPercentageMultiplier, index, product);
      // setTimeout(() => {
        // Calculate active invoice material total and total purchase
        calculateActiveOrderTotals();
        calculateOrderTotalPurchase(); // Calculate split invoice totals
      // }, 500)
    }
  }
  
  const sendEmailClickHandler = (_data: any, sendEmailFromSplitInvoice: boolean) => {
    setShowConfirmationPopup(true);
    setSendEmail(sendEmailFromSplitInvoice)
  };
  
  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
  };

  const confirmationPopupYes = handleSubmit((data) => {
    // Handle split PO logic here
    let payload: any = {
      "data": {
          "po_number": data.activeInvoice.invoice_details.buyer_po_number,
          "left_panel": {
              "invoice_details": {
                  "invoice_number": data.activeInvoice.invoice_details.invoice_number,
                  "invoice_date": data.activeInvoice.invoice_details.invoice_date,
                  "payment_due_date": data.activeInvoice.invoice_details.payment_due_date
              },
              "items": data.activeInvoice.lineItems?.map((item: any, index: number) => {
                const qty = item.quantity.includes('.') ? Number(item.quantity).toFixed(2): item.quantity;
                const qtyUnit = item.qty_type.toUpperCase();
                const priceUnit = item.price_type.toUpperCase();

                return { 
                  "product_id": item.product_id,
                  // "description": item.description,
                  "domestic_material_only": item.domestic_material_only,
                  "product_tag": item.product_tag,
                  "po_line": item.po_line,
                  qty,
                  "qty_unit": qtyUnit,
                  "buyer_price_per_unit": removeCommaFromCurrency(item.price_per_unit.toString()),
                  "buyer_calculation_price_per_unit": item.buyer_calculation_price_per_unit || '0',
                  "price_unit": priceUnit,
                  "line_total_weight": removeCommaFromCurrency(item.line_weight),
                  "buyer_line_total": removeCommaFromCurrency(item.extended)
              }}),
              "details": {
                  "credit": activeAdjustmentValues.creditReturn || null,
                  "freight_fee": activeAdjustmentValues.freightFee || null,
                  "restock_fee": activeAdjustmentValues.restockFee || null
              },
              "send_email": !sendEmail ? 1 : 0
          },
          "right_panel": {
              "invoice_details": {
                  "invoice_number": data.splitInvoice.invoice_details.invoice_number,
                  "invoice_date": data.splitInvoice.invoice_details.invoice_date,
                  "payment_due_date": data.splitInvoice.invoice_details.payment_due_date
              },
              "order_fulfilled_by": {
                  "seller_company_name": data.splitInvoice.fulfilled_by.company_name?.trim() || "",
                  "seller_first_name": data.splitInvoice.fulfilled_by.name?.split(" ")[0] ? data.splitInvoice.fulfilled_by.name?.split(" ")[0]?.trim() : "",
                  "seller_last_name": data.splitInvoice.fulfilled_by.name?.split(" ")[1] ? data.splitInvoice.fulfilled_by.name?.split(" ")[1]?.trim() : "",
                  "seller_email_id": data.splitInvoice.fulfilled_by.email?.trim() || undefined,
                  "address": {
                      "line1": data.splitInvoice.fulfilled_by.address.line1?.trim() || "",
                      "line2": data.splitInvoice.fulfilled_by.address.line2?.trim() || "",
                      "city": data.splitInvoice.fulfilled_by.address.city?.trim() || "",
                      "state": data.splitInvoice.fulfilled_by.address.state?.trim() || "",
                      "zip": data.splitInvoice.fulfilled_by.address.zip?.trim() || ""
                  }
              },
              "items": data.splitInvoice.lineItems?.filter(item => item.description)
                .map((item: any, index: number) => { 
                  const qtyUnit = item.qty_type.toUpperCase();
                  const priceUnit = item.price_type.toUpperCase();
                  return {
                  "product_id": item.product_id,
                  // "description": item.description,
                  "domestic_material_only": item.domestic_material_only,
                  "product_tag": item.product_tag,
                  "po_line": item.po_line,
                  "qty": item.quantity === 'NaN' ? '' : item.quantity.includes('.') ? Number(item.quantity).toFixed(2): item.quantity,
                  "qty_unit": qtyUnit,
                  "buyer_price_per_unit": removeCommaFromCurrency(item.price_per_unit.toString()),
                  "buyer_calculation_price_per_unit": item.buyer_calculation_price_per_unit || '0',
                  "price_unit": priceUnit,
                  "line_total_weight": removeCommaFromCurrency(item.line_weight),
                  "buyer_line_total": removeCommaFromCurrency(item.extended)
              }}),
              "details": {
                  "credit": splitAdjustmentValues.creditReturn || null,
                  "freight_fee": splitAdjustmentValues.freightFee || null,
                  "restock_fee": splitAdjustmentValues.restockFee || null,
              },
              "send_email": sendEmail ? 1 : 0
          }
      }
  }

  if(typeof invoiceData.order_fulfilled_by === 'object' && !Array.isArray(invoiceData.order_fulfilled_by) ){
    payload.data.left_panel.order_fulfilled_by = {
      "seller_company_name": data.activeInvoice.fulfilled_by.company_name || "",
      "seller_first_name": data.activeInvoice.fulfilled_by.name?.split(" ")[0] || "",
      "seller_last_name": data.activeInvoice.fulfilled_by.name?.split(" ")[1] || "",
      "seller_email_id": data.activeInvoice.fulfilled_by.email || "",
      "address": {
        "line1": data.activeInvoice.fulfilled_by.address.line1 || "",
        "line2": data.activeInvoice.fulfilled_by.address.line2 || "",
        "city": data.activeInvoice.fulfilled_by.address.city || "",
        "state": data.activeInvoice.fulfilled_by.address.state || "",
        "zip": data.activeInvoice.fulfilled_by.address.zip || ""
      }
    }
  }

  if(!sendEmail){
    payload.data.right_panel = undefined;
  }


  postSplitInvoiceData(payload);
  console.log("Split PO submitted:", data, payload);

    confirmationPopupClose();
    // Add your API call or processing logic here
  });


  // Add scroll synchronization
useEffect(() => {
  const activeContainer = activeInvoiceRef.current;
  const splitContainer = splitInvoiceRef.current;
  
  if (!activeContainer || !splitContainer) return;
  
  const syncScroll = (e: Event) => {
    const source = e.target as HTMLDivElement;
    const target = source === activeContainer ? splitContainer : activeContainer;
    
    // Prevent infinite loop
    if (source.scrollTop !== target.scrollTop) {
      target.removeEventListener('scroll', syncScroll);
      target.scrollTop = source.scrollTop;
      setTimeout(() => {
        target.addEventListener('scroll', syncScroll);
      }, 10);
    }
  };
  
  activeContainer.addEventListener('scroll', syncScroll);
  splitContainer.addEventListener('scroll', syncScroll);
  
  return () => {
    activeContainer.removeEventListener('scroll', syncScroll);
    splitContainer.removeEventListener('scroll', syncScroll);
  };
}, []);

  // Remove split invoice line item
  const removeSplitLineItem = (index: number) => {
    // Get current line items
    const currentSplitLineItems = getValues('splitInvoice.lineItems') || [];
    const updatedSplitLineItems = [...currentSplitLineItems];
    
    // Get the active item and split item
    const activeItem = getValues(`activeInvoice.lineItems.${index}`);
    const splitItem = getValues(`splitInvoice.lineItems.${index}`);
    
    if (!activeItem || !splitItem) return;
    
    // Reset the split item to empty
    updatedSplitLineItems[index] = {
      ...splitItem,
      description: '',
      description_line_1: '',
      description_other_part: '',
      domestic_material_only: null,
      quantity: '',
      line_weight: '',
      price_per_unit: '',
      extended: '0.00',
    };
    
    // Set the updated line items in the split invoice
    setValue('splitInvoice.lineItems', updatedSplitLineItems);
    
    // Get the original quantity from tracked original quantities
    const originalQuantity = parseFloat(originalQuantities[index] || '0');
    
    // Update the active invoice item quantity back to original
    setValue(`activeInvoice.lineItems.${index}.quantity`, invoiceData.items[index].quantity);
    setValue(`activeInvoice.lineItems.${index}.extended`, invoiceData.items[index].extended);
    setValue(`activeInvoice.lineItems.${index}.price_per_unit`, invoiceData.items[index].price_per_unit);
    setValue(`activeInvoice.lineItems.${index}.line_weight`, invoiceData.items[index].line_weight);
    
    // Recalculate totals
    calculateActiveOrderTotals();
    calculateOrderTotalPurchase();
  };

  // Function to handle opening the invoice history popup
  const handleOpenInvoiceHistory = async () => {
    setInvoiceHistory(invoiceData.invoice_history);
    // Show the popup
    setShowInvoiceHistoryPopup(true);
  };

  
  return (
    <>
       <div className={styles.backButtonContainer}>
        <button className={styles.backButton} onClick={onClose}><LeftArrow/>Back to Order List</button>
        <button 
          className={styles.invoiceHistoryButton} 
          onClick={handleOpenInvoiceHistory}
        >
          Invoice History
        </button>
      </div>
      {(isInvoiceDataLoading || isPostSplitInvoiceDataLoading || isLoadingHistory) && <div className={clsx(styles.loaderImg, 'loaderImg', styles.overlay)}><Loader /></div>}
      
      {/* Invoice History Popup */}
      <MatPopup
        className={styles.invoiceHistoryPopup}
        open={showInvoiceHistoryPopup}
        disablePortal
        classes={{
          paper: styles.invoiceHistoryPopupPaper
        }}
      >
        <div className={styles.invoiceHistoryContainer}>
          <div className={styles.invoiceHistoryHeader}>
            <h3>Invoice History</h3>
            <button 
              className={styles.closePopupButton} 
              onClick={() => setShowInvoiceHistoryPopup(false)}
            >
              <CloseIcon />
            </button>
          </div>
          <div className={styles.invoiceHistoryContent}>
            <div className={styles.tblscroll}>
              <table className={styles.invoiceHistoryTable}>
                <thead>
                  <tr>
                    <th>File Name</th>
                    <th>File Type</th>
                    <th>Sent Date</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {invoiceHistory && invoiceHistory.length > 0 ? (
                    invoiceHistory.map((item: any, index: number) => (
                      <tr key={index}>
                        <td title={item.file_name}>{item.file_name}</td>
                        <td>{item.file_type || 'Unknown'}</td>
                        <td>{item.sent_date || 'N/A'}</td>
                        <td>
                          <button 
                            className={styles.downloadButton}
                            onClick={() => handleFileDownload(item.file_url, item.file_name)}
                          >
                            Download
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={4} className={styles.noHistoryMessage}>
                        No invoice history found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </MatPopup>
      
       <div className={styles.splitPoContainer}>
   
      <div className={styles.invoiceContainer}>
        <div className={styles.invoiceColumn}>
          <div className={styles.columnHeader}>
            <h3>Active Order/Backorder</h3>
            <button 
              className={styles.emailButton} 
              onClick={handleSubmit((data) => sendEmailClickHandler(data, false))}
              disabled={(disableActiveSendButton || (Number(watch('activeInvoice.details.total_purchase')) <= 0 ))}
            >
              Email Invoice Complete
            </button>
          </div>
          <div className={clsx(styles.invoiceBoxMain,styles.noScrollBar)} ref={activeInvoiceRef}>
          {splitDataByPage.map((maxItems, pageNumber)=>{
            let itemCount = maxItems;
            return (
              <div className={styles.invoiceBox}>
            {pageNumber === 0 && <><div className={styles.invoiceHeader}>
              <div className={styles.logoSection}>
                <div className={styles.logo}>
                  <img src={Logo} alt="BRYZOS" />
                </div>
                <p className={styles.paymentNote}>PAYMENT SHALL BE MADE PER THE INSTRUCTIONS ON THIS INVOICE.</p>
              </div>
              <div className={styles.invoiceDetailsSection}>
                <div className={styles.invoiceTitle}>INVOICE</div>
                <div className={styles.invoiceDetails}>
                  <div className={styles.invoiceRow}>
                    <span>Bryzos Invoice#</span>
                    {watch("activeInvoice.invoice_details.invoice_number")}
                  </div>
                  <div className={styles.invoiceRow}>
                    <span>Bryzos Sales Order#</span>
                    {watch("activeInvoice.invoice_details.buyer_po_number")}
                  </div>
                  <div className={styles.invoiceRow}>
                    <span>Buyer PO#</span>
                    {watch("activeInvoice.invoice_details.buyer_internal_po_number")}
                  </div>
                  {/* <div className={styles.invoiceRow}>
                    <span>Version</span>
                    {watch("activeInvoice.invoice_details.invoice_version")}
                  </div> */}
                  <div className={styles.invoiceRow}>
                    <span>Invoice Date</span>
                    {formatDate(watch("activeInvoice.invoice_details.invoice_date"))}
                  </div>
                  <div className={styles.invoiceRow}>
                    <span className={styles.paymentDueDate}>Payment Due Date</span>
                    <span className={styles.paymentDueDateValue}>{formatDate(watch("activeInvoice.invoice_details.payment_due_date"))}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className={styles.billingSection}>
              <div className={styles.fulfilledByContainer}>
                <div className={styles.billTo}>
                  <h4>BILL TO</h4>
                  <p>{watch("activeInvoice.bill_to.company_name")}</p>
                  <p>{watch("activeInvoice.bill_to.name")}</p>
                  <p>{watch("activeInvoice.bill_to.address.line1")}</p>
                  <p>{watch("activeInvoice.bill_to.address.city")}, {watch("activeInvoice.bill_to.address.state")}, {watch("activeInvoice.bill_to.address.zip")}</p>
                  <p>{watch("activeInvoice.bill_to.email")}</p>
                </div>
                <div className={styles.fulfilledBy}>
                  <h4>FULFILLED BY</h4>
                  {watch("activeInvoice.fulfilled_by") && (
                    <>
                      <p>{watch("activeInvoice.fulfilled_by.company_name")}</p>
                      <p>{watch("activeInvoice.fulfilled_by.name")}</p>
                      <p>{watch("activeInvoice.fulfilled_by.address.line1")}</p>
                      <p>{watch("activeInvoice.fulfilled_by.address.city")}, {watch("activeInvoice.fulfilled_by.address.state")}, {watch("activeInvoice.fulfilled_by.address.zip")}</p>
                      <p>{watch("activeInvoice.fulfilled_by.email")}</p>
                    </>
                  )}
                </div>
              </div>
              
              <div className={styles.paymentInstructionsBox}>
                <h4>PAYMENT INSTRUCTIONS</h4>
                <div className={styles.paymentMethodRow}>
                  <span>ACH or Wire -&nbsp;</span>
                  <span className={styles.noChecks}>NO CHECKS</span>
                  <span className={styles.greenArrow}>
                   <img src={GreenArrow} alt="Green Arrow" />
                  </span>
                </div>
                <div className={styles.paymentDetails}>
                  <div className={styles.paymentRow}>
                    <span>Seller Name:</span>
                    <span>{watch("activeInvoice.payment_instructions.company_name")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Bank Name:</span>
                    <span>{watch("activeInvoice.payment_instructions.bank_name")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Account Name:</span>
                    <span>{watch("activeInvoice.payment_instructions.account_name")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Account Number:</span>
                    <span>{watch("activeInvoice.payment_instructions.account_number")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Routing Number:</span>
                    <span>{watch("activeInvoice.payment_instructions.routing_number")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Bank Address:</span>
                    <span>{watch("activeInvoice.payment_instructions.bank_address")}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className={styles.paymentInfo}>
              <h3>$ {formatValue(watch("activeInvoice.details.total_purchase"))} due {formatDate(watch("activeInvoice.details.payment_due_date"))}</h3>
            </div>
            </>}

            <div className={styles.lineItems}>
              {pageNumber===0 && <h4>LINE ITEM DETAIL</h4>}
              <table className={`${styles.itemTable} activeInvoiceTable`}>
                <thead>
                  <tr>
                    <th>LN</th>
                    <th>Description</th>
                    <th>Qty</th>
                    <th>$ / Unit</th>
                    <th>EXT ($)</th>
                  </tr>
                </thead>
                <tbody>
                  {watch("activeInvoice.lineItems")?.map((_item: any, index: number) => {
                    if(itemCount>0){
                      itemCount--;
                      const activeItems = watch("activeInvoice.lineItems");
                      const item = activeItems ? activeItems[activeLineItemIndex] : undefined;
                      const newIndex = item ? Number(item?.line_no) - 1 : -1;
                      activeLineItemIndex++;
                      if(!item) return <></>
                      return(
                        <tr key={index}>
                          <td>{item.po_line}</td>
                          <td>
                            <p><strong>{item.product_tag}</strong></p>
                            <div>{item.description.split('\n').map(
                                  (desc:string)=>{
                                    return <p>{desc}</p>
                                  }
                                )}</div>
                            {!!(item.domestic_material_only) && <p className={styles.domesticMaterialOnly}>Domestic (USA) Material Only</p>}
                          </td>
                          
                        <td>
                          <div className={styles.editInputTd}>
                            <input 
                              type="text"
                              {...register(`activeInvoice.lineItems.${newIndex}.quantity`, { valueAsNumber: true })}
                              onChange={(e) => {
                                setHasLineItemStateChanged(e.target.value.length > 0);
                                e.target.value = allowNumericOnly(e.target.value);
                                qtyChangeActiveHandler(newIndex, productMapping[item?.product_id ?? 0], Number(e.target.value));
                                register(`activeInvoice.lineItems.${newIndex}.quantity`).onChange(e);
                              }}
                              className={styles.adjustmentInput}
                            />
                            {item.qty_type?.toUpperCase()}
                          </div>
                          <div>{item.line_weight} {item.line_weight_unit}</div>
                        </td>
                        <td>
                            <div className={styles.priceUnitInputEditMain}>
                              <div className={styles.priceUnitInputEdit}><span>$</span> <input 
                        type="text"
                        {...register(`activeInvoice.lineItems.${newIndex}.price_per_unit`)}
                        onChange={(e) => {
                          setHasLineItemStateChanged(e.target.value.length > 0);
                          const numericValue = allowNumericOnly(e.target.value);
                          onPriceChange('activeInvoice.lineItems', Number(numericValue), newIndex, productMapping[item?.product_id ?? 0]);
                          setValue(`activeInvoice.lineItems.${newIndex}.price_per_unit`, numericValue);
                        }}
                        className={styles.adjustmentInput}
                      /></div> <span className={styles.priceUnitEdit}> / {item.price_type?.toUpperCase()}</span></div>
                         </td>
                          {/* <td>
                            <div>
                              <span style={{ 
                              color: originalQuantities[activeLineItemIndex-1] !== item.quantity ? '#ff0000' : 'inherit'
                            }}>{item.quantity}</span> {item.qty_type}
                            </div>
                            <div>{item.line_weight} {item.line_weight_unit}</div>
                          </td>
                          <td>$ {item.price_per_unit} / {item.price_type}</td> */}
                          <td>$ {formatValue(item.extended)}</td>
                        </tr>
                      )
                    }
                    return <></>
                    })}
                </tbody>
                
              </table>
              {pageNumber+1 === splitDataByPage.length && <table className={styles.materialTotalTable}>
                  <tr>
                    <th></th>
                    <th></th>
                    <th className={styles.materialTotal}>Material Total:</th>
                    <th>$</th>
                    <th>{formatValue(watch("activeInvoice.details.material_total"))}</th>
                  </tr>
                </table>}
                
            </div>
            
            {pageNumber+1 === splitDataByPage.length && <div className={styles.invoiceFooter}>
              <div className={styles.note}>
                 {watch('splitInvoice.details.payment_method') !== 'ach_credit' ? <>
                    <p className={styles.paymentNote1}>NOTE: Payment made by check will be considered non-payment. Remit payment via ACH or wire per the instructions on this invoice. Please note that under no circumstances will Bryzos be party to any third-party contracts, such as lien waivers.</p>
                 </> :
                  <div className={styles.paymentNote2}> 
                        <p>
                          <b>DISCLAIMER :</b> This order requires a deposit due to the Cash In Advance payment method. 
                          In the event you receive a higher quantity than ordered, the deposit is estimated to cover any 
                          additional costs incurred upon shipment. Any funds not used for additional costs will be refunded to you following order completion.
                        </p>
                        <p>
                          <span>This order cannot be processed for fulfillment until Total Purchase amount of funds has been received by Bryzos. </span> 
                          If funds have not been received within 72 hours of the checkout date, this order will be cancelled.
                        </p>
                        <p>
                          NOTE: Payment made by check will be considered non-payment. Remit payment via ACH or wire per the instructions on this invoice. Please note that under no circumstances will Bryzos be party to any third-party contracts, such as lien waivers.
                        </p>
                  </div>
                  }
              
              </div>
              <div className={styles.totals}>
                 <table>
                    {activeAdjustments.creditReturn && (
                      <tr>
                        <td>Credit</td>
                        <td>$</td>
                        <td>
                          <input
                            type="text"
                            value={activeAdjustmentValues.creditReturn}
                            onChange={(e) => {
                              handleActiveAdjustmentChange('creditReturn', allowNumericOnly(e.target.value));
                            }}
                            className={styles.adjustmentInput}  
                            style={{ color: '#ff0000' }}
                            ref={activeCreditInputRef}
                          />
                        </td>
                      </tr>
                    )}
                    {activeAdjustments.restockFee && (
                      <tr>
                        <td>Restock Fee</td>
                        <td>$</td>
                        <td>
                          <input
                            type="text"
                            value={activeAdjustmentValues.restockFee}
                            onChange={(e) => {
                              handleActiveAdjustmentChange('restockFee', allowNumericOnly(e.target.value));
                            }}
                            className={styles.adjustmentInput}
                            ref={activeRestockInputRef}
                          />
                        </td>
                      </tr>
                    )}
                    {activeAdjustments.freightFee && (
                      <tr>
                        <td>Freight Fee</td>
                        <td>$</td>
                        <td>
                          <input
                            type="text"
                            value={activeAdjustmentValues.freightFee}
                            onChange={(e) => {
                              handleActiveAdjustmentChange('freightFee', allowNumericOnly(e.target.value));
                            }}
                            className={styles.adjustmentInput}
                            ref={activeFreightInputRef}
                          />
                        </td>
                      </tr>
                    )}
                  <tr>
                    <td>Sales Tax:</td>
                    <td>$</td>
                    <td>{watch("activeInvoice.details.sales_tax") === 'Exempt' ? '0.00' : formatValue(watch("activeInvoice.details.sales_tax"))}</td>
                  </tr>
                  {watch("activeInvoice.details.payment_method") === "ach_credit" && (
                    <tr>
                      <td>Deposit:</td>
                      <td>$</td>
                      <td>{formatValue(watch("activeInvoice.details.deposit")??'')}</td>
                    </tr>
                  )}
                  <tr>
                    <td>Total Purchase</td>
                    <td>$</td>
                    <td>{formatValue(watch("activeInvoice.details.total_purchase"))}</td>
                  </tr>
                </table>
                 <div className={styles.taxExemption}>
                <p>A valid sales tax exemption certificate must be presented to Bryzos prior to purchase to honor a sales tax exemption.</p>
              </div>
              </div>
             
            </div>}
            <div className={styles.pageNumber}>{pageNumber+1} of {splitDataByPage.length}</div>
          </div>
            )
          })}
          </div>
          
          
          <div className={styles.adjustments}>
            <span>Invoice Ledger Adjustments:</span>
            <button 
              className={styles.adjustmentButton} 
              type="button" 
              onClick={() => handleAddCreditReturn('active')}
            >
              + Add Credit for Return
            </button>
            <button 
              className={styles.adjustmentButton} 
              type="button" 
              onClick={() => handleAddRestockFee('active')}
            >
              + Add Restock Fee
            </button>
            <button 
              className={styles.adjustmentButton} 
              type="button" 
              onClick={() => handleAddFreightFee('active')}
            >
              + Add Freight Fee
            </button>
          </div>
        </div>
        
        <div className={styles.invoiceColumn}>
          <div className={styles.columnHeader}>
            <h3>Manually Split Invoice</h3>
            <button className={styles.emailButton} onClick={handleSubmit((data) => sendEmailClickHandler(data, true))} disabled={(disableEmailSendButton || Number(watch('splitInvoice.details.total_purchase')) <= 0 )} >Email Split Invoice</button>
          </div>
          <div className={styles.invoiceBoxMain} ref={splitInvoiceRef}>
          {splitDataByPage.map((maxItems, pageNumber)=>{
            let itemCount = maxItems;
            return (
          <div className={styles.invoiceBox}>
            {pageNumber === 0 && <><div className={styles.invoiceHeader}>
              <div className={styles.logoSection}>
                <div className={styles.logo}>
                  <img src={Logo} alt="BRYZOS" />
                </div>
                <p className={styles.paymentNote}>PAYMENT SHALL BE MADE PER THE INSTRUCTIONS ON THIS INVOICE.</p>
              </div>
              <div className={styles.invoiceDetailsSection}>
                <div className={styles.invoiceTitle}>INVOICE</div>
                <div className={styles.invoiceDetails}>
                  <div className={styles.invoiceRow}>
                    <span>Bryzos Invoice#</span>
                    <input type="text" className={styles.editInvoiceInput} {...register("splitInvoice.invoice_details.invoice_number")} />
                  </div>
                  <div className={styles.invoiceRow}>
                    <span>Bryzos Sales Order#</span>
                    <span>{watch("splitInvoice.invoice_details.buyer_po_number")}</span>
                  </div>
                  <div className={styles.invoiceRow}>
                    <span>Buyer PO#</span>
                    <span>{watch("splitInvoice.invoice_details.buyer_internal_po_number")}</span>
                  </div>
                  {/* <div className={styles.invoiceRow}>
                    <span>Version</span>
                    <span>{watch("splitInvoice.invoice_details.invoice_version")}</span>
                  </div> */}
                  <div className={clsx(styles.invoiceRow,'datePickerSplitInvoice')}>
                    <span>Invoice Date</span>
                      <Controller
                        control={control}
                        rules={{
                          required: true,
                        }}
                        name={register(`splitInvoice.invoice_details.invoice_date`).name}
                        render={({ field: { value, onChange }, fieldState: { error } }) => {
                          const datePickerValue = watch(`splitInvoice.invoice_details.invoice_date`) ? dayjs(watch(`splitInvoice.invoice_details.invoice_date`)) : null;
                          return (
                            <CustomDatePicker
                              // disableDates={disableDates}
                              value={datePickerValue}
                              onChange={(newValue) => {
                                // customDatePickerOnChange(newValue, i, watch(`splitInvoice.invoice_details.invoice_date`));
                                onChange(newValue?.format('M/D/YY'));
                                const paymentDueDate = dayjs(newValue).add(30, 'days');
                                setValue(`splitInvoice.invoice_details.payment_due_date`, paymentDueDate.format('M/D/YY'));
                                setValue(`splitInvoice.details.payment_due_date`, paymentDueDate.format('MMMM D, YYYY'));
                              }}
                              format={'M/D/YY'}
                              // openOnFocus={i === calendarInFocusIndex}
                              // onClose={() => { setCalendarInFocusIndex(null) }}
                              // minDate={dayjs()}
                               />
                          )
                        }}
                      />
                    {/* <input type="date" className={styles.editInvoiceInput} {...register("splitInvoice.invoice_details.invoice_date")} /> */}
                    {/* {errors.splitInvoice?.invoice_details?.invoice_date && <span className={styles.errorMessage}>{errors.splitInvoice.invoice_details.invoice_date.message}</span>} */}
                  </div>
                 <div className={clsx(styles.invoiceRow,'datePickerSplitInvoice')}>
                    <span className={styles.paymentDueDate}>Payment Due Date</span>
                      <Controller
                        control={control}
                        rules={{
                          required: true,
                        }}
                        name={register(`splitInvoice.invoice_details.payment_due_date`).name}
                        render={({ field: { value, onChange }, fieldState: { error } }) => {
                          const datePickerValue = watch(`splitInvoice.invoice_details.payment_due_date`) ? dayjs(watch(`splitInvoice.invoice_details.payment_due_date`)) : null;
                          return (
                            <CustomDatePicker
                              // disableDates={disableDates}
                              value={datePickerValue}
                              onChange={(newValue) => {
                                // customDatePickerOnChange(newValue, i, watch(`splitInvoice.invoice_details.invoice_date`));
                                if (newValue) {
                                  onChange(newValue.format('M/D/YY'));
                                  setValue(`splitInvoice.details.payment_due_date`, newValue.format('MMMM D, YYYY'));
                                }
                              }}
                              format={'M/D/YY'}
                              // openOnFocus={i === calendarInFocusIndex}
                              // onClose={() => { setCalendarInFocusIndex(null) }}
                              // minDate={dayjs()}
                               />
                          )
                        }}
                      />
                    {/* <input type="date" className={clsx(styles.editInvoiceInput,styles.paymentDueInput)} {...register("splitInvoice.invoice_details.payment_due_date")} /> */}
                    {/* {errors.splitInvoice?.invoice_details?.payment_due_date && <span className={styles.errorMessage}>{errors.splitInvoice.invoice_details.payment_due_date.message}</span>} */}
                  </div>
                </div>
              </div>
            </div>
            
            <div className={styles.billingSection}>
              <div className={styles.fulfilledByContainer}>
           <div className={styles.billTo}>
                <h4>BILL TO</h4>
                <p>{watch("splitInvoice.bill_to.company_name")}</p>
                <p>{watch("splitInvoice.bill_to.name")}</p>
                <p>{watch("splitInvoice.bill_to.address.line1")}</p>
                <p>{watch("splitInvoice.bill_to.address.city")}, {watch("splitInvoice.bill_to.address.state")}, {watch("splitInvoice.bill_to.address.zip")}</p>
                <p>{watch("splitInvoice.bill_to.email")}</p>
              </div>
              <div className={styles.fulfilledBy}>
                <h4>FULFILLED BY</h4>
                  <div className={styles.fulfilledByInput}>
                    <input 
                      type="text" 
                      value={watch("splitInvoice.fulfilled_by.company_name") || ''}
                      onChange={(e) => setValue("splitInvoice.fulfilled_by.company_name", e.target.value)}
                      placeholder="Seller Company Name"
                    />
                    <div className={styles.coInput}>
                    {/* <span>c/o</span>  */}
                    <input 
                      type="text" 
                      value={watch("splitInvoice.fulfilled_by.name") || ''}
                      onChange={(e) => setValue("splitInvoice.fulfilled_by.name", e.target.value)}
                      placeholder="User's First & Last Name"
                    />
                    </div>
                    <input 
                      type="text" 
                      value={watch("splitInvoice.fulfilled_by.address.line1") || ''}
                      onChange={(e) => setValue("splitInvoice.fulfilled_by.address.line1", e.target.value)}
                      placeholder="Company Address"
                    />
                    <div style={{ display: 'flex', gap: '2px' }}>
                      <input 
                        type="text" 
                        value={watch("splitInvoice.fulfilled_by.address.city") || ''}
                        onChange={(e) => setValue("splitInvoice.fulfilled_by.address.city", e.target.value)}
                        placeholder="Company City"
                        style={{ flex: 1 }}
                      />,
                        <MatSelect
                          className={clsx(styles.InputFieldcss, 'inputPendingUsers', 'stateDropdownSplitInvoice')}
                          fieldName={register("splitInvoice.fulfilled_by.address.state").name}
                          control={control}
                          placeHolderText="State"

                          MenuProps={{
                            classes: {
                              paper: 'stateDropdownListSplit',

                            },
                          }}
                          options={states?.map((x:any) => ({ title: x.code, value: x.code }))}
                        />,
                      <input 
                        type="text" 
                        value={watch("splitInvoice.fulfilled_by.address.zip") || ''}
                        onChange={(e) => setValue("splitInvoice.fulfilled_by.address.zip", e.target.value.replace(/[^0-9]/g, ''))}
                        placeholder="Zipcode"
                        style={{ width: '70px' }}
                        maxLength={5}
                      />
                    </div>
                    <input 
                      type="email" 
                      value={watch("splitInvoice.fulfilled_by.email") || ''}
                      onChange={(e) => setValue("splitInvoice.fulfilled_by.email", e.target.value)}
                      placeholder="User's Email Address"
                    />
                  </div>
                  {errors.splitInvoice?.fulfilled_by?.email && <span className={styles.errorMessage}>{errors.splitInvoice.fulfilled_by.email.message}</span>}
              </div>

              </div>
            
              <div className={styles.paymentInstructionsBox}>
                <h4>PAYMENT INSTRUCTIONS</h4>
                <div className={styles.paymentMethodRow}>
                  <span>ACH or Wire -&nbsp;</span>
                  <span className={styles.noChecks}>NO CHECKS</span>
                  <span className={styles.greenArrow}>
                     <img src={GreenArrow} alt="Green Arrow" />
                  </span>
                </div>
                <div className={styles.paymentDetails}>
                  <div className={styles.paymentRow}>
                    <span>Seller Name:</span>
                    <span>{watch("splitInvoice.payment_instructions.company_name")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Bank Name:</span>
                    <span>{watch("splitInvoice.payment_instructions.bank_name")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Account Name:</span>
                    <span>{watch("splitInvoice.payment_instructions.account_name")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Account Number:</span>
                    <span>{watch("splitInvoice.payment_instructions.account_number")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Routing Number:</span>
                    <span>{watch("splitInvoice.payment_instructions.routing_number")}</span>
                  </div>
                  <div className={styles.paymentRow}>
                    <span>Bank Address:</span>
                    <span>{watch("splitInvoice.payment_instructions.bank_address")}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className={styles.paymentInfo}>
              <h3>$ {watch("splitInvoice.details.total_purchase")} due {formatDate(watch("splitInvoice.details.payment_due_date"))}</h3>
            </div>
            </>}
               <div className={styles.lineItems}>
              {pageNumber===0 && <h4>LINE ITEM DETAIL</h4>}
              <table className={`${styles.itemTable} splitInvoiceTable`}>
                <thead>
                  <tr>
                    <th>LN</th>
                    <th>Description</th>
                    <th>Qty</th>
                    <th>$ / Unit</th>
                    <th>EXT ($)</th>
                  </tr>
                </thead>
                <tbody>
                  {
                  watch("splitInvoice.lineItems")?.map((_item: any, index: number) => {
                    if(itemCount>0){
                      itemCount--;
                      const item = watch("splitInvoice.lineItems")[splitLineItemIndex];
                      const newIndex = Number(item.line_no) - 1;
                      splitLineItemIndex++;
                      if(!item) return <></>
                      return(
                    <tr key={index}>
                      {(!item.description) ?
                      <td colSpan={5} className={styles.addRowTd}>
                        <button 
                          type="button"
                          className={styles.addButton}
                          onClick={() => addLineItemToSplitInvoice(item.line_no)}
                        >
                          <AddRow/>
                        </button>
                      </td>
                      :
                      <>
                        <td>
                          <div className={styles.descriptionWithRemove}>
                            <button 
                              type="button"
                              className={styles.removeButton}
                              onClick={() => removeSplitLineItem(newIndex)}
                            >
                              <RemoveIcon />
                            </button>
                            <span>{item.po_line}</span>
                          </div>
                        </td>
                        <td>
                          <p><strong>{item.product_tag}</strong></p>
                          <div>{item.description.split('\n').map(
                            (desc:string, i:number)=>{
                              return <p key={i}>{desc}</p>
                            }
                          )}</div>
                          {!!(item.domestic_material_only) && <p className={styles.domesticMaterialOnly}>Domestic (USA) Material Only</p>}
                        </td>
                        <td>
                          <div className={styles.editInputTd}>
                            <input 
                              type="text"
                              {...register(`splitInvoice.lineItems.${newIndex}.quantity`, { valueAsNumber: true })}
                              onChange={(e) => {
                                e.target.value = allowNumericOnly(e.target.value);
                                qtyChangeHandler(newIndex, productMapping[item?.product_id ?? 0], Number(e.target.value));
                                register(`splitInvoice.lineItems.${newIndex}.quantity`).onChange(e);
                              }}
                              className={styles.adjustmentInput}
                            />
                            {item.qty_type?.toUpperCase()}
                          </div>
                          <div>{item.line_weight} {item.line_weight_unit}</div>
                        </td>
                        <td>
                            <div className={styles.priceUnitInputEditMain}>
                              <div className={styles.priceUnitInputEdit}><span>$</span> <input 
                        type="text"
                        {...register(`splitInvoice.lineItems.${newIndex}.price_per_unit`)}
                        onChange={(e) => {
                          const numericValue = allowNumericOnly(e.target.value);
                          onPriceChange('splitInvoice.lineItems', Number(numericValue), newIndex, productMapping[item?.product_id ?? 0]);
                          setValue(`splitInvoice.lineItems.${newIndex}.price_per_unit`, numericValue);
                        }}
                        className={styles.adjustmentInput}
                      /></div> <span className={styles.priceUnitEdit}> / {item.price_type?.toUpperCase()}</span></div>
                         </td>
                        <td>$ {formatValue(item.extended || 0)}</td>
                      </>
                      }
                    </tr>
                    )
                  }
                  return <></>
                  })
                  }
                </tbody>
                
              </table>
              {pageNumber+1 === splitDataByPage.length && <table className={styles.materialTotalTable}>
                  <tr>
                    <th></th>
                    <th></th>
                    <th className={styles.materialTotal}>Material Total:</th>
                    <th>$</th>
                    <th>{formatValue(watch("splitInvoice.details.material_total"))}</th>
                  </tr>
                </table>}
                
            </div>
            
            {pageNumber+1 === splitDataByPage.length && <div className={styles.invoiceFooter}>
              <div className={styles.note}>
                 {watch('splitInvoice.details.payment_method') !== 'ach_credit' ? <>
                    <p className={styles.paymentNote1}>NOTE: Payment made by check will be considered non-payment. Remit payment via ACH or wire per the instructions on this invoice. Please note that under no circumstances will Bryzos be party to any third-party contracts, such as lien waivers.</p>
                 </> :
                  <div className={styles.paymentNote2}> 
                        <p>
                          <b>DISCLAIMER :</b> This order requires a deposit due to the Cash In Advance payment method. 
                          In the event you receive a higher quantity than ordered, the deposit is estimated to cover any 
                          additional costs incurred upon shipment. Any funds not used for additional costs will be refunded to you following order completion.
                        </p>
                        <p>
                          <span>This order cannot be processed for fulfillment until Total Purchase amount of funds has been received by Bryzos. </span> 
                          If funds have not been received within 72 hours of the checkout date, this order will be cancelled.
                        </p>
                        <p>
                          NOTE: Payment made by check will be considered non-payment. Remit payment via ACH or wire per the instructions on this invoice. Please note that under no circumstances will Bryzos be party to any third-party contracts, such as lien waivers.
                        </p>
                  </div>
                  }
              
              </div>
              <div className={styles.totals}>
                 <table>
                    {splitAdjustments.creditReturn && (
                      <tr>
                        <td>Credit</td>
                        <td>$</td>
                        <td>
                          <input
                            type="text"
                            value={splitAdjustmentValues.creditReturn}
                            onChange={(e) => {
                              handleSplitAdjustmentChange('creditReturn', allowNumericOnly(e.target.value));
                            }}
                            className={styles.adjustmentInput}
                            style={{ color: '#ff0000' }}
                            ref={splitCreditInputRef}
                          />
                        </td>
                      </tr>
                    )}
                    {splitAdjustments.restockFee && (
                      <tr>
                        <td>Restock Fee</td>
                        <td>$</td>
                        <td>
                          <input
                            type="text"
                            value={splitAdjustmentValues.restockFee}
                            onChange={(e) => {
                              handleSplitAdjustmentChange('restockFee', allowNumericOnly(e.target.value));
                            }}
                            className={styles.adjustmentInput}
                            ref={splitRestockInputRef}
                          />
                        </td>
                      </tr>
                    )}
                    {splitAdjustments.freightFee && (
                      <tr>
                        <td>Freight Fee</td>
                        <td>$</td>
                        <td>
                          <input
                            type="text"
                            value={splitAdjustmentValues.freightFee}
                            onChange={(e) => {
                              handleSplitAdjustmentChange('freightFee', allowNumericOnly(e.target.value));
                            }}
                            className={styles.adjustmentInput}
                            ref={splitFreightInputRef}
                          />
                        </td>
                      </tr>
                    )}
                  <tr>
                    <td>Sales Tax:</td>
                    <td>$</td>
                    <td>{formatValue(watch("splitInvoice.details.sales_tax"))}</td>
                  </tr>
                  {watch('splitInvoice.details.payment_method') === 'ach_credit' && (
                    <tr>
                      <td>Deposit:</td>
                      <td>$</td>
                    <td>{watch("splitInvoice.details.deposit")}</td>
                  </tr>
                  )}
                  <tr>
                    <td>Total Purchase</td>
                    <td>$</td>
                    <td>{formatValue(watch("splitInvoice.details.total_purchase"))}</td>
                  </tr>
                </table>
                 <div className={styles.taxExemption}>
                <p>A valid sales tax exemption certificate must be presented to Bryzos prior to purchase to honor a sales tax exemption.</p>
              </div>
              </div>
             
            </div>
            }
            <div className={styles.pageNumber}>{pageNumber+1} of {splitDataByPage.length}</div>
          </div>)
          })}
          </div>
          
          <div className={styles.adjustments}>
            <span>Invoice Ledger Adjustments:</span>
            <button 
              className={styles.adjustmentButton} 
              type="button" 
              onClick={() => handleAddCreditReturn('split')}
            >
              + Add Credit for Return
            </button>
            <button 
              className={styles.adjustmentButton} 
              type="button" 
              onClick={() => handleAddRestockFee('split')}
            >
              + Add Restock Fee
            </button>
            <button 
              className={styles.adjustmentButton} 
              type="button" 
              onClick={() => handleAddFreightFee('split')}
            >
              + Add Freight Fee
            </button>
          </div>
          <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
        disablePortal
        classes={{
          root: clsx(styles.emailSentMain),
          container: clsx(sendEmail === true ? styles.emailSplitPopup : styles.emailInvoicePopup, styles.twoColumnLayout),
        }}
        slotProps={{
          backdrop: {
            sx: {
              backgroundColor: 'rgba(143, 143, 143, 0.8)',
            },
          },
        }}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Email will be sent - </p>
          <div className={styles.sendEmailGrid}>
            <span>To: </span>
            <Tooltip title={order?.send_invoice_to ? order.send_invoice_to : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={order?.send_invoice_to} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <div className={styles.sendEmailGrid}>
            <span>Cc: </span>
            <Tooltip title={invoiceEmailRecipients?.cc_email ? invoiceEmailRecipients.cc_email : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={invoiceEmailRecipients?.cc_email ? invoiceEmailRecipients.cc_email : 'NA'} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <div className={styles.sendEmailGrid}>
            <span>Bcc: </span>
            <Tooltip title={invoiceEmailRecipients?.bcc_email ? invoiceEmailRecipients.bcc_email : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={invoiceEmailRecipients?.bcc_email ? invoiceEmailRecipients.bcc_email : 'NA'} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <p className={styles.continuetext}>Do you want to continue? (you can use Generate Email feature to send invoice to desired recipients)</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
        </div>
      </div>
    </div>
    
  
    </>
   
  );
};

export default SplitPO;
