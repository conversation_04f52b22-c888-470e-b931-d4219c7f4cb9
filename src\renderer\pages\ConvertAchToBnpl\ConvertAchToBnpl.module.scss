.selectDropdown {
  width: 100%;
  max-width: 350px;
  height: 48px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 100%;
  }
}

.autocompleteDescPanel {
  border-radius: 4px;
  width: 100%;
  max-width: 350px;
  height: 48px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  padding-right: 4px;
  border-radius: 0px 0px 4px 4px;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 316px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }

  span {
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    box-shadow: none;
    padding: 4px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;

    &:hover {
      border-radius: 2px;
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      // background-color: #EBF2FF;
      // color: #397aff;
      background-color: #fff;
      color: #000;
    }
  }
}

.btnSendNotif {
  margin-top: 30px;

  button {
    height: 38px;
    color: #fff;
    background-color: var(--primaryColor);
    border-radius: 4px;
    cursor: pointer;
    padding: 6px 18px;
    border: 0px;
    font-size: 16px;
    line-height: normal;

    @media (max-width: 767px) {
      font-size: 14px;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.sendNotificationMain {
  border: 1px solid #f1f1f1;
  padding: 20px;
  width: 100%;
  max-width: 100%;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 6px;

  @media (max-width: 767px) {
    margin: 16px auto;
    padding: 16px;
  }

  .title {
    font-size: 18px;
    line-height: normal;
    margin-bottom: 40px;
    font-weight: 600;
    color: var(--primaryColor);

    @media (max-width: 767px) {
      margin-bottom: 20px;
    }
  }

  label {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
  }
}
.loaderImg {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}