import { useMutation } from "@tanstack/react-query";
import axios from 'axios';

const fetchPublicHolidays = async (year = new Date().getFullYear()) => {
  const currentYear = year;
  const nextYear = year + 1;

  const [{ data: currentYearHolidays }, { data: nextYearHolidays }] = await Promise.all([
    axios.get(`https://date.nager.at/api/v3/publicholidays/${currentYear}/US`),
    axios.get(`https://date.nager.at/api/v3/publicholidays/${nextYear}/US`)
  ]);

  return [...currentYearHolidays, ...nextYearHolidays];
};

export const useGetPublicHolidays = () => {
  return useMutation((year) => fetchPublicHolidays(year ?? new Date().getFullYear()));
};
