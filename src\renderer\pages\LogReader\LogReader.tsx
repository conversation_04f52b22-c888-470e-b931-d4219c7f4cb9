import { Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import { AgGridReact } from 'ag-grid-react';
import React, { useRef, useState } from 'react'
import FileUpload from './components/FileUpload';
import clsx from 'clsx';
import styles from "./LogReader.module.scss";
import { parsePipeSeparatedFile, removeAlternateCharactersFromLines } from '../../utils/helper';

const LogReader = () => {
    const gridRef = useRef<AgGridReact>(null);
    const [openDialog, setOpenDialog] = useState(false);
    const [openLogDialog,setOpenLogDialog] = useState(false)
    const [logData, setLogData] = useState("")
    const [rowData, setRowData] = useState<any[]>([]);
    const [quickFilterText, setQuickFilterText] = useState("");
    const [rowCount, setRowCount] = useState(0);
    
  
    const columnDefs = [
      {
        headerName: "Date & Time",
        field: "dateTime",
        sortable: true,
        filter: "agDateColumnFilter", // Use the built-in date filter
        valueFormatter: params => params.value ? new Date(params.value).toLocaleString() : '', // Format ISO string to a readable format
        filterParams: {
          comparator: (filterLocalDateAtMidnight, cellValue) => {
            // Parse the cell value into a JavaScript Date
            const cellDate = new Date(cellValue);
    
            // Compare the two dates (ignoring time)
            if (cellDate < filterLocalDateAtMidnight) {
              return -1;
            } else if (cellDate > filterLocalDateAtMidnight) {
              return 1;
            }
            return 0;
          },
          browserDatePicker: true, // Enables browser's built-in date picker
        },
        width: 200, // Adjust width for readability
        cellClass: 'LogListCell',
        headerClass: 'LogListHeader'
      },
      {
        headerName: "Process",
        field: "process",
        sortable: true,
        filter: "agDateColumnFilter",
        width: 130,
        cellClass: 'LogListCell',
        headerClass: 'LogListHeader'
      },
      {
        headerName: "Level",
        field: "level",
        sortable: true,
        filter: "agTextColumnFilter",
        width: 130,
        cellClassRules: {
          "level-info": params => params.value === "info",
          "level-error": params => params.value === "error",
          "level-warning": params => params.value === "warning"
        },
        cellClass: 'LogListCell',
        headerClass: 'LogListHeader'
      },
      {
        headerName: "Log Message",
        field: "log",
        sortable: false,
        filter: "agTextColumnFilter",
        flex: 1 ,// Adjust width dynamically based on available space
        cellClass: 'LogListCell',
        headerClass: 'LogListHeader',
        minWidth: 300,
        cellStyle : {'display': 'block','text-overflow':'ellipsis','white-space':'nowrap', 'overflow': 'hidden', 'padding': 0 }
      }
    ];  

    const defaultColDef = {
        lockVisible: true,
        wrapText: true,
    };
    const handleDialogClose = () => {
      setOpenDialog(false); 
    };
  
    const handleDialogOpen = () => {
      setOpenDialog(true);
    };
    
    function handleFileParsed(data: any[]): void {
      setRowData(data);
      setRowCount(data.length)
      setOpenDialog(false); 
    }
  
    const handleQuickFilter = (event: React.ChangeEvent<HTMLInputElement>) => {
      setQuickFilterText(event.target.value);
    };
  
    const handleLogDialogClose = ()=>{
      setOpenLogDialog(false);
    }
  
    const handleRowClick = (event) => {
      setLogData(event.data.log)
      setOpenLogDialog(true);
    };

    return (
      <>
        <div className="content">
          <div className={styles.header}>
          <Button onClick={handleDialogOpen} color="primary">
            Add Log File
          </Button>
          <input
          type="text"
          placeholder="Quick Filter"
          onChange={handleQuickFilter}
        />
         {rowCount > 0 && <label>Row Count: {rowCount}</label>}

          </div>
        {rowData.length > 0 && (
          
          <div className={clsx(styles.ag_theme_quartz, styles.agGridAdmin,'logReaderGrid')} style={{
            height: "calc(100vh - 450px)",
            width: "100%",
            minHeight: "400px",
            marginTop:"15px"
        }}>
            <AgGridReact
              ref={gridRef}
              rowData={rowData}
              columnDefs={columnDefs}
              quickFilterText={quickFilterText}
              rowHeight={50}
              headerHeight={32}
              getRowStyle={(params) => {
                if (params.data.level === "error") {
                  return { backgroundColor: "rgba(255, 0, 0, 0.2)" }; // Light red for errors
                } else if (params.data.level === "warning") {
                  return { backgroundColor: "rgba(255, 255, 0, 0.2)" }; // Light yellow for warnings
                } else if (params.data.level === "info") {
                  return { backgroundColor: "rgba(0, 255, 0, 0.2)" }; // Light green for info
                }
                return null; // Default style
              }}
              onRowClicked= {handleRowClick}
              enableCellTextSelection={true}
              suppressCellFocus={true}
              defaultColDef={defaultColDef}
            />
          </div>
        )}
        </div>
        <Dialog    
         classes={{
                    paper: styles.logReaderPopup,
                }} 
          open={openDialog}>
             <div className={styles.logReadertitle}>Drop Log File Here</div>
             <div className={styles.uploadFileMain}>
              <FileUpload onFileParsed={handleFileParsed} />
             </div>
            <button className={styles.closeBtn} onClick={handleDialogClose} color="primary">
              Close
            </button>
        </Dialog>
        <Dialog 
          open={openLogDialog} 
          onClose={handleLogDialogClose}
          sx={{ 
            "& .MuiDialog-paper": {
              width: "600px", // Fixed width
              height: "400px", // Fixed height
            } 
          }}
        >
          <DialogTitle>Log Details</DialogTitle>
          <DialogContent>
          <pre style={{ whiteSpace: "pre-wrap" }}>{logData?.replaceAll("\\n", "\n")}</pre>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleLogDialogClose} color="primary">
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </>
    );
    
}

export default LogReader