{"env": {"browser": true, "es2021": true, "jest": true, "node": true}, "extends": ["eslint:recommended", "eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:import/recommended", "plugin:import/typescript"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "ignorePatterns": ["**/*.js"], "plugins": ["react", "@typescript-eslint"], "rules": {"react-hooks/exhaustive-deps": "off", "@typescript-eslint/no-unused-vars": "off", "import/no-unresolved": "off", "import/default": "off", "react/react-in-jsx-scope": "off", "import/namespace": "off", "react/prop-types": "off", "no-unsafe-optional-chaining": "off", "prefer-const": "off", "no-empty": "off", "import/no-duplicates": "off", "@typescript-eslint/no-explicit-any": "off", "import/no-named-as-default": "off", "import/no-named-as-default-member": "off"}, "settings": {"import/resolver": {"typescript": {}}, "react": {"version": "detect"}}}