import { Select, MenuItem, Tooltip, Autocomplete, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader/Loader";
import { filterArrray } from "../../../utils/helper";
import styles from "./CohortList.module.scss";
import MatPopup from "../../../components/common/MatPopup";
import clsx from "clsx";
import DiscountPopup from "../../../components/DiscountPopup";
import dayjs from "dayjs";
import useGetAdminReferenceData from "../../../hooks/useGetAdminReferenceData";
import usePostAddCohort from "../../../hooks/usePostAddCohort";
import usePostEditCohort from "../../../hooks/usePostEditCohort";
const CohortList = () => {
  const [filteredCohorts, setFilteredCohorts] = useImmer<any>([]);
  const [allCohorts, setAllCohorts] = useImmer<any>([]);

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [showAddCohortPopup, setShowAddCohortPopup] = useImmer(false);
  const [addCohortName, setAddCohortName] = useImmer("");
  const [allCohortNameChecked, setAllCohortNameChecked] = useImmer<any>(false);
  const [pageChecked, setPageChecked] = useImmer<any>(null);
  const [showEditCohortPopup, setShowEditCohortPopup] = useImmer(false);
  const [editCohortDetails, setEditCohortDetails] = useImmer<any>(null);

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredCohorts.length / itemsPerPage);

  const { data: adminReferenceData, isLoading: adminReferenceDataLoading, isFetching: adminReferenceDataFetching } = useGetAdminReferenceData();

  const {
    mutate: addCohort,
    isLoading: isAddCohortLoading,
    data: addCohortData,
  } = usePostAddCohort();

  const {
    mutate: editCohort,
    isLoading: isEditCohortLoading,
    data: editCohortData,
  } = usePostEditCohort();

  useEffect(() => {
    if (adminReferenceData?.onboard_source) {
      setAllCohorts(adminReferenceData.onboard_source)
      if (inputSearchValue.length === 0) {
        setFilteredCohorts(adminReferenceData.onboard_source);
      }
    } else {
      setFilteredCohorts([]);
    }
  }, [adminReferenceData?.onboard_source]);

  useEffect(() => {
    if (allCohorts.length > 0 && inputSearchValue.length !== 0) {
      search(inputSearchValue)
    }
  }, [allCohorts])

  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
    const pageList: any = {};
    if (pageCount !== 0) {
      for (let index = 0; index < pageCount; index++) {
        pageList[index] = false
      }
    } else {
      pageList[pageCount] = false
    }
    setPageChecked(pageList)
  }, [itemsPerPage, pageCount]);


  useEffect(() => {
    if (addCohortData && !isAddCohortLoading) {
      setApiResponseMessage(addCohortData);
    }
  }, [addCohortData, isAddCohortLoading]);

  useEffect(() => {
    if (editCohortData && !isEditCohortLoading) {
      setApiResponseMessage(editCohortData);
    }
  }, [editCohortData, isEditCohortLoading]);

  const search = (searchValue: string) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(searchValue);
    if (searchValue) {
      const _filterArrray = filterArrray(allCohorts, searchValue, [
        "onboarded_app",
      ]);
      if (_filterArrray?.length) {
        setFilteredCohorts(_filterArrray);
      } else {
        setFilteredCohorts([]);
      }
    } else {
      setFilteredCohorts(allCohorts ? allCohorts : []);
    }
  };

  const handlePageClick = (event: any) => {
    const newOffset = (event.selected * itemsPerPage) % filteredCohorts.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
    const newEndOffset = newOffset + itemsPerPage;
    const isCheckedAvailable: any = [];
    filteredCohorts.slice(newOffset, newEndOffset).forEach((cohort: any) => {
      if ('is_approved' in cohort) {
        isCheckedAvailable.push(cohort)
      }
    })
    if (isCheckedAvailable.length !== 0) {
      setAllCohortNameChecked(pageChecked[event.selected])
    } else {
      setAllCohortNameChecked(null)
    }
  };


  const confirmationPopupClose = () => {
    setShowAddCohortPopup(false);
    setAddCohortName('');
    setShowEditCohortPopup(false)
  };

  const handleAddCohort = () => {
    confirmationPopupClose();
    const payload = {
      "data": {
        "cohort_name": addCohortName
      }
    }
    addCohort(payload)
  }

  const handleEditCohort = (cohort: any) => {
    setEditCohortDetails(cohort)
    setShowEditCohortPopup(true);
  }

  const allCohortsChecked = (e: any, currentPage: number) => {
    let cohortList: any[] = [];
    filteredCohorts.slice(itemOffset, endOffset).forEach((cohort: any) => {
      if ('isChecked' in cohort) {
        cohortList.push(cohort)
      }
    })

    if (e.target.checked === true) {
      cohortList.forEach((cohortData: any, i: any) => {
        const cohortNameIndex = filteredCohorts.findIndex((cohort: any) => cohort.id === cohortData.id);
        setFilteredCohorts((prev: any) => {
          prev[cohortNameIndex].isChecked = true;
          return prev;
        });
      })
      setPageChecked((prev: any) => {
        prev[currentPage] = true
        return prev
      })
      setAllCohortNameChecked(true)
    } else {
      cohortList.forEach((cohortData: any, i: number) => {
        const cohortNameIndex = filteredCohorts.findIndex((cohort: any) => cohort.id === cohortData.id);
        setFilteredCohorts((prev: any) => {
          prev[cohortNameIndex].isChecked = false;
          return prev;
        });
      })
      setPageChecked((prev: any) => {
        prev[currentPage] = false
        return prev
      })
      setAllCohortNameChecked(false)
    }
  }

  const handleDropdownChange = (event: any) => {
    setItemsPerPage(+event.target.value);
    let e = {
      target: {
        check: false
      }
    }
    if (allCohortNameChecked) {
      e.target.check = true
      allCohortsChecked(e, currentPage)
    } else {
      e.target.check = false
      allCohortsChecked(e, currentPage)
    }
  }

  const handleDisableBtn = () => {
    if (addCohortName.length === 0 || /<|>/g.test(addCohortName)) {
      return true
    }
    return false;
  }

  const submitEditCohortDetails = (data: any) => {
    let payload: any = {
      "cohort_id": data.id,
    }
    const phaseoutStartDate = data?.discDiscountPhaseoutStartdate ? dayjs(data.discDiscountPhaseoutStartdate).format('YYYY-MM-DD') : null;
    payload.is_discount = data.discIsDiscounted
    payload.discount_percentage = data.discDiscountRate
    payload.discount_period = data.discDiscountPeriod
    payload.discount_phaseout_startdate = phaseoutStartDate
    payload.discount_phaseout_period = data.discDiscountPhaseoutPeriod
    payload.discount_pricing = 'Neutral_Pricing'//data.discDiscountPricingColumn
    payload.seller_spread_percentage = data.spreadRate
    editCohort({ data: payload })
    confirmationPopupClose();
  }

  return (
    <div className="contentMain">
      {
        adminReferenceDataLoading ||
          isAddCohortLoading ||
          isEditCohortLoading ||
          adminReferenceDataFetching ? (
          <div className="loaderImg">
            <Loader />
          </div>
        ) : (
          <div>
            <div className={styles.searchBox}>
              <Select
                className={styles.showdropdwn}
                value={itemsPerPage}
                onChange={(event) => { handleDropdownChange(event) }}
              >
                {perPageEntriesOptions.map((item, index) => (
                  <MenuItem key={index} value={item}>
                    <span>{item}</span>
                  </MenuItem>
                ))}
              </Select>
              <div className={styles.allCompanyText}>

              </div>
              <button className={styles.addCompanyBtn} onClick={() => setShowAddCohortPopup(true)} >Add Cohort</button>
              <input
                className={styles.searchInput}
                type="text"
                onChange={(e) => search(e.target.value)}
                placeholder="Search Cohort"
                value={inputSearchValue}
              />
            </div>
            <div className={styles.tblscroll}>
              <table>
                <thead>
                  <tr>
                    <th>Cohort Name</th>
                    <th></th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCohorts?.length ? (
                    filteredCohorts
                      .slice(itemOffset, endOffset)
                      .map((cohort: any, index: number) => (
                        <tr key={cohort.id}>
                          <td>{cohort.onboarded_app}</td>
                          <td>
                            <button
                              className={styles.resetPassBtn}
                              onClick={() =>
                                handleEditCohort(cohort)
                              }
                            >
                              Spread
                            </button>
                          </td>
                          <td></td>
                        </tr>
                      ))
                  ) : (
                    <tr>
                      <td colSpan={4} className={"noDataFoundTd"}>No data found</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            <div className={"PaginationNumber"}>
              <ReactPaginate
                breakLabel="..."
                nextLabel=">"
                onPageChange={handlePageClick}
                pageRangeDisplayed={5}
                pageCount={pageCount}
                previousLabel="<"
                renderOnZeroPageCount={(props) =>
                  props.pageCount > 0 ? undefined : null
                }
                forcePage={pageCount > 0 ? currentPage : -1}
              />
            </div>

          </div>
        )}

      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showAddCohortPopup}
      >
        <div className={styles.tblscrollPop}>
          <div className={styles.continuePopup}>
            <p className={styles.addCohortTitle}>Add Cohort</p>
            <div className={styles.overFlowForPop}>
              <input type="text" className={styles.companyNameInput} value={addCohortName} onChange={(e) => { setAddCohortName(e.target.value) }} />
            </div>
            <div className={styles.yesAndnoBtn}>
              <button className={styles.okBtn} onClick={handleAddCohort} disabled={handleDisableBtn()}>
                Save
              </button>
              <button
                className={styles.okBtn}
                onClick={confirmationPopupClose}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>

      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showEditCohortPopup}
      >
        <DiscountPopup pageName={"cohort"} popupHeading={"Edit Spread Detail (Cohort)"} discountData={editCohortDetails} pricingColumnList={adminReferenceData?.pricing_columns} defaultValues={adminReferenceData?.discount_default_values} submitUserDiscount={submitEditCohortDetails} confirmationPopupClose={confirmationPopupClose} />
      </MatPopup>
    </div>
  );
};

export default CohortList;
