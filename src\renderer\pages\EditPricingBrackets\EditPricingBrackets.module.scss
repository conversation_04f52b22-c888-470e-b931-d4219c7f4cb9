.agGridAdmin {
    --ag-icon-font-code-asc: '\25B2';
    --ag-icon-font-code-desc: '\25BC';
    --ag-icon-font-code-none: '\25B2\25BC';
    .ag-center-cols-viewport {
        min-height: 5000px !important;
    }
    .ag-icon-asc::before {
        content: var(--ag-icon-font-code-asc);
      }
    .ag-icon-none::before {
        content: var(--ag-icon-font-code-none);
        color: green;
        padding: 2px;
        margin-bottom: 5px;
        font-size: 20px !important;
    }
    .ag-root-wrapper {
      .ag-root-wrapper-body {
        .ag-body-horizontal-scroll-viewport{
            overflow-x:auto;
            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }
        
            &::-webkit-scrollbar-track {
                box-shadow: inset 0 0 6px #a8b2bb;
                border-radius: 4px;
            }
        
            &::-webkit-scrollbar-thumb {
                background: #a8b2bb;
                border-radius: 4px;
            }
        }
        .ag-header-row {
          .ag-header-cell {
            padding-left: 20px;
            padding-right: 20px;
            line-height: 1.2;
            font-weight: 600;
            font-size: 16px;
            margin: 0;
            text-align: left;
            color: #fff;
            background: #676f7c;
            &:hover {
              color: #fff;
              background: #676f7c;
            }
          }
          .ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover{
            color: #fff;
            background: #676f7c;
          }
        }
        .ag-body-viewport-wrapper.ag-layout-normal {
          overflow-x: scroll;
          overflow-y: scroll;
        }
        ::-webkit-scrollbar {
          -webkit-appearance: none;
          width: 8px;
          height: 6px;
        }
        ::-webkit-scrollbar-thumb {
          border-radius: 4px;
          background: #a8b2bb;
          box-shadow: inset 0 0 6px #a8b2bb;
        }
        .ag-body {
          .ag-body-viewport {
            .ag-center-cols-clipper {
              .ag-row-odd {
                background-color: #f2f2f2;
              }
              .ag-cell{
                cursor: pointer;
              }
              .red-border{
                border: 1px solid red;
              }
            }
          }
        }
      }
    }
  }

  .ag_theme_quartz {
    --ag-foreground-color: #676f7c;
    --ag-background-color: white;
    --ag-header-foreground-color: white;
    --ag-header-background-color: #676f7c;
    --ag-odd-row-background-color: #f2f2f2;
    --ag-header-column-resize-handle-color: rgb(126, 46, 132);
    --ag-font-size: 14px;
    --ag-font-family: monospace;
    --ag-icon-font-code-aggregation: "\f247";
    --ag-icon-font-color-group: red;
    --ag-icon-font-weight-group: normal;
}

// Custom styling for pricing brackets table
.pricingBracketsContainer {
  width: 100%;
//   max-width: 800px;
height: 100%;
  margin: 0 auto;
  padding: 20px;
//   background-color: #fff;

  h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 20px;
    font-weight: 600;
  }
}

.bracketTable {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  thead{
    text-align: center;
  }
  
  th {
    background-color: #676f7c;
    color: white;
    padding: 12px 16px;
    text-align: center;
    font-weight: 600;
    border: 1px solid #ddd;
  }
  
  td {
    padding: 12px 16px;
    border: 1px solid #ddd;
    
    &.gearColumn {
      background-color: #f5f5f5;
      font-weight: 600;
      text-align: center;
    }
  }
  
  tr {
    &:nth-child(even) {
      background-color: #f9f9f9;
    }
    
    &:hover {
      background-color: #f3f3f3;
    }
  }
}

.inputWrapper {
  display: block;
  width: 100%;
}

.inputField {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
  
  &.error {
    border: 2px solid #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
  }
  
  &:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.65;
  }
}

.header_initial {
  background-color: #676f7c;
  color: white;
}

.cell_default {
  padding: 10px 15px;
}

.buttonContainer {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}

.actionColumn {
  width: 70px;
  text-align: center;
}

.deleteButton {
  background-color: transparent;
  color: #dc3545;
  border: none;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(220, 53, 69, 0.1);
  }
}

.actionButtons {
  margin-top: 30px;
  display: flex;
  justify-content:flex-start;
  gap: 10px;
}

.addButton {
  background-color: #28a745;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #218838;
  }
}

.submitButton {
  background-color: #343a40;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.7;
  }
}

.editFlexContainer {
  display: flex;
  gap: 30px;
  margin-top: 20px;
}

.bracketTableContainer {
  flex: 1;
  max-width: 660px;
}

.gearContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;
  align-items: center;
    h1{
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 20px;
    }
}

.totalWeightContainer {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.totalWeightLabel {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.totalWeightInput {
  width: 300px;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}
