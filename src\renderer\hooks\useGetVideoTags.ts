import { useQuery, UseQueryResult } from "@tanstack/react-query";
import axios from 'axios';

const fetchVideoTags = async (videoFileName: string): Promise<any> => {
  const response = await axios.get<any>(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/getVideoTags`, {
    params: {
      video_file_name: videoFileName,
    },
  });
  return response.data;
};

const useGetVideoTags = (videoFileName?: string): UseQueryResult<any, Error> => {
  return useQuery<any, Error>(
    ['videoTags', videoFileName],
    () => fetchVideoTags(videoFileName!),
    {
      enabled: !!videoFileName, // Ensure the query runs only if videoFileName is provided
    }
  );
};

export default useGetVideoTags;
