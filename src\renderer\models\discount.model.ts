import dayjs from "dayjs";
import * as yup from "yup";

export const DiscountFromSchema = yup.object().shape({
  id: yup.number(),
  companyName: yup.string(),
  discIsDiscounted: yup.boolean().required("Required"),
  discDiscountRate: yup.number().typeError("Please enter valid spread").min(0,"Spread Rate cannot subceed 0").max(99, "Spread Rate cannot exceed 99").nullable().required("Required"),
  discDiscountPeriod: yup.number().integer('Spread Period cannot be a decimal value').min(0,"Spread Period cannot be less than 0").nullable().default(0).transform((value)=>{
    if(isNaN(value)){ 
      return 0
    }
    return value;
  }),
  discDiscountPhaseoutStartdate: yup.date().nullable(),
  discDiscountPhaseoutPeriod: yup.number().integer('Spread Phaseout Period cannot be a decimal value').min(0,"Spread Phaseout Period cannot be less than 0").nullable().required("Required").transform((value)=>{
    if(isNaN(value)){ 
      return 0
    }
    return value;
  }),
  discDiscountPricingColumn: yup.string().default("").nullable().required("Required"),
  discIsDiscountVarOverriden: yup.boolean(),
  userCreatedDate: yup.string().nullable(),
  spreadRate: yup.number().typeError("Please enter valid spread").min(0,"Spread Rate cannot subceed 0").max(99, "Spread Rate cannot exceed 99").nullable().required("Required"),
});

export type DiscountFromSchemaType = yup.InferType<
  typeof DiscountFromSchema
>;
