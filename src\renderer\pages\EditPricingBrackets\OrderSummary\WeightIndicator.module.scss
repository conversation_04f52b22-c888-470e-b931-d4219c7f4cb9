.container {
  position: absolute;
  width: 100%;
}

.weightDisplay {
  display: flex;
  padding: 2px;
  app-region: drag;
}

.weightValue {
  line-height: 1;
}

.weightValue {
  font-size: 28px;
  font-weight: normal;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  min-width: 59px;

  .weightInfoText {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -0.72px;
    text-align: left;
    color: #fff;
    margin-top: 3px;
  }

}
.weightValueBelowMinimum {
  font-weight: bold;
}

.weightInfo {
  display: flex;
  flex-direction: column;
}


.weightMinimum,
.weightOrder {
  font-family: Syncopate;
  font-size: 10.6px;
  font-weight: bold;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: -0.1px;
  text-align: left;
  color: #fff;
}


.showMininumWeightImg {
  background-image: url('../../../../assests/images/RedShape.svg');
  background-repeat: no-repeat;
  padding: 0px 5px;
}
.content {
  width: 270px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-weight: bold;
  position: relative;

  .weight {
    font-size: 24px;
  }

  .min-order {
    font-size: 12px;
    text-transform: uppercase;
  }
}