import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";

const useCapgoUpdateURL = () => {
  const queryClient = useQueryClient();

  // Define the GET request
  const fetchData = async () => {
    const response = await axios.get(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/getMobileReleaseUrls`);
    return response.data;
  };

  // Use useQuery for the GET request
  const { data, error, isLoading, isError } = useQuery(["fetchData"], fetchData);

  // Define the POST request
  const postData = async (payload:any) => {
    const response = await axios.post(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/setMobileReleaseUrl`, payload);
    return response.data;
  };

  // Use useMutation for the POST request
  const mutation = useMutation(postData, {
    onSuccess: () => {
      // Invalidate and refetch the query after a successful mutation
      queryClient.invalidateQueries(["fetchData"]);
    },
  });

  return {
    data,
    error,
    isLoading,
    isError,
    mutate: mutation.mutate,
    mutationError: mutation.error,
    mutationIsLoading: mutation.isLoading,
    mutationIsError: mutation.isError,
    mutationData: mutation.data,
  };
};

export default useCapgoUpdateURL;
