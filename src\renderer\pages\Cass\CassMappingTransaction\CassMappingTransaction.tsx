
import { MenuItem, Select } from "@mui/material";
import styles from "./CassMappingTransaction.module.scss";
import { useContext, useEffect, useState } from "react";
import { useImmer } from "use-immer";
import ReactPaginate from "react-paginate";
import { CommonCtx } from "../../AppContainer";
import useGetProbablePo from "../../../hooks/useGetProbablePo";
import Loader from "../../../components/common/Loader";
import useGetPoList from "../../../hooks/useGetPoList";
import usePostMapTransactionToPo from "../../../hooks/usePostMapTransactionToPo";
import { MapTransactionToPoPayloadData, MapTransactionToPoPayloadDto, PoList, ProbablePo } from "@bryzos/giss-common-lib";
import CassMappingTransactionTable from "./components/cassMappingTransactionTable";
import CloseOrderConfirmationPopup from "./components/CloseOrderConfirmationPopup/CloseOrderConfirmationPopup";
import { useDebouncedValue } from "@mantine/hooks";
import useGetMappedCompanies from "../../../hooks/useGetMappedCompanies";
import { cloneDeep } from "lodash";
import clsx from "clsx";

export interface PoNumberListDto {
    [id: string]: MapTransactionToPoPayloadData
}

const CassMappingTransaction = () => {
    const [itemOffset, setItemOffset] = useState(0);
    const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [inputSearchValue, setInputSearchValue] = useState("");
    const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue?.trim(), 1000);
    const [showSaveBtn, setShowSaveBtn] = useState(false);
    const [filteredDataToShow, setFilteredDataToShow] = useImmer<any[]>([]);
    const [poNumber, setPoNumber] = useImmer<PoNumberListDto | null>(null);
    const [inputValue, setInputValue] = useImmer<any>({});
    const [closeOrderPopupData, setCloseOrderPopupData] = useImmer<any>(null);
    const [meta, setMeta] = useImmer<any>(null);
    const [showMatchedTransactionCheckbox, setShowMatchedTransactionCheckbox] = useState<boolean>(false);
    const [defaultProbablePoData, setDefaultProbablePoData] = useState<any>({});
    const [showSubmitBtn, setShowSubmitBtn] = useState<boolean>(true);
    const [disableSubmitBtn, setDisableSubmitBtn] = useState<boolean>(false);
    const [goToPageNumber, setGoToPageNumber] = useState("");
    const [disableGoToPageBtn, setDisableGoToPageBtn] = useState<boolean>(true);

    const showPopupFormAnyComponent = useContext(CommonCtx);

    const {
        data: allMappedCompanies,
        isLoading: allMappedCompanyLoading
    } = useGetMappedCompanies();

    const {
        data: getProbablePoData,
        isLoading: isGetProbablePoLoading,
        isFetching: isGetProbablePoFetching,
    } = useGetProbablePo(itemsPerPage, currentPage, encodeURIComponent(debouncedInputSearchValue), showMatchedTransactionCheckbox);

    const {
        data: getPoListData,
        isLoading: isGetPoListLoading,
        isFetching: isGetPoListFetching,
    } = useGetPoList();

    const {
        mutate: saveMapTransactionToPo,
        data: saveMapTransactionToPoData,
        isLoading: isSaveMapTransactionToPoLoading,
    } = usePostMapTransactionToPo();

    useEffect(() => {
        if (isGetProbablePoLoading || isGetProbablePoFetching || isGetPoListFetching || isGetPoListLoading) {
            return;
        }
        if (getProbablePoData?.meta) {
            setMeta(getProbablePoData.meta);
        } else {
            setMeta(null)
        }
        if (getProbablePoData?.data && getPoListData) {
            const _probableData = getProbablePoData?.data?.length ? getProbablePoData.data : [];
            if (_probableData?.length > 0) {
                _probableData.forEach((data) => {
                    if (!defaultProbablePoData[data.id]) {
                        let selectedPOsTotalData = 0;
                        if (data.mapped_orders) {
                            (JSON.parse(data.mapped_orders)).forEach((mappingData) => {
                                if (mappingData.is_checked) {
                                    if (!isNaN(+mappingData.buyer_po_total_purchase)) {
                                        selectedPOsTotalData += Number(mappingData.buyer_po_total_purchase);
                                    }
                                }
                            });
                        }
                        data.selectedPOsTotal = selectedPOsTotalData;
                        defaultProbablePoData[data.id] = data;
                    }
                });
                setDefaultProbablePoData(cloneDeep({ ...defaultProbablePoData }));
                handleCassMappingTableData(_probableData);
            } else {
                setFilteredDataToShow([])
            }
        }
    }, [getProbablePoData, isGetProbablePoLoading, isGetProbablePoFetching, getPoListData, isGetPoListFetching, isGetPoListLoading]);

    useEffect(() => {
        if (saveMapTransactionToPoData) {
            showPopupFormAnyComponent(saveMapTransactionToPoData)
            clear();
        }
    }, [saveMapTransactionToPoData])

    useEffect(() => {
        setCurrentPage(1);
    }, [debouncedInputSearchValue])

    useEffect(() => {
        if (poNumber) {
            handleShowSubmitBtn()
        }
    }, [poNumber])

    useEffect(() => {
        if (meta?.totalPages && (Number(goToPageNumber) > 0 && Number(goToPageNumber) <= meta.totalPages)) {
            setDisableGoToPageBtn(false);
        } else {
            setDisableGoToPageBtn(true);
        }
    }, [meta])

    const clear = () => {
        setShowSaveBtn(false);
        setPoNumber(null);
        setInputValue({})
        setDefaultProbablePoData({})
    };

    const handlePageClick = (event: any) => {
        setCurrentPage(event.selected + 1);
    };

    const getUnmappedPOList = (originalSelectedPos, selectedPos)=>{
    if(!originalSelectedPos || originalSelectedPos.length ===0){
        return;
    }
        // Create a Set of po_numbers in selectedPos for efficient lookup
    const selectedPoNumbers = new Set(selectedPos.map(item => item.po_number));

    // Filter and map to create deep copies with isUnmapped set to true
    return originalSelectedPos
        .filter(item => !selectedPoNumbers.has(item.po_number)) // exclude items with po_number present in selectedPos
        .map(item => ({ ...item, isUnmapped: true }));
    }


    const getDto = (poNumber:any)=>{
        const arr = poNumber.map(({ id, selected_orders, processed_amount }) => {
            const originalData = defaultProbablePoData[id];
            const mappedOrders = JSON.parse(originalData?.mapped_orders);
            const unmappedPos = getUnmappedPOList(mappedOrders, selected_orders);
            let total_amount_received_from_buyer = 0;
            if(unmappedPos && unmappedPos.length >0)
                selected_orders = [...selected_orders, ...unmappedPos]

            const updatedOrders = selected_orders.map(({ poData, amount_received_from_buyer, isUnmapped, hasInvalidAmount, hasZero, ...rest }, index) => {
                total_amount_received_from_buyer += amount_received_from_buyer
                if(isUnmapped){
                    return {
                        ...rest,
                        amount_received_from_buyer: (amount_received_from_buyer) ? Number(amount_received_from_buyer) : 0,
                        is_unmapped: true
                    }
                }

                return {
                    ...rest,
                    amount_received_from_buyer: (amount_received_from_buyer) ? Number(amount_received_from_buyer) : 0,
                    po_data:(!poData)?mappedOrders.filter(mapped=>mapped.po_number === rest.po_number)[0]?.po_data:{...poData, total_amount_received:(poData?.total_amount_received)?poData?.total_amount_received:0},
                    is_unmapped: false
                }
            });

            return { id, selected_orders:updatedOrders, processed_amount, total_amount_received_from_buyer }
        });
        const payload: MapTransactionToPoPayloadDto = {
            data:arr
        }
        return payload;
    }

    const saveCassMappingWithPo = (poNumber: any) => {
        if (poNumber.length !== 0) {
            const payload: MapTransactionToPoPayloadDto = getDto(poNumber);
            saveMapTransactionToPo(payload);
        }
        else {
            setShowSaveBtn(false);
        }
    }

    const showCloseOrderPopup = (poNumber: PoNumberListDto | null) => {
        const poNumberData: any[] = [];
        let isPoChanged = false;
        let isCompanyChanged = false;
        Object.values(poNumber ?? {}).forEach(_poObjects => {
            const poObjects = JSON.parse(JSON.stringify(_poObjects ?? []));
            if (poObjects?.length) {
                const firstObj = poObjects?.[0];

                let buyerTotalPurchase = 0;
                const selectedOrders: any[] = [];
                let isChanged = false;
                const fundingAmt = Number(firstObj.processed_amount?.toFixed(2));
                let selectedPoTotal = 0;

                poObjects.forEach((obj: any) => {
                    if (obj.is_checked) {
                        if (!isNaN(+obj.buyer_total_purchase)) {
                            selectedPoTotal += Number(obj.buyer_total_purchase);
                        }
                    }
                });

                poObjects.forEach((obj: any) => {
                    const defaultData = defaultProbablePoData[obj.id];
                    if (defaultData && defaultData.mapped_company_name !== obj.mapped_company_name && obj.is_mapped_name_changed) {
                        isCompanyChanged = true;
                        isChanged = true;
                    }

                    if (obj.is_changed) {
                        isChanged = true;
                        isPoChanged = true
                    }
                    const cassTransactionId = obj.user_purchase_order_id;
                    const poNumber = obj.po_number;
                    const autoCloseOrder = isPoChanged && fundingAmt === Number(selectedPoTotal?.toFixed(2));
                    const isChecked = obj.is_checked;
                    const buyerPoTotalPurchase = obj.buyer_total_purchase;

                    if (obj.is_checked) {
                        const poData = getPoListData?.find((getPoData: any) => getPoData.po_number === poNumber);

                        selectedOrders.push({
                            user_purchase_order_id: cassTransactionId,
                            po_number: poNumber,
                            amount_received_from_buyer:obj.amount_received_from_buyer,
                            is_checked: isChecked,
                            auto_close_order: autoCloseOrder,
                            buyer_po_total_purchase: buyerPoTotalPurchase,
                            poData: poData,
                        });

                        buyerTotalPurchase += obj.buyer_total_purchase;
                    }
                });
                if (!(isChanged)) {
                    return
                }

                let isAnyOnePoOpen = selectedOrders.some(obj => obj.poData);
                if(isPoChanged)
                    poNumberData.push({
                        id: firstObj.id,
                        company_name: firstObj.company_name,
                        mapped_company_name: firstObj.mapped_company_name,
                        selected_orders: selectedOrders,
                        processed_amount: firstObj.processed_amount,
                        buyer_total_purchase: parseFloat(buyerTotalPurchase.toFixed(2)),
                        is_changed: isPoChanged,
                        isAnyOnePoOpen: isAnyOnePoOpen,
                        total_amount_paid_by_buyer:firstObj.amount_received_from_buyer,
                    });
            }
        });
        const isAnyHasSelectedOrders = poNumberData.some(obj => obj.selected_orders.some((po) => {
            return getPoListData?.find((x) => x.po_number === po.po_number)
        }));
        if (isPoChanged && isAnyHasSelectedOrders) {
            if (Array.isArray(poNumberData)) {
                setCloseOrderPopupData(() => poNumberData.map((order: any) => {
                    let total_amount_paid_by_buyer = 0;
                    return {
                        ...order,
                        selected_orders: order.selected_orders ? order.selected_orders.map((selectedOrder: any, index: number) => {
                            const amount_received_from_buyer = (order.selected_orders.length === 1 && order.processed_amount === selectedOrder.buyer_po_total_purchase && !selectedOrder?.amount_received_from_buyer) ? selectedOrder.buyer_po_total_purchase : (selectedOrder?.amount_received_from_buyer)?selectedOrder?.amount_received_from_buyer:0;
                            total_amount_paid_by_buyer += amount_received_from_buyer;
                            return {
                                ...selectedOrder,
                                amount_received_from_buyer:amount_received_from_buyer,
                                isClosed:selectedOrder?.poData?false:true
                            }
                        }) : [],
                        total_amount_paid_by_buyer:total_amount_paid_by_buyer
                    }}));
            }
        } else {
            saveCassMappingWithPo(poNumberData)
        }
    }

    const hideCloseOrderPopup = () => {
        setCloseOrderPopupData(null);
    }

    const submitCloseOrderPopup = () => {
        saveCassMappingWithPo(closeOrderPopupData);
        hideCloseOrderPopup();
    }

    const handleShowSubmitBtn = () => {
        const filterData = Object.values(poNumber ?? {}).filter(Boolean);
        const checkDefault = filterData.some((data: any) => {
            let dataChanged = false;
            data.forEach((objData) => {
                const defaultData = defaultProbablePoData[objData.id];
                if (objData.is_changed) {
                    dataChanged = true;
                }

            });
            return dataChanged;
        })
        setShowSubmitBtn(showSaveBtn || checkDefault)
    }

    const handleCassMappingTableData = (cassData: any) => {
        setPoNumber((prev) => {
            const _prev = JSON.parse(JSON.stringify(prev ?? {}))
            const poNumberList: any = _prev;
            const cassMappingList: any = [...cassData];
            cassMappingList.forEach((cassMappedData: any, i: number) => {
                let mappedOrderBuyerPoTotalPurhchase = 0;
                const mappedData = !cassMappedData?.mapped_orders ? [] :
                    (JSON.parse(cassMappedData.mapped_orders ?? []).map((data: any) => {
                        const poData = getPoListData?.find((getPoData: { po_number: any; }) => getPoData.po_number === data.po_number);
                        const cassStoreData = poNumberList[cassMappedData.id]?.find((getPoData: { po_number: any; }) => getPoData.po_number === data.po_number);
                        if (cassStoreData) {
                            return null;
                        }
                        const obj = {
                            "id": cassMappedData.id,
                            "po_number": data.po_number,
                            "user_purchase_order_id": data.user_purchase_order_id,
                            "transfer_type": null,
                            "buyer_company_name": poData?.buyer_company_name,
                            "buyer_name": poData?.buyer_name ?? data?.po_data?.buyer_name ?? null,
                            "seller_name":poData?.seller_name ?? data?.po_data?.seller_name ?? null,
                            "is_changed": data?.is_changed ?? false,
                            "isEdit": true,
                            "is_checked": data.is_checked,
                            "buyer_total_purchase": data?.buyer_po_total_purchase ?? poData?.buyer_total_purchase ?? null,
                            "mapped_company_name": cassMappedData.mapped_company_name,
                            "processed_amount": +cassMappedData.processed_amount,
                            "is_po_closed": !poData,
                            "auto_close_order": false,
                            "is_mapped_name_changed": false,
                            "amount_received_from_buyer": data?.amount_received_from_buyer
                        }
                        mappedOrderBuyerPoTotalPurhchase += (data?.buyer_po_total_purchase ?? poData?.buyer_total_purchase ?? 0)
                        return obj;
                    }).filter(Boolean))

                if (poNumberList[cassMappedData.id]) {
                    poNumberList[cassMappedData.id] = [...poNumberList[cassMappedData.id], ...mappedData];
                } else {
                    poNumberList[cassMappedData.id] = mappedData;
                }
                const filterData = filteredDataToShow.find((data: { company_name: any; }) => data.company_name === cassMappedData.company_name);
                if (filterData) {
                    cassData[i].mapped_company_name = filterData.mapped_company_name;
                }
            });
            setFilteredDataToShow(cassData)
            return poNumberList;
        });
    }

    const handleShowMatchedMappedDataCheckBox = () => {
        setShowMatchedTransactionCheckbox(!showMatchedTransactionCheckbox);
    }

    const goToPage = () => {
        const pageNumber = Number(goToPageNumber);
        setCurrentPage(pageNumber);
        setGoToPageNumber("");
        setDisableGoToPageBtn(true);
    }

    const goToPageNumberBtnValidation = (pageNumber: number) => {
        if (meta && (pageNumber > 0 && pageNumber <= meta.totalPages)) {
            setDisableGoToPageBtn(false);
        } else {
            setDisableGoToPageBtn(true)
        }
    }

    return (
        <div>
            {(isGetProbablePoLoading ||
                isGetProbablePoFetching ||
                isGetPoListLoading ||
                isGetPoListFetching ||
                allMappedCompanyLoading ||
                isSaveMapTransactionToPoLoading) && (
                    <>
                        <div className={styles.backDrop}></div>
                        <div className={styles.loaderImg}>
                            <Loader />
                        </div>
                    </>

                )}
            <div>
                <div className={styles.searchBox}>
                    <Select
                        className={styles.showdropdwn}
                        value={itemsPerPage}
                        onChange={(event) => {
                            setItemsPerPage(+event.target.value);
                            setCurrentPage(1);
                        }}
                    >
                        {perPageEntriesOptions.map((item, index) => (
                            <MenuItem key={index} value={item}>
                                <span>{item}</span>
                            </MenuItem>
                        ))}
                    </Select>
                    <div className={styles.rightEditSection}>
                        <span className={clsx(styles.showAllTranChk, 'cassChkBox')} onClick={() => { handleShowMatchedMappedDataCheckBox() }}>
                            <span className="containerChk">
                                <input type="checkbox" checked={showMatchedTransactionCheckbox} />
                                <span className="checkmark" />
                                Show Matched Transactions
                            </span>
                        </span>
                        {showSubmitBtn && (<button
                            className={styles.saveBtnf}
                            onClick={() => { showCloseOrderPopup(poNumber) }}
                            disabled={disableSubmitBtn}
                        >
                            Submit
                        </button>
                        )}

                        <input
                            className={styles.searchInput}
                            type="text"
                            onChange={(event) => setInputSearchValue(event.target.value)}
                            value={inputSearchValue}
                            placeholder="Search"
                        />
                    </div>
                </div>

                <CassMappingTransactionTable
                    tableData={filteredDataToShow}
                    allCompany={allMappedCompanies}
                    setTableData={setFilteredDataToShow}
                    setShowSaveBtn={setShowSaveBtn}
                    setPoNumber={setPoNumber}
                    poNumber={poNumber}
                    getPoListData={getPoListData}
                    itemOffset={itemOffset}
                    setInputValue={setInputValue}
                    inputValue={inputValue}
                    defaultProbablePoData={defaultProbablePoData}
                    setDisableSubmitBtn={setDisableSubmitBtn}
                />
                <div className="PaginationNumber">
                    <div className="saveBtn">
                        {meta && <>
                            <ReactPaginate
                                breakLabel="..."
                                nextLabel=">"
                                onPageChange={handlePageClick}
                                pageRangeDisplayed={5}
                                pageCount={meta.totalPages}
                                previousLabel="<"
                                renderOnZeroPageCount={(props) =>
                                    props.pageCount > 0 ? undefined : null
                                }
                                forcePage={meta.currentPage > 0 ? meta.currentPage - 1 : undefined}
                            />

                        </>
                        }

                        {showSubmitBtn && (
                            <button
                                className={styles.saveButton}
                                onClick={() => { showCloseOrderPopup(poNumber) }}
                                disabled={disableSubmitBtn}
                            >
                                Submit
                            </button>
                        )}
                    </div>
                </div>
                {meta && <span className={styles.goToPageNumberInput}>
                    <span>Page</span>
                    <input
                        type="text"
                        value={goToPageNumber}
                        placeholder="Enter Page No"
                        onChange={(e) => {
                            const goToPageNumberOnChangeValue = e.target.value.replace(/\D/g, '');
                            setGoToPageNumber(goToPageNumberOnChangeValue);
                            goToPageNumberBtnValidation(Number(goToPageNumberOnChangeValue))
                        }}
                        onKeyDown={(e) => { (e.key === "Enter" && !disableGoToPageBtn) && goToPage() }}
                    />
                    <span>of {meta.totalPages}</span>
                    <button className={styles.goBtn} disabled={disableGoToPageBtn} onClick={goToPage}>Go</button>
                </span>
                }

            </div>
            <CloseOrderConfirmationPopup
                data={closeOrderPopupData}
                setData={setCloseOrderPopupData}
                close={hideCloseOrderPopup}
                submit={submitCloseOrderPopup}
                defaultProbablePoData={defaultProbablePoData}
                getPoListData={getPoListData}
            />
        </div>
    )
}

export default CassMappingTransaction;