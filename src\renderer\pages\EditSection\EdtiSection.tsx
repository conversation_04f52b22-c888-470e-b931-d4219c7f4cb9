import React from "react";
import Loader from "../../components/common/Loader";
import styles from "./EditSection.module.scss";
import { filterArrray } from "../../utils/helper";
import { useEffect, useState, useContext , useRef } from "react";
import { useImmer } from "use-immer";
import { Tooltip } from "@mui/material";
import { cloneDeep, find } from "lodash";
import _ from "lodash";
import { convertUtcToCtTimeUsingDayjs } from "@bryzos/giss-ui-library";
import useGetAllVideoLibraryTags from "../../hooks/useGetAllVideoLibraryTags";
import MatPopup from "../../components/common/MatPopup";
import AddTag from "./components/AddTag";
import usePostUpdateTag from "../../hooks/usePostUpdateTag";
import usePostAddTag from "../../hooks/usePostAddTag";
import { CommonCtx } from "../AppContainer";
import {splitAndFormatItem} from "../../utils/helper"
import { isRequired, string, videoTagsFields , } from "../../utils/constant";
import clsx from "clsx";
function EdtiSection() {
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [showSaveBtn, setShowSaveBtn] = useState(false);
  const [filteredTags, setFilteredTags] = useImmer<any[]>([]);
  const [tagsList, setTagsList] = useImmer<any[]>([]);
  const [errorTooltip, setErrorTooltip] = useState<any>({});
  const [addTagPopup, setAddTagPopup] = useState(false);
  const showPopupFormAnyComponent = useContext(CommonCtx);
  const [uploadVideoLoaderText, setUploadVideoLoaderText] = useState(false);
  const [showLoader, setShowLoader] = useState(false);
  const nameInputRef = useRef<any>({});
  const { data: videoLibraryTagsData, isLoading } = useGetAllVideoLibraryTags();

  const {
    data: updateTagData,
    mutateAsync: updateTag,
    isLoading: isUpdateTagLoading,
  } = usePostUpdateTag();

  const {
    data: postAddTagData,
    isLoading: isAddTagLoading,
    mutateAsync: addTag,
  } = usePostAddTag();

  useEffect(() => {
    const tagsData = videoLibraryTagsData?.length ? videoLibraryTagsData : [];

    setTagsList(tagsData);

    setErrorTooltip({})
    if (tagsData?.length > 0 && inputSearchValue.length != 0) {
      search(inputSearchValue, tagsData);
    } else {
      setFilteredTags(tagsData);
    }
  }, [videoLibraryTagsData, isLoading]);

  useEffect(() => {
    if (!isUpdateTagLoading) {
      if (updateTagData) {
        showPopupFormAnyComponent(updateTagData);
      }
    }
  }, [updateTagData, isUpdateTagLoading]);

  useEffect(() => {
    if (!isAddTagLoading) {
      if (postAddTagData) {
        showPopupFormAnyComponent(postAddTagData);
      }
    }
  }, [postAddTagData, isAddTagLoading]);

  const search = (searchString: string, tagsData: any = tagsList) => {
    setInputSearchValue(searchString);
    if (searchString) {
      const _filterArrray = filterArrray(tagsData, searchString, [
        "display_subtitle",
        "name",
        "query_param",
        "display_title",
      ]);
      if (_filterArrray?.length) {
        setFilteredTags(_filterArrray);
      } else {
        setFilteredTags([]);
      }
    } else {
      setFilteredTags(tagsData);
    }
  };

  const arrayisEqual = (videoLibraryTagsData: any[], tagsList: any[]) => {
    return _.isEqual(videoLibraryTagsData, tagsList);
  };

  useEffect(() => {
    setErrorTooltip({})
    if (!arrayisEqual(videoLibraryTagsData, tagsList) && checkForm() && tagsList.length > 0) {
      setShowSaveBtn(true);
    } else {
      setShowSaveBtn(false);
    }
  }, [tagsList, filteredTags]);

  const checkForm = () => {
    let isValid = true; 
    const newErrorTooltip:any = {};
    tagsList.forEach((tag) => {
      for (let item in tag) {
        if(typeof tag[item] === string){
          if ((tag[item] === null || tag[item].trim().length <= 0) && nameInputRef?.current) {       
            let formattedTag = splitAndFormatItem(item , '_')    
            newErrorTooltip[item + tag.id] = `${formattedTag}  ${isRequired}`;
            isValid = false;             
            nameInputRef?.current[item + tag?.id]?.classList?.add(styles.highlightInput);
          } else {
            newErrorTooltip[item + tag.id] = ``;
            nameInputRef?.current[item + tag?.id]?.classList?.remove(styles.highlightInput);
          }
        }
      }
        if (!isValid) {
        return;
      }
    });
    setErrorTooltip(newErrorTooltip);
    return isValid;
  };

  const setInputText = (
    inputEvent: any,
    key: string,
    index: number,
    data: any
  ) => {
    const spreadTagsData = cloneDeep(tagsList);
    setFilteredTags((prev: any) => {
      const tag = prev[index];
      tag[key] = inputEvent.target.value;
      return prev;
    });
    const findTag = spreadTagsData.find(
      (uploadData) => uploadData.id === data.id
    );
    if (findTag) {
      findTag[key] = inputEvent.target.value;
    }
    setTagsList(spreadTagsData);
  };

  const handleCheckbox = (
    event: any,
    index: number,
    key: string,
    data: any
  ) => {
    const spreadTagsData = cloneDeep(tagsList);
    setFilteredTags((prev) => {
      const isChecked = event.target.checked;
      prev[index][key] = isChecked;
    });

    const findTag = spreadTagsData.find(
      (uploadData) => uploadData.id === data.id
    );
    if (findTag) {
      findTag[key] = event.target.checked;
    }

    setTagsList(spreadTagsData);
  };

  const save = () => {
    const changedData = filteredTags.filter((tag, index) => {
      for (let item in tag) {
        if (tag[item] != videoLibraryTagsData[index][item]) {
          return true;
        }
      }
      return false;
    });

    const payloadData = changedData.map((tag) => {
      return {
        id: tag?.id,
        name: tag.name,
        display_title: tag.display_title,
        display_subtitle: tag.display_subtitle,
        query_param: tag.query_param,
        show_on_app: tag.show_on_app,
        show_on_safe: tag.show_on_safe,
        add_at_top : Number(tag.add_at_top),
        shuffle_sequence : Number(tag.shuffle_sequence)
      };
    });
    const payload = {
      data: payloadData,
    };
    updateTag(payload);
  };

  const handleAddTagOnClick = () => {
    setAddTagPopup(true);
  };

  const confirmationPopupClose = () => {
    setAddTagPopup(false);
  };

  return (
    <div>
      {isLoading || isAddTagLoading || isUpdateTagLoading || showLoader ? (
        <div className={styles.loaderImg}>
          <Loader></Loader> {uploadVideoLoaderText}
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <>
              {showSaveBtn && (
                <button className={styles.saveBtnf} onClick={save}>
                  Save
                </button>
              )}
              <button className={styles.addBtnf} onClick={handleAddTagOnClick}>
                Add Tag
              </button>
              <input
                className={styles.searchInput}
                type="text"
                onChange={(event) => search(event.target.value)}
                value={inputSearchValue}
                placeholder="Search"
              />
            </>
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Display Title</th>
                  <th>Display Subtitle</th>
                  <th>Query Param </th>
                  <th>
                    Show On <br></br> App
                  </th>
                  <th>
                    Show on <br /> Safe
                  </th>
                  <th>Add at top</th>
                  <th>Shuffle sequence</th>
                  <th>Created at</th>
                </tr>
              </thead>
              <tbody>
                {filteredTags?.length > 0 ? (
                  filteredTags.map((tag: any, i) => (
                    
                    <tr key={tag.id}>
                      <td>
                        <Tooltip
                          title={errorTooltip[videoTagsFields.name + tag.id]}
                          placement="top-end"
                          classes={{
                            popper: styles.errorStyle,
                            tooltip: styles.tooltip,
                          }}
                        >
                          <input
                            className={styles.messageText}
                            type="text"
                            value={tag.name ?? ""}
                            onChange={(e) => setInputText(e, videoTagsFields.name, i, tag)}
                            ref={(ref)=>nameInputRef.current[videoTagsFields.name+tag.id] = ref}
                          />
                        </Tooltip>
                      </td>
                      <td>
                        <Tooltip
                          title={errorTooltip[videoTagsFields.display_title + tag.id]}
                          placement="top-end"
                          classes={{
                            popper: styles.errorStyle,
                            tooltip: styles.tooltip,
                          }}
                        >
                          <input
                            className={styles.messageText}
                            type="text"
                            value={tag.display_title ?? ""}
                            onChange={(e) =>
                              setInputText(e, videoTagsFields.display_title, i, tag)
                            }
                            ref={(ref)=>nameInputRef.current[videoTagsFields.display_title+tag.id] = ref}
                          />
                        </Tooltip>
                      </td>
                      <td>
                        <Tooltip
                          title={errorTooltip[videoTagsFields.display_subtitle + tag.id]}
                          placement="top-end"
                          classes={{
                            popper: styles.errorStyle,
                            tooltip: styles.tooltip,
                          }}
                        >
                          <input
                            className={styles.messageText}
                            type="text"
                            value={tag.display_subtitle ?? ""}
                            onChange={(e) =>
                              setInputText(e, videoTagsFields.display_subtitle, i, tag)
                            }
                            ref={(ref)=>nameInputRef.current[videoTagsFields.display_subtitle+tag.id] = ref}
                          />
                        </Tooltip>
                      </td>
                      <td>{tag.query_param}</td>
                      <td>
                        <input
                          type="checkbox"
                          checked={tag.show_on_app}
                          onChange={(e) =>
                            handleCheckbox(e, i, videoTagsFields.show_on_app, tag)
                          }
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          checked={tag.show_on_safe}
                          onChange={(e) =>
                            handleCheckbox(e, i,videoTagsFields.show_on_safe, tag)
                          }
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          checked={tag.add_at_top}
                          onChange={(e) =>
                            handleCheckbox(e, i, videoTagsFields.add_at_top, tag)
                          }
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          checked={tag.shuffle_sequence}
                          onChange={(e) =>
                            handleCheckbox(e, i,videoTagsFields.shuffle_sequence, tag)
                          }
                        />
                      </td>                      
                      <td>{convertUtcToCtTimeUsingDayjs(tag.created_date)}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className={"noDataFoundTd"}>
                      No data found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
      <MatPopup
        open={addTagPopup}
        className={styles.orderContinuePopup}
        classes={{
          paper: clsx(styles.uploadVideoPopup,styles.addTagPopup),
        }}
      >
        <div className={styles.uploadVideoTitle}>Add Tag</div>

        <AddTag
          confirmationPopupClose={confirmationPopupClose}
          addTag={addTag}
          setUploadVideoLoaderText={setUploadVideoLoaderText}
          setShowLoader={setShowLoader}
        />
      </MatPopup>
    </div>
  );
}

export default EdtiSection;
