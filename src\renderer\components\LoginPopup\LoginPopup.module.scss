.passwordBox {
    gap: 15px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        gap: 8px;
    }

    .inputPass {
        min-width: 41px;
        height: 34px;
        line-height: 32px;
        font-weight: 400; 
        color: #495057;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 30px;
        padding: 0px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            min-width: 37px;
            font-size: 20px;
        }

        &:focus-visible {
            outline: 1px solid #000;
        }
    }
}

.loginPopup {
    .loaderImg {
        width: 360px;
        height: 330px;
        align-items: center;
        display: flex;
        justify-content: center;
    }

    h2 {
        display: none;
    }

    form {
        padding: 20px;
        text-align: center;

        img {
            padding-bottom: 20px;
        }

        .inputLoginPass {
            display: flex;
            flex-direction: column;
            gap: 20px;
            position: relative;

            .loginFiled {
                width: 100%;
                height: 40px;
                border-radius: 6px;
                border: 1px solid #ced4da;
                padding: 6px 12px;
                font-size: 16px;

                &:focus {
                    outline: 1px solid #000;
                }
            }

            .passField {
                display: flex;
            }

            .errorMessage {
                position: absolute;
                bottom: -30px;
                color: red;
            }

            .InputFieldcss {
                width: 320px;
                height: 40px;
                padding-left: 20px;
                font-weight: 400;
                line-height: 1.5;
                color: #495057;
                background-color: #fff;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 15px;

                @media screen and (max-width: 768px) and (min-width: 320px) {
                    width: 265px;

                }

                &:focus-visible {
                    border: 1px solid #000;
                }
            }

            .pass {
                width: 280px !important;
                border-top-right-radius: 0px !important;
                border-bottom-right-radius: 0px !important;
            }

            .showHidePass {
                width: 40px;
                height: 40px;
                border: 1px solid #ced4da;
                cursor: pointer;
                outline: none;
                padding: 0;
                margin: 0;
                border-radius: 4px;
                border-top-left-radius: 0px;
                border-bottom-left-radius: 0px;
            }

            @media screen and (max-width: 768px) and (min-width: 320px) {
                display: block;
                margin-bottom: 10px;

            }

        }
        
        .loginBtn {
            width: 220px;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            gap: 8px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
            margin-top: 40px;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }
    }

}

.closeBtn {
    position: absolute;
    top: 5px;
    right: 14px;
    cursor: pointer;
    font-size: 27px;
    color: #bbbdc3;
    background: transparent;
    border: 0;
    padding: 0;
    outline: none;

    &:hover {
        color: #000;
    }
}

.requestAccess {
    padding-top: 10px;
    font-size: 14px;
    color: #626b84;
    text-align: center;
}

.bryzosIcon {
    width: 220px;
    height: 84px;
    margin-bottom: 20px;
}