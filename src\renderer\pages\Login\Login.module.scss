* {
    box-sizing: border-box;
}

.loginPage {
    border: 3px solid #f1f1f1;
    width: 100%;
    max-width: 600px;
    padding: 24px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 100px;
    background-color: #f8f9fc;
    border-radius: 7px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        border: 3px solid #f1f1f1;
        padding: 20px 15px 30px;
        margin-left: auto;
        margin-right: auto;
        width: 300px;
    }

    .inputFiledLoginPass {
        display: flex;
        width: 100%;
        height: 65px;
        align-items: center;
        .pass{
            width: 280px !important;
            border-top-right-radius: 0px !important;
            border-bottom-right-radius: 0px !important;
        }
        .showHidePass{
            width: 40px;
            height: 40px;
            border: 1px solid #ced4da;
            cursor: pointer;
            outline: none;
            padding: 0;
            margin: 0;
            border-radius: 4px;
            border-top-left-radius: 0px;
            border-bottom-left-radius: 0px;
        }
        @media screen and (max-width: 768px) and (min-width: 320px) {
            display: block;
            margin-bottom: 10px;

        }

        .InputFieldcss {
            width: 320px;
            height: 40px;
            padding-left: 20px;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 15px;

            @media screen and (max-width: 768px) and (min-width: 320px) {
                width: 265px;

            }

            &:focus-visible {
                border: 1px solid #000;
            }
        }

        .emailText {
            width: 200px;
            font-size: 16px;
            font-weight: 600;

            @media screen and (max-width: 768px) and (min-width: 320px) {
                margin-bottom: 10px;

            }
        }
    }

    .loginBtn {
        padding: 7px 11px;
        font-size: 18px;
        background-color: var(--primaryColor) !important;
        color: #fff;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        user-select: none;
        border: 1px solid transparent;
        border-radius: 0.25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        cursor: pointer;
        margin-bottom: 5px;
    }

    .wrongEmail {
        color: red;
    }
}

.passwordBox {
    gap: 15px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        gap: 8px;

    }

    .inputPass {
        min-width: 41px;
        line-height: 32px;
        font-weight: 400;
        color: #495057;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 30px;
        padding: 0px;
        @media screen and (max-width: 768px) and (min-width: 320px) {
            min-width: 37px;
            font-size: 20px;
    
        }

        &:focus-visible {
            outline: 1px solid #000;
        }
    }
}

.loaderImg {

    text-align: center;
    margin-left: auto;
    margin-right: auto;
    
  
  }