import clsx from "clsx";
import Loader from "../../../../components/common/Loader";
import { formatCurrency } from "@bryzos/giss-ui-library";
import styles from "../CassMappingTransaction.module.scss";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { PoList } from "@bryzos/giss-common-lib";
import CassMappingTransactionFileData from "./cassMappingTransactionFileData";
import CassMappingTransactionAutoComplete from "./cassMappingTransactionAutoComplete";
import { Autocomplete, AutocompleteRenderInputParams, Box, ClickAwayListener, TextField, Tooltip } from "@mui/material";
import { tooltipText } from "../../../../utils/constant";

const CassMappingTransactionTable = ({ allCompany, tableData, setTableData, setShowSaveBtn, setPoNumber, 
                                        poNumber, getPoListData, itemOffset, endOffset, isEdit, setInputValue, 
                                        inputValue, isGetCassMappedPoLoading, defaultProbablePoData, setDisableSubmitBtn }: any) => {

    const handlePoNumberOptions = (_cassData: any) => {
        const probablePo: any[] = JSON.parse(JSON.stringify(_cassData?.po_numbers ?? []));
        const mappedOrders = !_cassData?.mapped_orders ? [] : JSON.parse(_cassData.mapped_orders ?? []);
        let poListData: PoList[] = [];
        let probablePoData: { po_number: any; group: string; }[] = [];
        if (probablePo && probablePo.length !== 0) {
            probablePoData = probablePo.filter((data, i, self) => {
                data.group = 'Probable POs';
                return data;
            })
        }
        if (getPoListData?.length) {
            const _getPoListData = JSON.parse(JSON.stringify(getPoListData));
            poListData = _getPoListData.filter((data: any) => {
                if (probablePo && !probablePo.some(poData => poData.po_number === data.po_number)) {
                    data.group = 'po List';
                    return data;
                } else if (!probablePo?.length) {
                    data.group = 'po List';
                    return data;
                }
            })
        }
        const optionList: any[] = [...probablePoData, ...poListData];
        return optionList;
    }

    const getBuyerTotalPurchase = (cassMappingId: string) => {
        let totalPurchase = 0;
        const selectedPos: any = [];

        if (poNumber?.[cassMappingId]?.length) {
            const arr: any[] = poNumber[cassMappingId];
            arr.forEach((obj)=>{
                if(obj.is_checked){
                    selectedPos.push({...obj});
                    if (!isNaN(+obj.buyer_total_purchase)) {
                        totalPurchase += Number(obj.buyer_total_purchase);
                    }
                }
            })
        }
        return { totalPurchase: formatCurrency(totalPurchase), selectedPos: selectedPos };
    }

    const getCommaSeparatedText = (array: any[], key: string): string => {
        // Use map to extract the key values from each object and join them with a comma
        if(!array){
            return "";
        }
        return array
            .map(item => {
                // Check if the key exists at the top level or nested in po_data
                if (item[key]) {
                    return item.po_number + " - " + item[key];
                } else if (item.po_data && item.po_data[key]) {
                    return item.po_number + " - " + item.po_data[key];
                }
                return "";
            })
            .filter(value => value !== "") // Remove empty strings (in case the key is not found)
            .join(",<br>");
    }

    const formatSelectedPOData = (selectedPos:any[], key:string, isCurrency:boolean, showClosed:boolean)=>{
        return selectedPos?.map(obj =>{ 
                if(!isCurrency && !obj[key])
                    return '';
                return `${obj.po_number} - ${isCurrency?formatCurrency(obj[key] ?? 0):obj[key]}${(showClosed && obj.is_po_closed) ? " <span>(Closed)</span>" : ""}`
            }
          )?.filter(str => str!=="")?.join(", <br/>");
    }

    const TableDataComponent = () => {
        const processedData = useMemo(() => {
            if (isGetCassMappedPoLoading) {
                return <tr><td colSpan={4}><span className={styles.loaderPosition}><Loader /></span></td></tr>
            } else if (tableData?.length > 0) {
                return tableData
                    .map((cassMappingdata: any) => {
                        const defaultData = defaultProbablePoData[cassMappingdata.id];
                        const { selectedPos, totalPurchase } = getBuyerTotalPurchase(cassMappingdata.id);
                        const selectedPosString = formatSelectedPOData(selectedPos, "amount_received_from_buyer",true, true);
                        const buyerNamesFormatted = formatSelectedPOData(selectedPos, "buyer_name", false, true);
                        const sellerNamesFormatted = formatSelectedPOData(selectedPos, "seller_name", false, true);
                        const isFundingAndTotalPurchaseAmountEqual = (defaultData && defaultData.mapped_orders && (Number(defaultData.processed_amount) === Number(cassMappingdata.mapped_pos_buyer_total))) ;
                        return (
                            <tr key={cassMappingdata.id} className={clsx(isFundingAndTotalPurchaseAmountEqual && styles.highlightTransaction)}>
                                <td>
                                    {(isFundingAndTotalPurchaseAmountEqual) &&
                                        <Tooltip
                                        title={tooltipText.cassMappingTransaction}
                                        placement='bottom-start'
                                        classes={{
                                            popper: styles.cassAutocompleteTooltipStyle,
                                            tooltip: styles.tooltip
                                        }}
                                    >
                                        <span className={styles.cassFileNameExclamation}>!</span>
                                    </Tooltip>
                                    }
                                </td>
                                <td>
                                    <div className={styles.cassFileNameDiv}>
                                        <CassMappingTransactionFileData cassMappingdata={cassMappingdata} />
                                        <span className={styles.cassFileName}>{cassMappingdata.cass_filename}</span>
                                    </div>
                                </td>
                                <td>{formatCurrency(cassMappingdata.processed_amount)}</td>
                                <td>{formatCurrency(cassMappingdata.mapped_pos_buyer_total)}</td>
                                <td className={styles.selectedPosTd}  dangerouslySetInnerHTML={{ __html: selectedPosString }}></td>
                                <td  className={styles.selectedPosTd}  dangerouslySetInnerHTML={{ __html: buyerNamesFormatted }}></td>
                                <td  className={styles.selectedPosTd}  dangerouslySetInnerHTML={{ __html: sellerNamesFormatted }}></td>
                                <td>
                                    <CassMappingTransactionAutoComplete
                                        cassMappingdata={cassMappingdata}
                                        options={handlePoNumberOptions(cassMappingdata)}
                                        selectedPos={selectedPos}
                                        poNumber={poNumber}
                                        setPoNumber={setPoNumber}
                                        inputValue={inputValue}
                                        setInputValue={setInputValue}
                                        setShowSaveBtn={setShowSaveBtn}
                                        isEdit={isEdit}
                                        totalPurchase={totalPurchase}
                                        setTableData={setTableData}
                                        tableData={tableData}
                                        getPoListData={getPoListData}
                                        defaultProbablePoData={defaultProbablePoData}
                                        setDisableSubmitBtn={setDisableSubmitBtn}
                                    />
                                </td>
                            </tr>
                        )
                    })

            } else {
                return <tr>
                    <td colSpan={8} className={clsx(styles.noDataFoundTd, "noDataFoundTd")}>
                        No data found
                    </td>
                </tr>

            }
        }, [allCompany, tableData, poNumber, isGetCassMappedPoLoading, inputValue, itemOffset, getPoListData, ]);

        return processedData;
    };

    const MappedCompanyAutoComplete = React.memo(({ cassMappingdata }: any) => {
        const cassMappingId = cassMappingdata.id;
        const mappedCompanyName = cassMappingdata?.mapped_company_name ?? "";

        const isAnyChange = useRef(false);

        const [open, setOpen] = useState(false);
        const [inputValue, setInputValue] = useState<any>(mappedCompanyName);
        const [autocompleteValue, setAutocompleteValue] = useState(mappedCompanyName);



        const handleClick = () => {
            setOpen(true);
        }

        const handleClickAway = () => {
            setOpen(false);
            if (isAnyChange.current) {
                handleInputChange(inputValue);
            }
        }

        const handleMappedCompanyNameChange = (value: string | undefined, cassMappingId: string) => {
            setPoNumber((prev: any) => {
                if (!prev) {
                    return prev;
                }
                if(prev[cassMappingId].length === 0){
                    prev[cassMappingId] = [{
                        id: cassMappingId,
                        po_number: null,
                        user_purchase_order_id: null,
                        transfer_type: null,
                        buyer_company_name: null,
                        company_name: cassMappingdata?.company_name ?? null,
                        is_changed: false,
                        isEdit: true,
                        is_checked: false,
                        buyer_total_purchase: null,
                        mapped_company_name: value,
                        processed_amount: +cassMappingdata.processed_amount,
                        auto_close_order: false,
                        is_mapped_name_changed: true,
                    }]
                }

                const companyName = prev?.[cassMappingId]?.[0]?.company_name;
                if (!companyName) {
                    return prev;
                }

                Object.keys(prev ?? {}).forEach((key: string) => {
                    const arr = prev[key];
                    arr?.forEach((element: any) => {
                        if (element.company_name === companyName) {
                            element.mapped_company_name = value;
                        }
                        if (element.id === cassMappingId) {
                        element.is_mapped_name_changed = true;
                        }
                    });
                });
                return prev;
            });

            const updateTableData = [...tableData];
            tableData.forEach((data: any, i: number) => {
                if(data.company_name === cassMappingdata.company_name){
                    updateTableData[i].mapped_company_name = value;
                }
            });
            setTableData(updateTableData);

        }

        const handleInputChange = (_inputValue: string) => {
            handleMappedCompanyNameChange(_inputValue, cassMappingId);
        }

        return <ClickAwayListener onClickAway={() => {}}>
            <Box sx={{ position: "relative" }}>
                {!open  ? (
                    <button className={clsx(styles.cassMapptingDropdownBtn,styles.minWidth1)} type="button" onClick={handleClick}>
                        <span className={inputValue ? styles.dataSelected : styles.placeholder}>{inputValue ?? "Choose One"}</span>
                        <span className={styles.arrowIcon}>
                            <svg focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDropDownIcon">
                                <path d="M7 10l5 5 5-5z"></path></svg>
                            <span></span>
                        </span>
                    </button>
                ) : (
                    <Autocomplete className={clsx(styles.selectDropdown, 'cassMappingDropdown')}
                        open={open}
                        options={allCompany?.length ? allCompany : []}
                        freeSolo={true}
                        renderInput={(params: AutocompleteRenderInputParams) => (
                            <TextField {...params} autoFocus={true} placeholder="Choose One / Enter New" />
                        )}
                        onBlur={handleClickAway}
                        inputValue={inputValue}
                        onInputChange={(e, value, reason) => {
                            if (reason === 'clear') {
                                isAnyChange.current = true;
                                setInputValue(value)
                                handleInputChange(value)
                            }
                            if (reason !== 'reset' || value.length > 0) {
                                isAnyChange.current = true;
                                setInputValue(value)
                            }
                        }}
                        getOptionLabel={(option: any) => option ?? ""}
                        renderOption={(props, option: any) => (
                            <span {...props} key={option}>
                                {option}
                            </span>
                        )}
                        classes={{
                            root: styles.autoCompleteDesc,
                            popper: styles.autocompleteDescPanel,
                            paper: styles.autocompleteDescInnerPanel,
                            listbox: clsx(styles.listAutoComletePanel1),
                        }}
                        isOptionEqualToValue={(option, value) => option === value}
                        value={autocompleteValue}
                        onChange={(e, data, reason) => {
                            isAnyChange.current = true;
                            if (reason === "selectOption") {
                                setInputValue(data)
                            }
                            setAutocompleteValue(data);
                            handleInputChange(data)
                            setOpen(false);
                        }}
                    />
                )}
            </Box>
        </ClickAwayListener>
    });

    return (
        <div className={styles.tblscroll}>
            <table>
                <thead>
                    <tr>
                        <th></th>
                        <th>Cass Filename</th>
                        <th>Processed Amount</th>
                        <th>Total Amount Received From The Buyer</th>
                        <th className={styles.selectedPosTh}>Amount Received From The Buyer</th>
                        <th>Buyer Name</th>
                        <th>Seller Name</th>
                        <th>PO Number</th>
                    </tr>
                </thead>
                <tbody>
                    {TableDataComponent()}
                </tbody>
            </table>
        </div>
    )

}

export default CassMappingTransactionTable