.container {
  display: inline-flex;
  grid-template-columns: repeat(6, 1fr);
  width: 100%;
  margin-bottom: 1rem;
  justify-content: space-between;
}

.gearButton {
  width: 38px;
  height: 38px;
  padding: 2px 10px;
  background-color: rgba(255, 255, 255, 0.04);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-family: Inter;
  font-size: 28px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: left;
  color: rgba(255, 255, 255, 0.04);
  &:nth-child(1){
    border-radius: 0px 0px 0px 13px;
  }
  &:last-child{
    border-radius: 0px 0px 13px 0px;
  }

  &.selected {
    background-color: #60E160;
    color: black;
    font-weight: 700;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

.gearNumber {
  font-size: 1.875rem;
  font-weight: 700;

  &.selected {
    color: black;
  }
} 