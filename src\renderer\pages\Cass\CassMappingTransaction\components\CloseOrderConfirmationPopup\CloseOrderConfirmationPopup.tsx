import { useImmer } from "use-immer";
import MatPopup from "../../../../../components/common/MatPopup";
import { useEffect, useRef, useState } from "react";
import { formatCurrency } from "@bryzos/giss-ui-library";
import styles from '../../CassMappingTransaction.module.scss'
import { ReactComponent as CloseIcon } from '../../../../../../assests/images/close-icon.svg';
import { update } from "lodash-es";

type Props = {
    data: any;
    close: () => void;
    submit: () => void;
    setData: (prev: any) => void;
    defaultProbablePoData: any;
    getPoListData: any;
};

const CloseOrderConfirmationPopup: React.FC<Props> = ({ data, close, submit, setData, defaultProbablePoData, getPoListData }) => {
    const [isAllPoClosed, setIsAllPoClosed] = useImmer(false);
    const [disableSubmit, setDisableSubmit] = useState(true);

    const hasZeroError = "Input cannot be 0.";
    const hasInvalidError = "Input can have 2 digits after decimal.";

    useEffect(() => {
        if (isAllPoClosed) {
            setIsAllPoClosed(false);
            submit();
        }
    }, [isAllPoClosed])

    useEffect(() => {
        const isInvalid = data?.some((row: any) =>
            row.selected_orders?.some((order: any) => {
                const amount = order.amount_received_from_buyer;
                const hasZero = amount === null || amount === 0 || isNaN(amount);
                const isInvalid = amount !== null && !isNaN(amount) && !/^\d+(\.\d{1,2})?$/.test(amount.toString());
                return hasZero || isInvalid;
            })
        );
        setDisableSubmit(isInvalid);
    }, [data]);

    const handleBlur = (dataIndex: number, selectedOrderIndex: number) => {
        setData((draftData: any) => {
            const amount = draftData[dataIndex].selected_orders[selectedOrderIndex]?.amount_received_from_buyer;
    
            draftData[dataIndex].selected_orders[selectedOrderIndex].hasZero =
                amount === null || amount === 0 || isNaN(amount);
            
            const isInvalid = amount !== null && !isNaN(amount) && !/^\d+(\.\d{1,2})?$/.test(amount.toString());
            draftData[dataIndex].selected_orders[selectedOrderIndex].hasInvalidAmount = isInvalid;
        });
    };

    const handleCheckboxChange = (value: boolean, objId: any, rowIndex: number, poIndex: number, poNumber: string, poObj, obj) => {
        setData((prevData: any) => {
            // Clone the previous data
            const updatedData = [...prevData];
            
            // Clone the specific object to modify it
            updatedData[rowIndex] = {
                ...updatedData[rowIndex],
                selected_orders: updatedData[rowIndex].selected_orders.map((order: any, index: number) =>
                    index === poIndex ? { ...order, auto_close_order: value } : order
                ),
            };
            
            return updatedData;
        });
    }

    const amountPaidByBuyerHandler = (event: React.ChangeEvent<HTMLInputElement>, dataIndex: number, selectedOrderIndex: number) => {
        const newValue = event.target.value === "" ? null : parseFloat(event.target.value);
        setData((prevData: any) => {
            const updatedData = [...prevData];
    
            // Clone the specific order to modify it
            updatedData[dataIndex] = {
                ...updatedData[dataIndex],
                selected_orders: updatedData[dataIndex].selected_orders.map((order: any, idx: number) =>
                    idx === selectedOrderIndex
                        ? { ...order, amount_received_from_buyer: newValue }
                        : order
                )
            };

            updatedData[dataIndex].total_amount_paid_by_buyer = updatedData[dataIndex].selected_orders.reduce((total, order) => {
                const amount = order.amount_received_from_buyer;
                return total + (amount && !isNaN(amount) ? amount : 0);
            }, 0);

    
            return updatedData;
        });
    };
    

    return !!data?.length ? (
        <MatPopup classes={{
            root: styles.closeOrderPopup,
            paper: styles.mainContent
        }} open={true}>
            <div className={styles.popupHeader}>
                <p>Edit Before Save</p>
                <button onClick={close}><CloseIcon /></button>
            </div>
            <div className={styles.scrollBox}>
                {data?.map((obj: any, rowIndex: any) => {
                    return (
                        <>
                            {obj.is_changed && obj.selected_orders?.length > 0 && obj.isAnyOnePoOpen &&
                                <div className={styles.closeOrderPopupBox} key={obj.id}>
                                    <div className={styles.closeOrderBox}>
                                      
                                        <div className={styles.chkMain}>
                                        <span className={styles.companyName}>
                                            Processed Amount: {formatCurrency(obj.processed_amount)}
                                        </span>
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>Please Check the box Against the PO# to Close it</th>
                                                        <th><span>Amount Received from the Buyer for This CASS Transaction</span></th>
                                                        <th><span>Buyer Total Purchase</span></th>
                                                        <th><span>Total Amount Received from the Buyer for all CASS Transactions</span></th>
                                                    </tr>

                                                </thead>
                                                <tbody>
                                                  
                                                     {obj.selected_orders?.map((poObj: any, poIndex: number) => {
                                                 return (
                                                    <tr key={poObj.id}>
                                                      
                                                    <td>
                                                            <div className={styles.chkBoxMain} key={poObj.po_number}>
                                                                 <div className={styles.chkBox}>
                                                                     <p>{poObj.po_number }{poObj.isClosed&&<span className={styles.closed}>(Closed)</span>}</p>
                                                                     {!poObj.isClosed&&<input type="checkbox" checked={(!!poObj.auto_close_order)} onChange={(e) => { handleCheckboxChange(e.target.checked, obj.id, rowIndex, poIndex, poObj.po_number, poObj, data); }} />}
                                                                 </div>
                                                            </div>
                                                    </td>
                                                    <td>
                                                            <div className={styles.chkBoxMain}>
                                                                
                                                                <div className={styles.AmtPaidByBuyer}>
                                                                    <input
                                                                        type="number"
                                                                        step="0.0001"
                                                                        value={obj?.selected_orders[poIndex]?.amount_received_from_buyer ?? ""}
                                                                        onChange={(event) => amountPaidByBuyerHandler(event, rowIndex, poIndex)}
                                                                        onBlur={() => handleBlur(rowIndex, poIndex)}
                                                                        className={poObj.hasInvalidAmount || poObj.hasZero ? styles.invalidInput : ""}
                                                                        disabled= {poObj.isClosed}
                                                                    />
                                                                    <span className={styles.errorMessage}>
                                                                    {poObj.hasZero && <span>{hasZeroError}</span>} 
                                                                    {poObj.hasInvalidAmount && <span>{hasInvalidError}</span>}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        
                                                    </td>
                                                    <td>
                                                        {formatCurrency(obj?.selected_orders[poIndex]?.buyer_po_total_purchase)}
                                                    </td>
                                                    <td>
                                                        {!poObj.isClosed && formatCurrency(obj?.selected_orders[poIndex]?.poData?.total_amount_received)}
                                                    </td>
                                                    </tr>
                                                );
                                            })} 
                                                    
                                                   
                                                   
                                                </tbody>
                                            </table>
                                            <span className={styles.companyName}>
                                                Total Amount Received From Buyer: {formatCurrency(isNaN(obj.total_amount_paid_by_buyer)||!obj.total_amount_paid_by_buyer?0:obj.total_amount_paid_by_buyer)}
                                            </span>
                                        </div>

                                    </div>

                                </div>
                            }
                        </>

                    );
                })}

            </div>
            <button className={styles.submitBtn} disabled={disableSubmit} onClick={submit}>Submit</button>
        </MatPopup>
    ) : (
        <></>
    );
};

export default CloseOrderConfirmationPopup;