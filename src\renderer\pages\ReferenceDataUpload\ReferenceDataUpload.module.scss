.referenceDataUpload {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    margin-bottom: 30px;

    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 10px 0;
    }

      p.referenceDataUploadNote{
      font-size: 20px;
      font-weight: 600;
      color: #ff0000;
      margin: 0 0 10px 0;
     }

    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .uploadSections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  .uploadSection {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    background-color: #fff;

    .sectionHeader {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      input{
        margin: 0px 5px 0px 0px;
        &[disabled]{
          opacity: 0.6;
          cursor: not-allowed;
        }
      }

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0 15px 0 0;
      }
    }

    .description {
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
      line-height: 1.5;
    }

    .fileInfo {
      margin-top: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;

      .fileName {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
      }

      .fileSize {
        font-size: 12px;
        color: #666;
      }

      .removeFile {
        background: none;
        border: none;
        color: #dc3545;
        cursor: pointer;
        font-size: 12px;
        text-decoration: underline;
        margin-top: 5px;
        padding: 0;

        &:hover {
          color: #b02a37;
        }
      }
    }

    .errorMessage {
      color: #dc3545;
      font-size: 12px;
      margin-top: 10px;
    }

    .successMessage {
      color: #28a745;
      font-size: 12px;
      margin-top: 10px;
    }
  }

  .uploadSection.selected {
    border-color: #1c40e7;
    background-color: #f8fbff;
  }

  .uploadSection.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .submitSection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;

    .selectionInfo {
      font-size: 14px;
      color: #666;

      .count {
        font-weight: 600;
        color: #333;
      }
    }

    .actionButtons {
      display: flex;
      gap: 15px;

      .submitButton {
        background-color: #1c40e7;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover:not(:disabled) {
          background-color: #1635c7;
        }

        &:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
      }

      .clearButton {
        background-color: transparent;
        color: #666;
        border: 1px solid #ccc;
        padding: 12px 24px;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;

        &:hover:not(:disabled) {
          background-color: #f5f5f5;
          border-color: #999;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .checkbox {
    width: 18px;
    height: 18px;
    accent-color: #1c40e7;
    cursor: pointer;
  }

  .loadingContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    font-size: 14px;
    color: #666;
  }
}
