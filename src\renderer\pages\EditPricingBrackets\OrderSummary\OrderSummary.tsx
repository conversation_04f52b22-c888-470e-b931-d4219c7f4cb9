import React, { useEffect, useState } from 'react';
import PricingBracket from './PricingBracket';
import styles from './OrderSummary.module.scss';
import clsx from 'clsx';
import { Fade, Tooltip } from '@mui/material';
import { ReactComponent as DropdownIcon } from '../../../../assests/images/DownArrow.svg';
interface WeightRange {
  min: number;
  max: number;
  gear: number;
}
interface OrderSummaryProps {
  numberOfBrackets?: number;
  bracketDivider?: number;
  minWeight?: number;
  numberOfGears?: number;
  animationTime?: number;
  pricingBrackets?: any;
  totalWeight?: number;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({ numberOfBrackets=12, bracketDivider=10, numberOfGears=6, animationTime=1000 , pricingBrackets, totalWeight= 1000}) => {
  const [weight, setWeight] = useState(0);
  const [totalNumberOfBarsFilled, setTotalNumberOfBarsFilled] = useState(0);
  const [weightRanges, setWeightRanges] = useState<any[]>([]);
  const [minWeight, setMinWeight] = useState(500);

  useEffect(() => {
    if (pricingBrackets) {
      const transformedRanges = (pricingBrackets).map((range:any, index: number) => ({
        min: Number(range.min_weight),
        max: Number(range.max_weight),
        gear: index + 1
      }));
      setMinWeight(transformedRanges[0]?.min);
      setWeightRanges(transformedRanges);
    }
  }, [pricingBrackets, numberOfGears]);


  const calculateBracket = (weightRange: WeightRange, weight: number): void => {
    const totalNumberOfBars = numberOfBrackets * bracketDivider;
    const perBar = (weightRange.max - weightRange.min + 1) / totalNumberOfBars;
    const barToBeFilled = Math.ceil((weight - weightRange.min + 1) / perBar);
    let totalBars = (weightRange.gear - 1) * numberOfBrackets * bracketDivider + barToBeFilled;
    if(totalBars <= 0){
      totalBars = 0;
    }
    if(totalBars >= (numberOfBrackets * bracketDivider * numberOfGears))
      totalBars = numberOfBrackets * bracketDivider * numberOfGears;
    setTotalNumberOfBarsFilled(totalBars);
  };

  const calculateGearPosition = (weight: number): void => {
    if(weightRanges.length > 0 &&  weight >= weightRanges[0]?.min) {
      const _weightRange = weightRanges?.find(range => weight >= range.min && weight <= range.max) ?? weightRanges[numberOfGears - 1];
      calculateBracket(_weightRange,weight);
    }
    else {
      setTotalNumberOfBarsFilled(0);
    }
  };
  useEffect(() => {
    const weight = Math.floor(totalWeight);
    setWeight(weight);
    calculateGearPosition(weight);
  }, [totalWeight]);

  // Generate bracket values with increasing increments

  const containerRef = React.useRef<HTMLElement>(null);

  return (
    <div className={clsx(styles.container,'orderSummaryContainer')}>
      <div className='bgEllips'></div>
      <div className='bgEllips1'></div>
      <div className={styles.fadeIn}>
        <PricingBracket 
          currentWeight={weight} 
          minWeight={minWeight} 
          numberOfBrackets={numberOfBrackets}
          bracketDivider={bracketDivider}
          numberOfGears={numberOfGears}
          totalNumberOfBarsFilled={totalNumberOfBarsFilled}
          animationTime={animationTime}
        />
        
        {/* Order Summary */}
        <div className={styles.slideUp} style={{ animationDelay: '100ms' }}>
          <div className={styles.summarySection}>
            <div className={styles.summaryRow}>
              <div className={styles.summaryRowLbl}>Material Total</div>
              <div className={styles.summaryRowNum}>$ 0.00 </div>
            </div>
            <div className={`${styles.summaryRow} ${styles.muted}`}>
              <div className={styles.summaryRowLbl}>Sales Tax</div>
              <div className={styles.summaryRowNum}>$ 0.00 </div>
            </div>
            <div className={`${styles.summaryRow} ${styles.muted}`}>
              <div className={styles.summaryRowLbl}>Deposit</div>
              <div className={styles.summaryRowNum}>$ 0.00 </div>
            </div>
            <div className={`${styles.summaryRow} ${styles.muted}`}>
              <div className={styles.summaryRowLbl}>Subscription</div>
              <div className={styles.summaryRowNum}>$ 0.00 </div>
            </div>
          </div>
          <div className={clsx(styles.summaryRow,styles.totalPurchase)}>
            <div className={styles.totalPurchaseLbl}>Total Purchase</div>
            <div className={styles.totalPurchaseNum}>$ 0.00 </div>
          </div>
          
          <div className={styles.disclaimer}>
            If you make an error, you will have an opportunity<br />
            to change or cancel your order via "Disputes."
          </div>

          {/* Place Order Button */}
          <button className={styles.methodOfPayment} disabled={true}>
            Method of Payment
            <span className={styles.dropdownIcon}>
              <DropdownIcon />
            </span>
          </button>
          <button 
            id="place-order-btn"
            className={`${styles.orderButton} ${styles.slideUp}`}
            style={{ animationDelay: '500ms' }}
            disabled={true}          >
            Place Order
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
