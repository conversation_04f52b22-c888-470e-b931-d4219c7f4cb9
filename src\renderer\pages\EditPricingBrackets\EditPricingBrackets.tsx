import React, { useState, useEffect, useContext } from 'react'
import styles from './EditPricingBrackets.module.scss'
import { Tooltip } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import useGetReferenceData from '../../hooks/useGetReferenceData';
import Loader from '../../components/common/Loader';
import OrderSummary from './OrderSummary/OrderSummary';
import usePostEditPricingBrackets from '../../hooks/usePostEditPricingBrackets';
import { CommonCtx } from '../AppContainer';

interface Bracket {
  gear: number;
  min_weight: number | null;
  max_weight: number | null;
  minError?: boolean;
  maxError?: boolean;
}

interface ReferenceDataBracket {
  id: string;
  min_weight: string;
  max_weight: string;
  is_active: number;
  created_date: string;
  time_stamp: string;
}

const EditPricingBrackets = () => {

  const [brackets, setBrackets] = useState<Bracket[]>(
    Array.from({ length: 6 }, (_, index) => ({
      gear: index + 1,
      min_weight: null,
      max_weight: null,
      minError: false,
      maxError: false
    }))
  )
  const [errorShown, setErrorShown] = useState(false);
  const [hasEmptyFields, setHasEmptyFields] = useState(true);
  const [totalWeight, setTotalWeight] = useState<any>(0);
  const { data: referenceData, isLoading: isGetReferenceDataLoading } = useGetReferenceData();
  const { mutate: postEditPricingBrackets, isLoading: isPostEditPricingBracketsLoading, data: postEditPricingBracketsData } = usePostEditPricingBrackets();
  const showPopupFormAnyComponent = useContext(CommonCtx);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (referenceData?.ref_weight_price_brackets) {
      const refBrackets = referenceData.ref_weight_price_brackets as ReferenceDataBracket[];

      if (refBrackets && refBrackets.length > 0) {
        const formattedBrackets = refBrackets.map((bracket, index) => ({
          gear: index + 1,
          min_weight: parseFloat(bracket.min_weight),
          max_weight: parseFloat(bracket.max_weight),
          minError: false,
          maxError: false
        }));

        setBrackets(formattedBrackets);
      }
    }
  }, [referenceData])

  useEffect(() => {
    const emptyFieldsExist = brackets.some(bracket =>
      bracket.min_weight === null || bracket.max_weight === null
    );
    setHasEmptyFields(emptyFieldsExist);
    setTotalWeight(0);
  }, [brackets]);

  useEffect(() => {
    if (postEditPricingBracketsData) {
      showPopupFormAnyComponent(postEditPricingBracketsData);
    }
  }, [postEditPricingBracketsData])

  const validateBrackets = (bracketData: Bracket[]) => {
    const newBrackets = [...bracketData];
    let hasError = false;

    for (let i = 0; i < newBrackets.length; i++) {
      const currentMin = newBrackets[i].min_weight;
      const currentMax = newBrackets[i].max_weight;

      newBrackets[i].minError = false;
      newBrackets[i].maxError = false;

      if (currentMin !== null && currentMax !== null && currentMax <= currentMin) {
        newBrackets[i].maxError = true;
        hasError = true;
      }

      if (i > 0) {
        const prevMax = newBrackets[i - 1].max_weight;

        if (prevMax !== null && currentMin !== null) {
          const isValid = currentMin === prevMax + 1;
          newBrackets[i].minError = !isValid;

          if (!isValid) {
            hasError = true;
          }
        }
      }
    }

    setErrorShown(hasError);
    return newBrackets;
  };

  const handleInputChange = (rowIndex: number, field: 'min_weight' | 'max_weight', value: string) => {
    const newBrackets = [...brackets];
    newBrackets[rowIndex][field] = value === '' ? null : Number(value);

    const validatedBrackets = validateBrackets(newBrackets);
    setBrackets(validatedBrackets);
  }

  const handleSubmit = () => {
    const payload = brackets.map(bracket => ({
      min_weight: bracket.min_weight as number,
      max_weight: bracket.max_weight as number
    }));
    try {
      postEditPricingBrackets({ data: payload });
    } catch (error) {
      console.log(error);
    }
  }

  const handleAddGear = () => {
    const nextGearNumber = brackets.length + 1;

    const newBracket: Bracket = {
      gear: nextGearNumber,
      min_weight: null,
      max_weight: null,
      minError: false,
      maxError: false
    };

    const newBrackets = [...brackets, newBracket];
    setBrackets(newBrackets);
  }

  const handleDeleteGear = (index: number) => {
    if (index < 6) return;

    const newBrackets = [...brackets];
    newBrackets.splice(index, 1);

    newBrackets.forEach((bracket, i) => {
      bracket.gear = i + 1;
    });

    const validatedBrackets = validateBrackets(newBrackets);
    setBrackets(validatedBrackets);
  }

  const getMinTooltipText = (index: number): string => {
    const bracket = brackets[index];

    if (bracket.minError && index > 0) {
      const prevMax = brackets[index - 1].max_weight;
      if (prevMax !== null) {
        return `Value must be exactly ${prevMax + 1}`;
      }
    }

    return "";
  }

  const getMaxTooltipText = (index: number): string => {
    const bracket = brackets[index];

    if (bracket.maxError && bracket.min_weight !== null) {
      return `Value must be greater than ${bracket.min_weight}`;
    }

    return "";
  }

  const handlePreview = () => {
    setShowPreview(!showPreview);
  }

  return (
    <div className={styles.pricingBracketsContainer}>
      <div className={styles.editFlexContainer}>
        {
          isGetReferenceDataLoading || isPostEditPricingBracketsLoading ? <Loader /> :
            (
              <>
                <div className={styles.bracketTableContainer}>
                  {
                    <table className={styles.bracketTable}>
                      <thead>
                        <tr>
                          <th>Gear</th>
                          <th>Min Weight</th>
                          <th>Max Weight</th>
                          {brackets.length > 6 && <th>Action</th>}
                        </tr>
                      </thead>
                      <tbody>
                        {brackets.map((bracket, index) => (
                          <tr key={index}>
                            <td className={styles.gearColumn}>
                              {bracket.gear}
                            </td>
                            <td>
                              <Tooltip title={getMinTooltipText(index)} arrow>
                                <input
                                  type="number"
                                  className={`${styles.inputField} ${bracket.minError ? styles.error : ''}`}
                                  value={bracket.min_weight === null ? '' : bracket.min_weight}
                                  onChange={(e) => handleInputChange(index, 'min_weight', e.target.value)}
                                />
                              </Tooltip>

                            </td>
                            <td>
                              <Tooltip title={getMaxTooltipText(index)} arrow>
                                <input
                                  type="number"
                                  className={`${styles.inputField} ${bracket.maxError ? styles.error : ''}`}
                                  value={bracket.max_weight === null ? '' : bracket.max_weight}
                                  onChange={(e) => handleInputChange(index, 'max_weight', e.target.value)}
                                />
                              </Tooltip>
                            </td>
                            {brackets.length > 6 && (
                              <td className={styles.actionColumn}>
                                {index >= 6 && (
                                  <button
                                    className={styles.deleteButton}
                                    onClick={() => handleDeleteGear(index)}
                                    aria-label="Delete gear"
                                  >
                                    <DeleteIcon />
                                  </button>
                                )}
                              </td>
                            )}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  }
                  <div className={styles.actionButtons}>
                    <button
                      className={styles.addButton}
                      onClick={handleAddGear}
                    >
                      Add Gear
                    </button>
                    <button
                      className={styles.addButton}
                      onClick={handlePreview}
                    >
                      {showPreview ? 'Hide Preview' : 'Show Preview'}
                    </button>
                    <button
                      className={styles.submitButton}
                      onClick={handleSubmit}
                      disabled={errorShown || hasEmptyFields}
                    >
                      Submit
                    </button>
                  </div>
                </div>
                {
                  showPreview && (<div className={styles.gearContainer}>
                    <div className={styles.totalWeightContainer}>
                      <label className={styles.totalWeightLabel}>Enter Total Weight</label>
                      <input type="number" value={totalWeight} onChange={(e) => setTotalWeight(e.target.value)} className={styles.totalWeightInput} />
                    </div>
                    <OrderSummary
                      totalWeight={Number(totalWeight)}
                      numberOfBrackets={12}
                      bracketDivider={10}
                      numberOfGears={brackets.length}
                      animationTime={1000}
                      pricingBrackets={brackets}
                    />
                  </div>
                  )

                }

              </>
            )
        }

      </div>
    </div>
  )
}

export default EditPricingBrackets
