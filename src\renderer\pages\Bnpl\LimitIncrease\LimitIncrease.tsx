import { Select, MenuItem } from "@mui/material";
import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader/Loader";
import MatPopup from "../../../components/common/MatPopup";
import useApproveRejectCreditLimitRequest from "../../../hooks/useApproveRejectCreditLimitRequest";
import useGetAllIncreaseRequest from "../../../hooks/useGetAllIncreaseRequest";
import useRefreshCreditLimit from "../../../hooks/useRefreshCreditLimit";
import { filterArrray, format2DecimalPlaces } from "../../../utils/helper";
import styles from "./LimitIncrease.module.scss";

const LimitIncrease = () => {
  const [apiResponseMessage, setApiResponseMessage] = useState("");
  const [showPopup, setShowPopup] = useState(false);
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [showBryzosCreditData] = useState(true);

  const [filteredIncreaseRequests, setFilteredIncreaseRequests] = useImmer([]);

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredIncreaseRequests.length / itemsPerPage);

  const { data: allIncreaseRequests, isLoading: isAllIncreaseRequestsLoading } =
    useGetAllIncreaseRequest();

  const {
    mutate: approveRejectCreditLimitRequest,
    data: approveRejectCreditLimitRequestData,
    isLoading: isApproveRejectCreditLimitRequestLoading,
  } = useApproveRejectCreditLimitRequest();

  const {
    mutate: refreshCreditLimit,
    isLoading: iscreditLimitDataLoading,
    data: creditLimitData,
  } = useRefreshCreditLimit();

  useEffect(() => {
    if (allIncreaseRequests) {
      setFilteredIncreaseRequests(allIncreaseRequests);
    } else {
      setFilteredIncreaseRequests([]);
    }
  }, [allIncreaseRequests]);

  useEffect(() => {
    if (
      approveRejectCreditLimitRequestData &&
      !isApproveRejectCreditLimitRequestLoading
    ) {
      setApiResponseMessage(approveRejectCreditLimitRequestData);
    }
  }, [
    approveRejectCreditLimitRequestData,
    isApproveRejectCreditLimitRequestLoading,
  ]);

  useEffect(() => {
    if (creditLimitData !== undefined && !iscreditLimitDataLoading) {
      setShowPopup(true);
    }
  }, [creditLimitData, iscreditLimitDataLoading]);

  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
  }, [itemsPerPage]);

  const search = (event: any) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(event.target.value);
    if (event.target.value) {
      const _filterArrray = filterArrray(
        allIncreaseRequests,
        event.target.value,
        [
          "first_name",
          "last_name",
          "company_name",
          "client_company",
          "requested_increase_credit",
          "status",
        ]
      );
      if (_filterArrray?.length) {
        setFilteredIncreaseRequests(_filterArrray.slice(0, itemsPerPage));
      } else {
        setFilteredIncreaseRequests([]);
      }
    } else {
      setFilteredIncreaseRequests(
        allIncreaseRequests ? allIncreaseRequests : []
      );
    }
  };

  const onClickApproveHandler = (data: any) => {
    approveRejectCreditLimitRequest({
      data: {
        user_id: data.user_id,
        is_approved: true,
        user_request_increase_credit_id: data.user_request_increase_credit_id,
      },
    });
  };

  const onClickRejectHandler = (data: any) => {
    approveRejectCreditLimitRequest({
      data: {
        user_id: data.user_id,
        is_approved: false,
        user_request_increase_credit_id: data.user_request_increase_credit_id,
      },
    });
  };

  const refreshCreditLimitHandler = (bnpl: any) => {
    refreshCreditLimit(bnpl.user_id);
  };

  const popupCloseHandler = () => {
    setShowPopup(false);
  };

  const handlePageClick = (event: any) => {
    const newOffset =
      (event.selected * itemsPerPage) % filteredIncreaseRequests.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  return (
    <div>
      {isAllIncreaseRequestsLoading ||
      isApproveRejectCreditLimitRequestLoading ||
      iscreditLimitDataLoading ? (
        <div className={styles.loaderImg}>
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <input
              className={styles.searchInput}
              type="text"
              onChange={search}
              value={inputSearchValue}
              placeholder="Search"
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>First Name</th>
                  <th>Last Name</th>
                  <th>Company Name</th>
                  <th>Company Entity/Location</th>
                  <th>Requested Credit Limit</th>
                  {showBryzosCreditData && (
                    <>
                      <th>Approved Limit</th>
                      <th>Available Credit</th>
                    </>
                  )}
                  <th colSpan={4}>Status</th>
                </tr>
              </thead>
              <tbody>
                {filteredIncreaseRequests?.length ? (
                  filteredIncreaseRequests
                    .slice(itemOffset, endOffset)
                    .map((data: any) => (
                      <tr key={data.user_id}>
                        <td>{data.first_name}</td>
                        <td>{data.last_name}</td>
                        <td>{data.company_name}</td>
                        <td>{data.client_company}</td>
                        <td>
                          {format2DecimalPlaces(data.requested_increase_credit)}
                        </td>
                        {showBryzosCreditData && (
                          <>
                            <td>{format2DecimalPlaces(data.bryzos_credit_limit)}</td>
                            <td>{format2DecimalPlaces(data.bryzos_available_credit_limit)}</td>
                          </>
                        )}
                        <td>{data.status}</td>
                        <td>
                          {data.status === "Pending" && (
                            <button
                              className={styles.approvalBtn}
                              onClick={() => onClickApproveHandler(data)}
                            >
                              Approve
                            </button>
                          )}
                        </td>
                        <td>
                          {data.status === "Pending" && (
                            <button
                              className={styles.rejectBtn}
                              onClick={() => onClickRejectHandler(data)}
                            >
                              Reject
                            </button>
                          )}
                        </td>
                        <td>
                          <button
                            className={styles.refreshCreditLimitHandler}
                            onClick={() => refreshCreditLimitHandler(data)}
                          >
                            Get Credit Limit
                          </button>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={10} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
        </div>
      )}
      <MatPopup
        className={styles.successfullyUpdatedpopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div
            className={styles.successfullytext}
            dangerouslySetInnerHTML={{ __html: apiResponseMessage }}
          ></div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup open={showPopup} disablePortal>
        <div className={styles.refreshPop}>
          <button className={styles.crossBtn} onClick={popupCloseHandler}>
            x
          </button>
          {creditLimitData && (
            <div>
              <table>
                <tbody>
                  <tr>
                    <td>Bryzos Available Credit Limit</td>
                    <td>
                      {format2DecimalPlaces(
                        creditLimitData?.balance_available_credit_limit
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Bryzos Credit Limit</td>
                    <td>
                      {format2DecimalPlaces(
                        creditLimitData?.balance_credit_limit
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Outstanding Amount</td>
                    <td>
                      {format2DecimalPlaces(
                        creditLimitData?.outstanding_amount
                      )}
                    </td>
                  </tr>
                  <tr>
                    <td>Status</td>
                    <td>{creditLimitData?.status}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          )}
          {creditLimitData === null && (
            <div>
              <p className={"noDataFoundTd"}>No data found</p>
            </div>
          )}
          <button className={styles.okBtn} onClick={popupCloseHandler}>
            Ok
          </button>
        </div>
      </MatPopup>
    </div>
  );
};

export default LimitIncrease;
