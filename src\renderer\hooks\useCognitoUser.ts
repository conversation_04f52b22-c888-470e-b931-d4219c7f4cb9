import { useQuery } from "@tanstack/react-query";
import { Auth } from "aws-amplify";
import { reactQueryKeys } from "../utils/constant";
import { CognitoUser } from "@aws-amplify/auth";

const getUser = () => {
  return Auth.currentAuthenticatedUser().then((res: CognitoUser) => {
    return res;
  });
};

const useCognitoUser = () => {
  return useQuery([reactQueryKeys.cognitoUser], getUser, {
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    onError(err) {
      return err;
    },
  });
};

export default useCognitoUser;
