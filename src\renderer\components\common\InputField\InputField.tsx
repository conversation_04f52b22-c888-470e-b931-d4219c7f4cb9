import Tooltip from '@mui/material/Tooltip';
import clsx from 'clsx';
import React from 'react';
import { Controller, Control } from 'react-hook-form';
import styles from './InputField.module.scss';

export type InputFieldProps = {
  fieldName: string;
  control: Control<any>;
  label?: string;
  hideLable?: boolean;
  hideError?: boolean;
  containerClass?: string;
  readOnly?: boolean;
} & React.DetailedHTMLProps<
  React.InputHTMLAttributes<HTMLInputElement>,
  HTMLInputElement
>;
const InputField: React.FC<InputFieldProps> = ({
  label,
  containerClass,
  hideLable,
  hideError,
  fieldName,
  control,
  className,
  readOnly = false,
  ...rest
}) => {
  return (
    <Controller
      control={control}
      name={fieldName}
      rules={{ required: true }}
      render={({ field: { value, ...field }, fieldState: { error } }) => (
        <Tooltip
          title={hideError ? '' : error?.message}
          placement='top-end'
          classes={{
            popper: styles.errorStyle,
            tooltip: styles.tooltip,
          }}
        >
          <div className={clsx('formInput', containerClass)}>
            {hideLable
              ? null
              : label && <label htmlFor='input-field'>{label}</label>}
            <input
              value={value ?? ''}
              className={clsx(
                { [styles.errorInput]: error?.message },
                className
              )}
              readOnly={readOnly}
              {...field}
              {...rest}
            />
          </div>
        </Tooltip>
      )}
    />
  );
};

export default InputField;
