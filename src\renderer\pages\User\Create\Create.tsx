import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { BUYER, SELLER } from "../../../utils/constant";
import InputField from "../../../components/common/InputField";
import MatSelect from "../../../components/common/MatSelect";
import { MatSelectOptionsType } from "../../../components/common/MatSelect/MatSelect";
import {
  CreateUserFromSchemaType,
  CreateUserFromSchema,
  EditUserFromSchema,
  EditUserFromSchemaType,
} from "../../../models/createUser.model";
import styles from "./Create.module.scss";
import MatPopup from "../../../components/common/MatPopup";
import Loader from "../../../components/common/Loader/Loader";
import OTPInput from "react-otp-input";
import { Autocomplete, TextField, Tooltip } from "@mui/material";
import clsx from "clsx";
import { useImmer } from "use-immer";
import useCheckValidEmail from "../../../hooks/useCheckValidEmail";
import { cloneDeep } from "lodash-es";
import useGetAllCompany from "../../../hooks/useGetAllCompany";
import SucessErrorPopup from "../../../components/common/SucessErrorPopup";
import useGetAdminReferenceData from "../../../hooks/useGetAdminReferenceData";
import useCreateUser from "../../../hooks/useCreateUser";
import { ReactComponent as ShowPass } from '../../../../assests/images/show-pass.svg';
import { ReactComponent as HidePass } from '../../../../assests/images/hide-pass.svg';

const Create = (props:any) => {
  const [callInProgress, setCallInProgress] = useState(false);
  const [userTypes, setUserTypes] = useState<MatSelectOptionsType>([]);
  const [apiResponseMessage, setApiResponseMessage] = useState("");
  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);
  const [disableCompanyEntity, setDisableCompanyEntity] = useState(true);
  const [companyList, setCompanyList] = useImmer<any[]>([]);
  const [onBoardedSourceList, setOnBoardedSourceList] = useImmer<any[]>([]);
  const [companyEntityList, setCompanyEntityList] = useImmer<any[]>([]);
  const [sucessErrorPopupMessage, setSucessErrorPopupMessage] = useImmer<any>("");
  const [formData, setFormdata] = useImmer<CreateUserFromSchemaType | null>(
    null
  );
  const [companyNameInput, setCompanyNameInput] = useImmer("");
  const [companyNameValue, setCompanyNameValue] = useImmer("");
  const [companyEntityInput, setCompanyEntityInput] = useImmer("");
  const [showPassword, setShowPassword] = useState(false)

  const { data: allCompany, isLoading: allCompanyLoading } = useGetAllCompany();
  const { data: adminReferenceData, isLoading: adminReferenceDataLoading } = useGetAdminReferenceData();

  const {
    mutate: createUser,
    isLoading: isCreateUserLoading,
    data: createUserData,
    error: createUserError,
  } = useCreateUser();

  const {
    mutate: checkValidEmail,
    isLoading: isCheckValidEmailLoading,
    data: checkValidEmailData,
    error: checkValidEmailError,
  } = useCheckValidEmail();

  const userSchema = (props?.isUserEdit || props?.hidePasscode) ? EditUserFromSchema : CreateUserFromSchema; 

  const {
    register,
    control,
    handleSubmit,
    setError,
    reset,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = useForm<CreateUserFromSchemaType | EditUserFromSchemaType>({
    defaultValues: {
      firstName: "",
      lastName: "",
      emailAddress: "",
      type: "",
      onboardSource: "",
    },
    resolver: yupResolver(userSchema),
  });

  const password = watch("password");

  useEffect(() => {
    setUserTypes([
      { title: BUYER, value: BUYER },
      { title: SELLER, value: SELLER },
    ]);
  }, []);

  useEffect(() => {
    if(allCompany){
      setCompanyList(allCompany)
    }
  }, [allCompany])

  useEffect(() => {
    if(adminReferenceData?.onboard_source){
      setOnBoardedSourceList(adminReferenceData.onboard_source)
    }
  }, [adminReferenceData])

  useEffect(() => {
    if(props?.isNewUserCreate){
      const user = props.isNewUserCreate;
      setValue("firstName", user.first_name)
      setValue("lastName", user.last_name)
      setValue("emailAddress", user.email_id)
      setValue("companyName", user.company_name)
      setValue("companyEntity", user?.client_company ?? null)
      setCompanyNameInput(user.company_name ?? "")
      setValue("zipCode", user.zip_code)
      setValue("type", user.user_type)
      setValue("onboardSource", user.onboard_source)
    }
  }, [props?.isNewUserCreate]);

  useEffect(() => {
    if(props?.isUserEdit){
      const user = props.isUserEdit;
      setValue("userId", user.id);
      setValue("firstName", user.first_name)
      setValue("lastName", user.last_name)
      setValue("emailAddress", user.email_id)
      setValue("companyName", user.company_name)
      setValue("companyEntity", user?.client_company ?? null)
      setValue("type", user.type)
      setValue("onboardSource", user.onboarded_app)
      setValue("isExternalApiAdmin",user?.is_external_api_company_admin)
    }
  }, [props?.isUserEdit]);

  useEffect(() => {
    if (createUserData && !isCreateUserLoading) {
      setCompanyNameInput("");
      setCompanyEntityInput("");
      reset();
      setApiResponseMessage(createUserData);
    }
  }, [createUserData, isCreateUserLoading]);

  useEffect(() => {
    if (checkValidEmailError) {
      setShowConfirmationPopup(true);
    } else if (checkValidEmailData) {
      confirmationPopupYes();
    }
  }, [checkValidEmailData, checkValidEmailError]);

  useEffect(() => {
    if(createUserError){
      const errorObj = JSON.parse((createUserError as Error).message)
      const pendingCompanyLink = `https://${import.meta.env.VITE_AWS_COGNITO_DOMAIN}/user/pending-company-list`;
      if(errorObj.error_redirect === pendingCompanyLink){
        const companyName = getValues('companyName')
        const errorMessage = (
          <>
            <div>
              The company, &quot;{companyName}&quot; does not exist. <a className="companyClickBtn" href={pendingCompanyLink} target="_blank" rel="noopener noreferrer">Click here </a> to approve.
            </div>
          </>
        )
        setSucessErrorPopupMessage(errorMessage)
      }
    }
  }, [createUserError])

  useEffect(() => {
    if(watch("companyName")){
      setDisableCompanyEntity(false)
      const companyEntityData = companyList?.find((companyData: any)=> companyData.company_name === watch("companyName"))
      if(companyEntityData?.client_company){
        setCompanyEntityList(companyEntityData.client_company)
      }
    }else{
      setDisableCompanyEntity(true)
      setCompanyEntityInput("")
      setValue("companyEntity","")
    }
  },[watch("companyName")])



  const loginFormSubmitHandler = async (data: CreateUserFromSchemaType | EditUserFromSchemaType) => {
    if(props?.handleNewUserData){
      props.handleNewUserData(data)
      props?.setShowNewUserPopup(false)
    }else{

      setFormdata(cloneDeep(data));
  
      checkValidEmail({
        data: { email: data.emailAddress },
      });
    }
  };

  const confirmationPopupYes = () => {
    if (formData) {
      setCallInProgress(true);
      try {
        createUser({
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            company_name: formData.companyName,
            client_company: formData.companyEntity,
            email_id: formData.emailAddress,
            password: formData.password.toString(),
            user_type: formData.type,
            onboard_source: formData.onboardSource,
          },
        });

        setCallInProgress(false);
      } catch (error: any) {
        setError("root", {
          type: "create-user-error",
          message: JSON.stringify(error),
        });
        setCallInProgress(false);
      }
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setFormdata(null);
  };

  const handleCompanyChange = (data: string) => {
    setValue('companyName', data)
  }

  const handleCompanyEntityChange = (data: string) => {
    setValue('companyEntity', data)
  }

  return (
    <div className={styles.loginPage}>
      {callInProgress || isCreateUserLoading || isCheckValidEmailLoading || allCompanyLoading || adminReferenceDataLoading ? (
        <div className={styles.loaderImg}>
         <Loader />
        </div>
      ) : (
        <form onSubmit={handleSubmit(loginFormSubmitHandler)}>
          <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain')}>
            <div className={clsx(styles.emailText,'lblCreateUser')}>First Name:</div>
            {props?.isUserEdit ?
              <span className={styles.editUserText}>{getValues("firstName")}</span>
            :
              <InputField
                className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
                control={control}
                fieldName={register("firstName").name}
                label=""
                placeholder="First Name"
              />
            }
          </div>
          <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain')}>
            <div className={clsx(styles.emailText,'lblCreateUser')}>Last Name:</div>
            {props?.isUserEdit ?
              <span className={styles.editUserText}>{getValues("lastName")}</span>
            :
              <InputField
              className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
              control={control}
              fieldName={register("lastName").name}
              label=""
              placeholder="Last Name"
            />}
          </div>
          <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain')}>
            <div className={clsx(styles.emailText,'lblCreateUser')}>Email:</div>
            {props?.isUserEdit ?
              <span className={styles.editUserText}>{getValues("emailAddress")}</span>
            :
              <InputField
              className={clsx(styles.InputFieldcss, styles.inputEmail,'inputPendingUsers')}
              control={control}
              fieldName={register("emailAddress").name}
              label=""
              placeholder="Email"
              readOnly={!!props?.hidePasscode}
            />}
          </div>
          <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain creartUserCompanyInput')}>
              <div className={clsx(styles.emailText,'lblCreateUser')}>Main Company:</div>
              {props?.isUserEdit ?
                <div>
                    <MatSelect
                    className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
                    fieldName={register("companyName").name}
                    control={control}
                    placeHolderText="Choose One"
                    
                    MenuProps={{
                      classes: {
                          paper: 'inputPendingUsersDropdown',
                          
                      },
                  }}
                    options={companyList.map(company => ({ title: company.company_name, value: company.company_name }))}
                    onChange={(e)=>{
                      register("companyName").onChange(e);
                      setValue("companyEntity", "")
                    }}
                  />
                </div>
                :
                <Controller
                  name="companyName"
                  control={control}
                  render={({
                    field: { onChange, onBlur, value, name, ref },
                    fieldState: { error },
                  }) => (
                    <Autocomplete
                      freeSolo
                      className={'companyDropdown'}
                      options={companyList}
                      value={companyNameValue ?? watch("companyName")}
                      inputValue={companyNameInput}
                      onInputChange={(_, value, reason) => {
                        if (reason !== "reset") {
                          setCompanyNameInput(value)
                          setValue('companyName', value, { shouldDirty: true })
                        }
                      }}
                      getOptionLabel={(companyData: any) => companyData.company_name ?? ""}
                      renderInput={(params) => (
                        <Tooltip
                          title={errors.companyName?.message}
                          placement="top-end"
                          classes={{
                            popper: styles.errorStyle,
                            tooltip: styles.tooltip,
                          }}
                        >

                          <TextField inputRef={ref} {...params} placeholder="Main Company Name" />
                        </Tooltip>
                      )}
                      onChange={(event, data: any, reason) => {
                        if (reason === "selectOption") {
                          setCompanyNameInput(data?.company_name)
                          setValue('companyName', data?.company_name, { shouldDirty: true })
                        }
                        setCompanyNameValue(data)
                        onChange(data && data?.company_name);
                      }}
                      classes={{
                        root: styles.autoCompleteDesc,
                        popper: styles.autocompleteDescPanel,
                        paper: styles.autocompleteDescInnerPanel,
                        listbox: styles.listAutoComletePanel,
                      }}
                    />
                  )}
                />
              }
          </div>
          <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain creartUserCompanyInput')}>
              <div className={clsx(styles.emailText,'lblCreateUser')}>Company Entity/Location:</div>
              <Controller
                name="companyEntity"
                control={control}
                render={({
                  field: { onChange, onBlur, value, name, ref },
                  fieldState: { error },
                }) => (
                  <Autocomplete
                    freeSolo
                    disabled={disableCompanyEntity}
                    className={'companyDropdown'}
                    options={companyEntityList?.length ? companyEntityList : []}
                    inputValue={companyEntityInput}
                    onInputChange={(_, value) => setCompanyEntityInput(value)}
                    value={companyEntityList?.find((client_company: string) => client_company?.toLowerCase().replaceAll(' ', '') === value?.toLowerCase().replaceAll(' ', '')) ?? getValues('companyEntity') ?? ''}
                    getOptionLabel={(client_company: string) => client_company ?? ""}
                    renderInput={(params) => (
                      <Tooltip
                        title={errors.companyEntity?.message}
                        placement="top-end"
                        classes={{
                          popper: styles.errorStyle,
                          tooltip: styles.tooltip,
                        }}
                      >

                        <TextField inputRef={ref} {...params} placeholder="Company Entity/Location" />
                      </Tooltip>
                    )}
                    onChange={(event, data: any) => {
                      handleCompanyEntityChange(data ? data : null);
                      onChange(data ? data : null);
                    }}
                    onBlur={(event: any) => {
                      handleCompanyEntityChange(event.target?.value);
                    }}
                    classes={{
                      root: styles.autoCompleteDesc,
                      popper: styles.autocompleteDescPanel,
                      paper: styles.autocompleteDescInnerPanel,
                      listbox: styles.listAutoComletePanel,
                    }}
                  />
                )}
                />
          </div>
          {props?.isNewUserCreate && (
            <>
            <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain')}>
                <div className={clsx(styles.emailText,'lblCreateUser')}>Zip Code:</div>
                <Tooltip
                  title={errors.zipCode?.message}
                  placement="top-end"
                  classes={{
                    popper: styles.errorStyle,
                    tooltip: styles.tooltip,
                  }}
                >
                  <div>
                    <input
                      {...register("zipCode")}
                      onChange={(e) => {
                        register("zipCode").onChange(e);
                        const zipCode = e.target.value.replace(/\D/g, '');
                        setValue('zipCode', zipCode);
                      }}
                      maxLength={5}
                      placeholder="Zip Code"
                      type="tel"
                      className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
                    />
                  </div>
                </Tooltip>
                {/* <InputField
                  className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
                  control={control}
                  fieldName={register("zipCode").name}
                  label=""
                  placeholder="Zip Code"
                /> */}
              </div>
            </>
          )}
         {(!props?.isUserEdit && !props?.hidePasscode) &&
          <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain')}>
            <div className={clsx(styles.emailText,'lblCreateUser')}>Passcode:</div>
              <div className={clsx(styles.inputFiledPassword)}>
                <InputField
                  className={`${styles.InputFieldcss} ${styles.pass}`}
                  control={control}
                  containerClass={clsx(styles.inputFiledPasswordContainer)}
                  fieldName={register("password").name}
                  type={showPassword ? "text" : "password"}
                  autoComplete="new-password"
                  placeholder="Passcode"
                />
                <button type="button" className={styles.showHidePass} onClick={() => setShowPassword(x => !x)}>
                  {showPassword ? <HidePass /> : <ShowPass />}
                </button>
              </div>
          </div>
          }
          <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain')}>
            <div className={clsx(styles.emailText,'lblCreateUser')}>Type:</div>
            {props?.isUserEdit ?
              <span className={styles.editUserText}>{getValues("type")}</span>
            :
            <MatSelect
              className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
              fieldName={register("type").name}
              control={control}
              placeHolderText="Choose One"
              options={userTypes}
            />}
          </div>
          <div className={clsx(styles.inputFiledLoginPass,'pendingUsersInputMain')}>
            <div className={clsx(styles.emailText,'lblCreateUser')}>Onboard Source:</div>

            <MatSelect
              className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
              fieldName={register("onboardSource").name}
              control={control}
              placeHolderText="Choose One"
              MenuProps={{
                classes: {
                    paper: 'inputPendingUsersDropdown',
                },
            }}
              options={onBoardedSourceList.map(x => ({ title: x.onboarded_app, value: x.onboarded_app }))}
            />
          </div>
          {
            props?.isUserEdit && 
            <div className={clsx(styles.inputFiledLoginPass,styles.externalApiAdminDiv, 'pendingUsersInputMain')}>
              <div className={styles.emailText}>External API Company Admin:</div>
              <input
                type="checkbox"

                {...register("isExternalApiAdmin")}
                checked={watch("isExternalApiAdmin")}
                onChange={(e) => {
                  setValue("isExternalApiAdmin",  e.target.checked);
                }}
                className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
              />
            </div>
          }
          {props?.isNewUserCreate || props?.isUserEdit ? (
            <div className={clsx(styles.yesAndnoBtn, 'submitBtnCreateUser')}>
              <button className={styles.okBtn} type="submit">
                Submit
              </button>
              <div className={styles.okBtn} onClick={() => props?.popupClose()}>
                Cancel
              </div>
            </div>
          ) : (
            <button className={styles.loginBtn} type="submit">
              Create
            </button>
          )}
        </form>
      )}
      {errors?.root?.message}
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyPop}>
          <div
            className={styles.successfullytext}
            dangerouslySetInnerHTML={{ __html: apiResponseMessage }}
          ></div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.emailNotvalidPopup}
        open={showConfirmationPopup}
      >
        <div className={styles.emailNotvalidbox}>
          <p className={styles.emailText}>
            Email is not valid <br />
            Do you want to continue ?
          </p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
      <SucessErrorPopup
        open={!!sucessErrorPopupMessage}
        messageText={sucessErrorPopupMessage}
        onPopupClose={() => {
          setSucessErrorPopupMessage("");
        }}
      />
    </div>
  );
};

export default Create;
