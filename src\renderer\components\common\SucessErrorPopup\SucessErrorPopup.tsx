import React from "react";
import MatPopup from "../MatPopup";
import styles from "./SucessErrorPopup.module.scss";

type Props = {
  open: boolean;
  messageText: string;
  buttonText?: string;
  onPopupClose: () => void;
};

const SucessErrorPopup: React.FC<Props> = ({
  open,
  messageText,
  onPopupClose,
  buttonText,
}) => {
  return (
    <div>
      <MatPopup className={styles.approveRejectPopup} open={open}>
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{messageText}</div>
          <button className={styles.okBtn} onClick={onPopupClose}>
            {buttonText ?? "Ok"}
          </button>
        </div>
      </MatPopup>
    </div>
  );
};

export default SucessErrorPopup;
