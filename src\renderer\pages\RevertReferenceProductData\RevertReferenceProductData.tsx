import React, { useState, useMemo, useContext, useEffect } from 'react'
import { useForm } from 'react-hook-form';
import styles from "./RevertReferenceProductData.module.scss";
import useGetReferenceDataProductVersionsV2 from '../../hooks/useGetReferenceDataProductVersionsV2';
import MatSelect from '../../components/common/MatSelect/MatSelect';
import usePostRevertReferenceDataProduct from '../../hooks/usePostRevertReferenceDataProduct';
import { CommonCtx } from '../AppContainer';

interface FormData {
    searchDataVersion: string;
    pricingDataVersion: string;
}

const RevertReferenceProductData = () => {
    const {data: getReferenceDataProduct, isLoading: getReferenceDataProductLoading} = useGetReferenceDataProductVersionsV2();
    const {
        mutate: revertReferenceData,
        data: revertReferenceDataResponse,
        isLoading: isRevertingReferenceData,
    } = usePostRevertReferenceDataProduct();
    const showPopupFormAnyComponent = useContext(CommonCtx);
    
    const { control, watch, handleSubmit, formState: { errors } } = useForm<FormData>({
        defaultValues: {
            searchDataVersion: '',
            pricingDataVersion: ''
        }
    });

    // Watch form values to enable/disable buttons
    const searchDataVersion = watch('searchDataVersion');
    const pricingDataVersion = watch('pricingDataVersion');

    // Extract search and pricing data options from API response
    const { searchDataOptions, pricingDataOptions } = useMemo(() => {
        if (!getReferenceDataProduct) {
            return { searchDataOptions: [], pricingDataOptions: [] };
        }
        const searchOptions = getReferenceDataProduct?.search?.map((version: string) => ({
            title: `Version ${version}`,
            value: version
        })) || [];

        const pricingOptions = getReferenceDataProduct?.pricing?.map((version: string) => ({
            title: `Version ${version}`,
            value: version
        })) || [];

        return { 
            searchDataOptions: searchOptions, 
            pricingDataOptions: pricingOptions 
        };
    }, [getReferenceDataProduct]);

    useEffect(() => {
        if(revertReferenceDataResponse){
            console.log('revertReferenceDataResponse', revertReferenceDataResponse);
            showPopupFormAnyComponent(revertReferenceDataResponse);
        }
    }, [revertReferenceDataResponse]);

    const handleRevertSearchData = () => {
        if (!searchDataVersion) return;
        
        try {
            const payload = {
                data: {
                    file_type: "search",
                    version: searchDataVersion
                }
            };
            console.log('search  payload', payload);
            revertReferenceData(payload);
        } catch (error) {
            console.error('Error reverting search data:', error);
        }
    };

    const handleRevertPricingData = () => {
        if (!pricingDataVersion) return;
        
        try {
            const payload = {
                data: {
                    file_type: "pricing",
                    version: pricingDataVersion
                }
            };
            console.log('pricing  payload', payload);
            revertReferenceData(payload);
        } catch (error) {
            console.error('Error reverting pricing data:', error);
        }
    };

    if (getReferenceDataProductLoading) {
        return (
            <div className={styles.container}>
                <div className={styles.loadingText}>Loading versions...</div>
            </div>
        );
    }

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <p>Select versions to revert search and pricing data</p>
            </div>

            <div className={styles.formContainer}>
                <div className={styles.dropdownSection}>
                    {/* Search Component - Left Side */}
                    <div className={styles.componentSection}>
                        <label className={styles.label}>Search Data Version</label>
                        <div className={styles.dropdownButtonRow}>
                            <div className={styles.dropdownGroup}>
                                <MatSelect
                                    fieldName="searchDataVersion"
                                    control={control}
                                    placeHolderText="Select Search Data Version"
                                    options={searchDataOptions}
                                    className={styles.dropdown}
                                />
                            </div>
                            <button
                                type="button"
                                className={styles.revertBtn}
                                onClick={handleRevertSearchData}
                                disabled={!searchDataVersion || isRevertingReferenceData}
                            >
                                {isRevertingReferenceData ? 'Reverting...' : 'Revert Search Data'}
                            </button>
                        </div>
                    </div>

                    {/* Pricing Component - Right Side */}
                    <div className={styles.componentSection}>
                        <label className={styles.label}>Pricing Data Version</label>
                        <div className={styles.dropdownButtonRow}>
                            <div className={styles.dropdownGroup}>
                                <MatSelect
                                    fieldName="pricingDataVersion"
                                    control={control}
                                    placeHolderText="Select Pricing Data Version"
                                    options={pricingDataOptions}
                                    className={styles.dropdown}
                                />
                            </div>
                            <button
                                type="button"
                                className={styles.revertBtn}
                                onClick={handleRevertPricingData}
                                disabled={!pricingDataVersion || isRevertingReferenceData}
                            >
                                {isRevertingReferenceData ? 'Reverting...' : 'Revert Pricing Data'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default RevertReferenceProductData