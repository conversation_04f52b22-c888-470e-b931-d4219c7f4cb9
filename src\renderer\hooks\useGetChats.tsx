import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { commomKeys } from '@bryzos/giss-ui-library';

const useGetChats = () => {
  return useMutation( async () => {
    try {
      let url = `${import.meta.env.VITE_API_CHAT_SERVICE}/get-chats`;

      const response = axios.get(url);
      const responseData = await response;

      if (responseData.data && responseData.data?.data) {
        return responseData.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
        throw new Error(error?.message ?? '');
    }
  });
};

export default useGetChats;
