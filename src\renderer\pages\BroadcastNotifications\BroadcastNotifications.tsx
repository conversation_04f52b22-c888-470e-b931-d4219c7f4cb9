import { useContext, useEffect, useState } from "react";
import { useImmer } from "use-immer";
import Loader from "../../components/common/Loader";
import MatPopup from "../../components/common/MatPopup";
import styles from "./BroadcastNotifications.module.scss";
import InputField from "../../components/common/InputField";
import MatSelect from "../../components/common/MatSelect";
import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import * as yup from "yup";
import usePostBroadcastNotifications from "../../hooks/usePostBroadcastNotifications";
import useGetNotificationEvents from "../../hooks/useGetNotificationEvents";
import { cloneDeep } from "lodash-es";
import { CommonCtx } from "../AppContainer";
import { Autocomplete, TextField } from "@mui/material";
import useGetPoNumberData from "../../hooks/useGetPoNumberData";
import { ReactComponent as CloseIcon } from '../../../assests/images/Icon_Close.svg';
import clsx from "clsx";
import { CUSTOM_NOTIFICATION, sendToOptions } from "../../utils/constant";
import SendTo from "./send-to/SendTo";
import CustomDatePicker from "../../components/common/CustomDatePicker";
import dayjs from "dayjs";
import { dateTimeFormat } from "@bryzos/giss-ui-library";

const isEmail = (email: string) => {
  const emailPattern = new RegExp("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$");
  return emailPattern.test(email);
};
const GenerateNotificationSchema = yup.object().shape({
  notificationType: yup.string().default("").required(),
  po_number: yup.string().default(""),
  user_email: yup.string().email("Email is not valid").default(""),

  custom_notification_message: yup
    .string()
    .trim()
    .default("")
    .when("notificationType", {
      is: (notificationType: string) => notificationType === CUSTOM_NOTIFICATION,
      then: (s) => s.required("Required"),
    }),
  custom_notification_expiration_date: yup
    .string().nullable(),
  custom_notification_priorty: yup.string().when("notificationType", {
    is: (notificationType: string) => notificationType === CUSTOM_NOTIFICATION,
    then: (s) => s.required("Required").oneOf(["LOW", "MEDIUM", "HIGH"]),
  }),
  custom_notification_action: yup.string().when("notificationType", {
      is: (notificationType: string) => notificationType === CUSTOM_NOTIFICATION,
      then: (s) => s.required("Required").oneOf(["REFRESH", "CLOSE"]),
    }),
  sendTo: yup.string().when("notificationType", {
    is: (notificationType: string) => notificationType === CUSTOM_NOTIFICATION,
    then: (s) => s.required("Required").oneOf([sendToOptions.all, sendToOptions.buyers, sendToOptions.sellers, sendToOptions.users]),
  }),
  sendToUsers: yup.array().of(yup.object().shape({
    userId: yup.number(),
    firstName: yup.string(),
    lastName: yup.string(),
    email: yup.string(),
  })).when("sendTo", {
    is: (sendTo: string) => sendTo === sendToOptions.users,
    then: (s) => s.min(1).required(),
  }),
  custom_notification_preview: yup.string(),
});
export type SchemaType = yup.InferType<
  typeof GenerateNotificationSchema
>;

const GenerateEmail = () => {
  const [modifiedNotifications, setModifiedNotification] = useImmer<any[]>([]);
  const [notificationEventsTypes, setNotificationEventsTypes] = useImmer<any[]>(
    []
  );
  const [selectedNotificationData, setSelectedNotificationData] = useImmer<any>(
    []
  );
  const [sendNotificationData, setSendNotificationData] = useImmer<any>(null);
  const [poLists, setPoLists] = useImmer([]);
  const [showPreview, setShowPreview] = useState(false);


  const { 
    data: poNumberData, 
    isLoading: isPoNumberDataLoading 
  } = useGetPoNumberData();
  
  const {
    data: notificationEventsData,
    isLoading: isNotificationEventsLoading,
  } = useGetNotificationEvents();

  const {
    mutate: sendGenerateNotification,
    data: sendGenerateNotificationData,
    isLoading: isSendGenerateNotificationLoading,
  } = usePostBroadcastNotifications();

  const showPopupFormAnyComponent = useContext(CommonCtx);

  const defaultValues = {
    custom_notification_priorty: "MEDIUM",
    custom_notification_action: "CLOSE",
    custom_notification_preview: "DESKTOP",
    custom_notification_expiration_date: null
  };

  const {
    register,
    control,
    handleSubmit,
    setError,
    clearErrors,
    reset,
    getValues,
    setValue,
    watch,
    formState: { errors, isValid},
  } = useForm<SchemaType>({
    resolver: yupResolver(GenerateNotificationSchema),
    defaultValues,
  });

  const { fields, prepend, remove } = useFieldArray({ control, name: 'sendToUsers' });
  
  useEffect(() => {
    if (!isPoNumberDataLoading && poNumberData) {
      const _setPoLists = poNumberData.map(
        (data: any, index: number) => ({
          id: index,
          title: data.buyer_po_number,
          value: data.buyer_po_number,
        })
      );

      setPoLists(_setPoLists);
    }
  }, [isPoNumberDataLoading, poNumberData]);
  useEffect(() => {
    if (notificationEventsData?.length) {
      const _notificationEventsData: any[] = cloneDeep(notificationEventsData);
      const _notificationEventsTypes: any[] = [];

      _notificationEventsData.forEach((data: any) => {
        if (data.notification_params) {
          const _notification_params = data.notification_params.split(",");
          data.notification_params = _notification_params;
        } else {
          data.notification_params = [];
        }

        if (data.is_active) {
          _notificationEventsTypes.push({
            title: data.notiifcation_name === "Announcement" ? "Custom Notification" : data.notiifcation_name,
            value: data.notification_event,
          });
        }
      });

      setModifiedNotification(_notificationEventsData);
      setNotificationEventsTypes(_notificationEventsTypes);
    } else {
      setNotificationEventsTypes([]);
    }
  }, [notificationEventsData]);

  useEffect(() => {
    if (sendGenerateNotificationData) {
      showPopupFormAnyComponent(sendGenerateNotificationData);
    }
  }, [sendGenerateNotificationData]);

  const notificationEventsTypesChange = (eventValue: any) => {
    if (eventValue) {
      const obj = modifiedNotifications.find(
        (data) => data.notification_event === eventValue
      );
      if (obj) {
        setSelectedNotificationData(cloneDeep(obj));
      }
    } else {
      reset(defaultValues);
      setSelectedNotificationData([]);
      setShowPreview(false);
      setSendNotificationData(null);
    }
  };

  const confirmationPopupYes = () => {
    if (sendNotificationData) {
      sendGenerateNotification(sendNotificationData);
      setSelectedNotificationData([]);
      reset();
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowPreview(false);
    setSendNotificationData(null);
  };
  const generateNotificationSubmitHandler = async (
    data: SchemaType
  ) => {
    const usersIds = fields.map(filed => filed.userId);

    let payload: any = {
      data: {
        notification_event: selectedNotificationData.notification_event,
        ...(selectedNotificationData.notification_event === CUSTOM_NOTIFICATION && {
          message: getValues("custom_notification_message"),
          expiry_date: getValues("custom_notification_expiration_date"),
          priority: getValues("custom_notification_priorty"),
          action: getValues("custom_notification_action"),
          send_to: getValues("sendTo"), 
          send_to_users: usersIds,
        }),
      },
    };

    if (selectedNotificationData?.notification_params?.length) {
      let isAnyError = false;

      selectedNotificationData.notification_params.forEach(
        (notificationParam: any) => {
          if (notificationParam === "po_number") {
            if (!data.po_number) {
              setError("po_number", { message: "Required" });
              isAnyError = true;
            } else {
              clearErrors("po_number");
            }
          }

          if (notificationParam === "user_email") {
            if (!data.user_email) {
              setError("user_email", { message: "Required" });
              isAnyError = true;
            } else if (!isEmail(data.user_email)) {
              setError("user_email", { message: "Email is not valid" });
              isAnyError = true;
            } else {
              clearErrors("user_email");
            }
          }
        }
      );

      if (isAnyError) {
        return;
      }

      selectedNotificationData.notification_params.forEach(
        (notificationParam: "po_number" | "user_email") => {
          payload.data[notificationParam] = getValues(`${notificationParam}`);
        }
      );
    }

    setShowPreview(false);
    setSendNotificationData(payload);
  };

  return (
    <div className="contentMain">
      {isSendGenerateNotificationLoading || isNotificationEventsLoading ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <>
          <div className={styles.sendNotificationMain}>
            <div className={styles.title}>Notifications</div>
            <Controller
              name="notificationType"
              control={control}
              render={({
                field: { onChange, onBlur, value, name, ref },
                fieldState: { error },
              }) => (
                <Autocomplete
                  className={styles.selectDropdown}
                  options={notificationEventsTypes}
                  value={
                    notificationEventsTypes.find(
                      (obj: any) => obj.value === value
                    ) ?? null
                  }
                  getOptionLabel={(option: any) => option.title ?? ""}
                  renderInput={(params) => (
                    <TextField {...params} label="Choose One" />
                  )}
                  onChange={(event, data: any) => {
                    notificationEventsTypesChange(data ? data.value : null);
                    onChange(data ? data.value : null);
                  }}
                  classes={{
                    root: styles.autoCompleteDesc,
                    popper: styles.autocompleteDescPanel,
                    paper: styles.autocompleteDescInnerPanel,
                    listbox: styles.listAutoComletePanel,
                  }}
                />
              )}
            />
            {selectedNotificationData?.notification_params?.map((data: any) => (
              <>
                {data === "po_number" && (
                  <div className={styles.inputField}>
                    <Controller
                      name={register("po_number").name}
                      control={control}
                      render={({
                        field: { onChange, onBlur, value, name, ref },
                        fieldState: { error },
                      }) => (
                        <Autocomplete
                          className={styles.selectDropdown}
                          options={poLists}
                          value={poLists.find((obj: any) => obj.value === value) ?? null}
                          getOptionLabel={(option: any) => option.title ?? ""}
                          renderInput={(params) => (
                            <TextField {...params} label="Choose PO#" />
                          )}
                          onChange={(event, data: any) => {
                            onChange(data ? data.value : null);
                          }}
                          classes={{
                            root: styles.autoCompleteDesc,
                            popper: styles.autocompleteDescPanel,
                            paper: styles.autocompleteDescInnerPanel,
                            listbox: styles.listAutoComletePanel,
                          }}
                        />
                      )}
                    />
                  </div>
                )}
                {data === "user_email" && (
                  <div className={styles.inputField}>
                    <label>User Email</label>
                    <InputField
                      className={styles.InputFieldcss}
                      control={control}
                      fieldName={register("user_email").name}
                    />
                  </div>
                )}
              </>
            ))}
            {selectedNotificationData?.notification_event ===
              CUSTOM_NOTIFICATION && (
              <div className={styles.customNotificationMain}>
                <div className={styles.messageDiv}>
                  <div className={styles.inputSection}>
                    <label className={styles.messageLbl}>Message</label>
                    <InputField
                      type="text"
                      fieldName="custom_notification_message"
                      control={control}
                      maxLength={75}
                      className={styles.InputBox}
                    />
                  </div>
                  <div className={styles.inputSection}>
                    <label className={styles.messageLbl}>Expiration Date</label>
                      <Controller
                        control={control}
                        rules={{
                          required: true,
                        }}
                        name={register('custom_notification_expiration_date').name}
                        render={({ field: { onChange }, fieldState: { error } }) => {
                          const datePickerValue = watch('custom_notification_expiration_date') ? dayjs(watch('custom_notification_expiration_date')) : null;
                          const customDatePickerOnChange = (newValue: dayjs.Dayjs | null) => {
                            onChange(newValue?.format(dateTimeFormat.isoFormat));
                          }
                          return (
                            <CustomDatePicker value={datePickerValue} onChange={customDatePickerOnChange} format={dateTimeFormat.shortUSFormat} minDate={dayjs()} />
                          )
                        }}
                      />
                  </div>
                  <div className={styles.inputSection}>
                    <div>
                      <p className={styles.lblTitle}>Background Color</p>
                    </div>
                    <div className={styles.rightGrid}>
                    <label className={styles.lblRadio}>Yellow</label>
                      <input className={styles.radioInput}
                        {...register("custom_notification_priorty")}
                      type="radio"
                      value="LOW"
                      checked={watch("custom_notification_priorty") === "LOW"}
                      onChange={(e) => {
                        register("custom_notification_priorty").onChange(e);
                        setValue("custom_notification_priorty", e.target.value);
                      }}
                    />
                    <label className={styles.lblRadio}>Orange</label>
                    <input className={styles.radioInput}
                        {...register("custom_notification_priorty")}
                      type="radio"
                      value="MEDIUM"
                      checked={watch("custom_notification_priorty") === "MEDIUM"}
                      onChange={(e) => {
                        register("custom_notification_priorty").onChange(e);
                        setValue("custom_notification_priorty", e.target.value);
                      }}
                    />
                    <label className={styles.lblRadio}>Red</label>
                    <input className={styles.radioInput}
                        {...register("custom_notification_priorty")}
                      type="radio"
                      value="HIGH"
                      checked={watch("custom_notification_priorty") === "HIGH"}
                      onChange={(e) => {
                        register("custom_notification_priorty").onChange(e);
                        setValue("custom_notification_priorty", e.target.value);
                      }}
                    />
                    </div>
                  </div>
                  <div className={styles.inputSection}>
                    <div>
                      <p className={styles.lblTitle}>Action</p>
                    </div>
                    <div className={styles.rightGrid}>
                    <label className={styles.lblRadio}>Refresh</label>
                    <input className={styles.radioInput}
                        {...register("custom_notification_action")}
                        type="radio"
                        value="REFRESH"
                        checked={watch("custom_notification_action") === "REFRESH"}
                        onChange={(e) => {
                          register("custom_notification_action").onChange(e);
                          setValue("custom_notification_action", e.target.value);
                        }}
                      />
                    <label className={styles.lblRadio}>Close</label>
                    <input className={styles.radioInput}
                        {...register("custom_notification_action")}
                        type="radio"
                        value="CLOSE"
                        checked={watch("custom_notification_action") === "CLOSE"}
                        onChange={(e) => {
                          register("custom_notification_action").onChange(e);
                          setValue("custom_notification_action", e.target.value);
                        }}
                      />
                      </div>
                  </div>
                    <SendTo register={register} watch={watch} control={control} fields={fields} prepend={prepend} remove={remove}/>
                    <div className={styles.btnSendNotif}>
                      {showPreview ?
                        (
                          <>
                            <button onClick={() => setShowPreview(false)}> Close Preview</button>
                            <div className={styles.previewSelectBtn}>
                              <label className={styles.lblRadio}>Desktop</label>
                              <input className={styles.radioInput}
                                {...register("custom_notification_preview")}
                                type="radio"
                                value="DESKTOP"
                                checked={watch("custom_notification_preview") === "DESKTOP"}
                                onChange={(e) => {
                                  register("custom_notification_preview").onChange(e);
                                  setValue("custom_notification_preview", e.target.value);
                                }}
                              />
                              <label className={clsx(styles.lblRadio,styles.lblMobile)}>Mobile</label>
                              <input className={styles.radioInput}
                                {...register("custom_notification_preview")}
                                type="radio"
                                value="MOBILE"
                                checked={watch("custom_notification_preview") === "MOBILE"}
                                onChange={(e) => {
                                  register("custom_notification_preview").onChange(e);
                                  setValue("custom_notification_preview", e.target.value);
                                }}
                              />
                            </div>
                          </>
                        )
                        :
                        <button disabled={!watch("custom_notification_message") || watch("custom_notification_message")?.trim()?.length < 1} onClick={() => setShowPreview(true)}>Preview</button>
                      }
                    </div>
                </div>
                  <div className={styles.previewDiv}>
                    {(showPreview && watch("custom_notification_preview") === "DESKTOP") ? (
                      <div className={styles.previewTostMessage}>
                        <div className={watch("custom_notification_priorty")?.toLocaleLowerCase() + "Class"}>
                          <div className={clsx(styles.snackbarContainer, `snackbar_${watch("custom_notification_priorty")}`)}>
                            <div className={styles.content} dangerouslySetInnerHTML={{ __html: watch("custom_notification_message") }} />
                            {watch("custom_notification_action") === "REFRESH" && (<button className={styles.actionBtn} > REFRESH </button>)}
                            {watch("custom_notification_action") === "CLOSE" && (<button className={styles.closeBtn}> <CloseIcon /> </button>)}
                          </div>
                        </div>

                      </div>

                    )
                      : (showPreview && watch("custom_notification_preview") === "MOBILE")  &&
                      (
                        <div className={styles.previewMobileTostMessage}>
                          <div className={watch("custom_notification_priorty")?.toLocaleLowerCase() + "Class"}>
                            <div className={clsx(styles.snackbarContainer, `snackbar_${watch("custom_notification_priorty")}`)}>
                              <div className={styles.content} dangerouslySetInnerHTML={{ __html: watch("custom_notification_message") }} />
                              {watch("custom_notification_action") === "REFRESH" && (<button className={styles.actionBtn} > REFRESH </button>)}
                              {watch("custom_notification_action") === "CLOSE" && (<button className={styles.closeBtn}> <CloseIcon /> </button>)}
                            </div>
                          </div>

                        </div>
                      )
                    }
                  </div>
              </div>
            )}
            <div className={styles.btnSendNotif}>
              <button
                onClick={handleSubmit(generateNotificationSubmitHandler)}
                type="submit"
                disabled={!isValid}
              >
                Send Notification
              </button>
            </div>
          </div>
        </>
      )}
      <MatPopup
        className={styles.orderContinuePopup}
        open={!!sendNotificationData}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Do you want to continue ?</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
    </div>
  );
};

export default GenerateEmail;
