import { yup<PERSON><PERSON>olver } from "@hookform/resolvers/yup";
import { Controller, useForm } from "react-hook-form";
import * as yup from "yup";
import MatPopup from "../../components/common/MatPopup";
import { Auth } from "aws-amplify";
import {
    ROLE_BRYZ<PERSON>_<PERSON>MIN,
    logoNameList,
    reactQuery<PERSON>eys,
    routePaths,
} from "../../utils/constant";
import { useQueryClient } from "@tanstack/react-query";
import Loader from "../../components/common/Loader";
import { useState } from "react";
import OtpInput from "react-otp-input";
import styles from "./LoginPopup.module.scss";
import { useNavigate } from "react-router-dom";
import { CognitoUserSession } from "amazon-cognito-identity-js";
import { ReactComponent as BryzosIcon } from "../../../assests/images/BryzosOSLogo.svg";
import InputField from "../common/InputField";
import { ReactComponent as ShowPass } from '../../../assests/images/show-pass.svg';
import { ReactComponent as HidePass } from '../../../assests/images/hide-pass.svg';

const schema = yup.object().shape({
    email: yup
        .string()
        .default("")
        .trim()
        .required("Email is required")
        .email("Please enter valid email")
        .matches(/^$|^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "Please enter valid email"),
    password: yup
        .string()
        .default("")
        .trim()
        .required("Password is required")
        .min(6, "Min lenght is 6"),
});

type Props = {
    isUserReAuthenticated: boolean;
    logoName: string;
    setIsUserReAuthenticated: (value: boolean) => void;
};

interface LogoType {
    [x: string]: JSX.Element;
}

const logoObject: LogoType  = {
    [logoNameList.bryzosLogo]: <BryzosIcon className={styles.bryzosIcon} />,
    [logoNameList.bryzosPayLogo]: <img src="https://bryzos-assets.s3.amazonaws.com/img/BryzosPay-Email-icon.png" />
}


const LoginPopup: React.FC<Props> = ({
    isUserReAuthenticated,
    logoName,
    setIsUserReAuthenticated,
}) => {
    const [callInProgress, setCallInProgress] = useState(false);
    const [openPopup, setOpenPopup] = useState(!isUserReAuthenticated);
    const [showPassword, setShowPassword] = useState(false)

    const query = useQueryClient();

    const navigate = useNavigate();

    const defaultValues = {
        email: "",
        password: "",
    };

    const {
        register,
        handleSubmit,
        reset,
        setError,
        watch,
        setValue,
        control,
        clearErrors,
        formState: { errors, isValid },
    } = useForm({
        defaultValues,
        resolver: yupResolver(schema),
    });

    const login = async (data: any) => {
        if (data && isValid) {
            setCallInProgress(true);
            reset(defaultValues);
            try {
                const cognitoUser = await Auth.signIn(data.email, data.password);
                cognitoUser.getSession(
                    (error: Error | null, session: CognitoUserSession | null) => {
                        if (!error && session) {
                            query.invalidateQueries([reactQueryKeys.cognitoUser]);
                            let adminlogin =
                                session.getAccessToken().payload["cognito:groups"];
                            if (adminlogin?.indexOf(ROLE_BRYZOS_ADMIN) > -1) {
                                setIsUserReAuthenticated(true);
                            } else {
                                setIsUserReAuthenticated(false);
                                setOpenPopup(false);
                            }
                        }
                    }
                );

                setCallInProgress(false);
                query.invalidateQueries([reactQueryKeys.cognitoUser]);
                clearErrors();
            } catch (error: any) {
                setIsUserReAuthenticated(false);
                setError("root", {
                    type: error.code,
                    message: "Email or password is not valid",
                });
                setCallInProgress(false);
            }
        } else {
            setCallInProgress(false);
        }
    };

    const closePopup = () => {
        navigate(`/${routePaths.user}/${routePaths.create}`);
    };

    
    const showLogo = (logo: string): JSX.Element => {
        return logoObject[logo]
    }

    return (
        <MatPopup className={styles.loginPopup} open={openPopup}>
            {callInProgress ? (
                <div className={styles.loaderImg}><Loader /></div>
            ) : (
                <div>
                    <button className={styles.closeBtn} onClick={closePopup}>x</button>
                    <form onSubmit={handleSubmit(login)}>
                        {showLogo(logoName)}
                        <div className={styles.inputLoginPass}>
                            <input
                                className={styles.loginFiled}
                                placeholder="Email Address"
                                autoComplete="off"
                                {...register("email")}
                            />
                            <div className={styles.passField}>
                                <InputField
                                    className={`${styles.InputFieldcss} ${styles.pass}`}
                                    control={control}
                                    fieldName={register("password").name}
                                    type={showPassword ? "text" : "password"}
                                    autoComplete="new-password"
                                    placeholder="Enter Password"
                                />
                                <button type="button" className={styles.showHidePass} onClick={() => setShowPassword(x => !x)}>
                                    {showPassword ? <HidePass /> : <ShowPass />}
                                </button>
                            </div>
                            <span className={styles.errorMessage}>{errors.root?.message}</span>
                        </div>
                        <button className={styles.loginBtn} type="submit" disabled={!isValid}>
                            Enter
                        </button>
                        <div className={styles.requestAccess}>Request access</div>
                    </form>
                </div>
            )}
        </MatPopup>
    );
};

export default LoginPopup;
