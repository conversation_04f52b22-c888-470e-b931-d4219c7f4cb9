import { axios, formatToTwoDecimalPlaces, getUnitPostScript, orderIncrementPrefix, priceUnits } from "@bryzos/giss-ui-library";
import { Auth } from "aws-amplify";
import { v4 as uuidv4 } from 'uuid';
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import tz from "dayjs/plugin/timezone";
import { urlMasks } from "./constant";

dayjs.extend(utc);
dayjs.extend(tz);

export const Logout = async () => {
  try {
    await Auth.signOut();
    window.location.reload();
  } catch (error) {
    console.error(error);
  }
};

export const format2DecimalPlaces = (
  value: string | number | null | undefined = undefined
) => {
  if (value !== undefined && value !== null && !isNaN(+value)) {
    value = parseFloat(value + "")
      .toFixed(2)
      .replace(/(\d)(?=(\d{3})+\.)/g, "$1,");

    if (+value === 0 && value.includes("-")) {
      value = value.replace("-0.00", "0.00");
    }
    return value;
  } else {
    return "0.00";
  }
};

export const filterArrray = (
  array: any,
  _searchValue: any,
  searchKeys: Array<string | { column: string; searchType: "startsWith" }> = []
) => {
  if (array?.length && _searchValue) {
    let searchValue = _searchValue.toLocaleLowerCase();

    const searchResult = array.filter((refProduct: any) => {
      let isFound = false;
      if (searchKeys?.length) {
        for (let _key of searchKeys) {
          let value = "";
          let key = typeof _key === "object" ? _key.column : _key;

          if (refProduct[key]) {
            value = refProduct[key].toString().toLocaleLowerCase();
          }

          if (typeof _key === "object") {
            if (refProduct[_key.column]) {
              value = refProduct[_key.column].toString().toLocaleLowerCase();
              if (value.startsWith(searchValue)) {
                isFound = true;
                break;
              }
            }
          } else {
            if (refProduct[key]) {
              value = refProduct[key].toString().toLocaleLowerCase();
              if (value.includes(searchValue)) {
                isFound = true;
                break;
              }
            }
          }
        }
      } else {
        for (let _value of refProduct) {
          let value = "";
          if (typeof _value === "string" || typeof _value === "number") {
            value = _value.toString().toLocaleLowerCase();
            if (value.includes(searchValue)) {
              isFound = true;
              break;
            }
          }
        }
      }
      return isFound;
    });
    return searchResult;
  } else {
    return null;
  }
};

export const maskData = (input: string, symbol: string, limit: number) => {
  if (!input) {
    return null;
  }
  return input.slice(-1 * limit).padStart(input.length, symbol);
}

export const certUrlS3Constant = {
  prefix: 'https://',
  suffix:  ".s3.amazonaws.com/"
}

export const uploadFileAndGetS3Url = async(file: File, bucketName: string, folderPath: string = '/', getSignedUrl: string, prefixUrl: string): Promise<string> => {
    
  let index = file.name.length - 1;
      for (; index >= 0; index--) {
        if (file.name.charAt(index) === '.') {
          break;
        }
      }
      const ext = file.name.substring(index + 1, file.name.length);

      const objectKey = 'staging' + folderPath + prefixUrl + '-' + uuidv4() + '.' + ext;

      const payload = {
        data: {
          "bucket_name": bucketName,
          "object_key": objectKey,
          "expire_time": 3000
        }
      }

      let certUrlS3 = certUrlS3Constant.prefix + payload.data.bucket_name + certUrlS3Constant.suffix + payload.data.object_key;
      try{
        const response = await axios.post(getSignedUrl, payload)
        const signedUrl = response.data.data;
        await axios.put(signedUrl, file)
        return certUrlS3;

      }catch(err) {
        throw err;
      }
}


export const convertUtcToCtTimeUsingDayjs = (date: string| number | Date | dayjs.Dayjs | null | undefined)=>{
  return dayjs.utc(date).tz('America/Chicago').format('MMMM D, YYYY [at] h:mm:ss A')
}

export const splitAndFormatItem = (key: string , splitBy:string): string => {
  const parts = key.split(splitBy);
  return parts.map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()).join(' ');
};

export const convertUtcTotoLocaleTimeUsingDayjs = (date) => {
  return dayjs.utc(date).tz('America/Chicago').format('MM/DD/YYYY hh:mm:ss A');
};

export const parsePipeSeparatedFile = (fileContent: string) => {
  try{
    const data:any[] = fileContent
    .split("\n")
    .filter(line => line.trim() !== "") // Remove empty lines
    .map(line => {
      const components = line.split("|").map(component => component.trim());
      if(components.length <= 1){
        const unMaskUrlsString = maskURLs(line);
        return {
          dateTime: null,
          process: '',
          level: '',
          log: unMaskUrlsString
        }
      }
      const unMaskUrlsString = maskURLs(components[4]);
                  
      return {
        dateTime: `${addYearIfMissing(components[0])} ${components[1]} UTC`,
        process: components[2],
        level: components[3],
        log: unMaskUrlsString
      };
    });
    return data;
  } catch(error) {
    throw new Error('The log file has some errors. Please try checking/unchecking Remove Extra Space and check the log file.');
  }
};

export const removeAlternateCharactersFromLines = (str:string) => {
    return str
      .split('\n') // Split the string into lines
      .map(line => [...line].filter((_, index) => index % 2 === 0).join('')) // Process each line
      .join('\n'); // Join the processed lines back together
  }

  export  const maskURLs = (logString:string)=>{
    try{
      urlMasks.forEach(({key, mask}) => {
        const escapedKey = mask.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
        logString = logString.replace(new RegExp(escapedKey, 'gi'), key);
      });
      return logString;
    } catch(err) {
      throw new Error(err.message)
    }
    }

export function addYearIfMissing(dateStr) {
  return dateStr.replace(/\b([A-Za-z]{3} \d{1,2})(?!, \d{4})\b/, '$1, 2025');
}

export const getProductMapping = (productList: any[], userData: any = null) => {
  const productMap = {};
  productList.forEach((product) => {
    // product.Buyer_Pricing_CWT = product.Actual_Buyer_Pricing_CWT;
    // product.Buyer_Pricing_Ea = product.Actual_Buyer_Pricing_Ea;
    // product.Buyer_Pricing_Ft = product.Actual_Buyer_Pricing_Ft;
    // product.Buyer_Pricing_LB = product.Actual_Buyer_Pricing_LB;
    // product.Buyer_Pricing_Net_Ton = product.Actual_Buyer_Pricing_Net_Ton;

    // product.Seller_Pricing_CWT = product.Actual_Seller_Pricing_CWT;
    // product.Seller_Pricing_Ea = product.Actual_Seller_Pricing_Ea;
    // product.Seller_Pricing_Ft = product.Actual_Seller_Pricing_Ft;
    // product.Seller_Pricing_LB = product.Actual_Seller_Pricing_LB;
    // product.Seller_Pricing_Net_Ton = product.Actual_Seller_Pricing_Net_Ton;

    // product.Neutral_Pricing_CWT = product.Actual_Neutral_Pricing_CWT;
    // product.Neutral_Pricing_Ea = product.Actual_Neutral_Pricing_Ea;
    // product.Neutral_Pricing_Ft = product.Actual_Neutral_Pricing_Ft;
    // product.Neutral_Pricing_LB = product.Actual_Neutral_Pricing_LB;
    // product.Neutral_Pricing_Net_Ton = product.Actual_Neutral_Pricing_Net_Ton;

    // const spreadRate = !product.is_safe_product_code ? userData?.buyer_spread_rate : 1;
    // if (userData?.is_buyer_spread) {
    //   product.Buyer_Pricing_CWT = "$" + (product[userData.base_pricing_column + '_CWT'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Ea = "$" + (product[userData.base_pricing_column + '_Ea'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Ft = "$" + (product[userData.base_pricing_column + '_Ft'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_LB = "$" + (product[userData.base_pricing_column + '_LB'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Net_Ton = "$" + (product[userData.base_pricing_column + '_Net_Ton'].trim().replace(/[\$,]/g, '') * spreadRate);

    //   const sellerSpreadRate = !product.is_safe_product_code ? userData?.seller_spread_rate : 1;
    //     product.Seller_Pricing_CWT = "$" + (product[userData.base_pricing_column + '_CWT'].trim().replace(/[\$,]/g, '') * sellerSpreadRate);
    //     product.Seller_Pricing_Ea = "$" + (product[userData.base_pricing_column + '_Ea'].trim().replace(/[\$,]/g, '') * sellerSpreadRate);
    //     product.Seller_Pricing_Ft = "$" + (product[userData.base_pricing_column + '_Ft'].trim().replace(/[\$,]/g, '') * sellerSpreadRate);
    //     product.Seller_Pricing_LB = "$" + (product[userData.base_pricing_column + '_LB'].trim().replace(/[\$,]/g, '') * sellerSpreadRate);
    //     product.Seller_Pricing_Net_Ton = "$" + (product[userData.base_pricing_column + '_Net_Ton'].trim().replace(/[\$,]/g, '') * sellerSpreadRate); 

    // }
    
    Object.keys(product).forEach(key => {
      if (key.toLowerCase().includes(orderIncrementPrefix)) {
        let newKey = key.toLowerCase();
        if (key.toLowerCase().includes(priceUnits.ea)) {
          newKey = orderIncrementPrefix + priceUnits.pc;
        }
        product[newKey] = product[key];
        if (newKey !== key) delete product[key];
      }
    });
    product.QUM_Dropdown_Options = product.QUM_Dropdown_Options.toLowerCase().replace(priceUnits.ea, priceUnits.pc)?.split(",") || [];
    product.PUM_Dropdown_Options = product.PUM_Dropdown_Options.toLowerCase().replace(priceUnits.ea, priceUnits.pc)?.split(",") || [];

    product.PUM_Dropdown_Options = product?.PUM_Dropdown_Options?.filter((item: string) => !(item.trim().toLowerCase() === priceUnits.net_ton || item.trim().toLowerCase() === 'net ton')) || [];
    productMap[product.Product_ID] = product;
  });
  return productMap;
}

export const updatedAllProductsData = (products: any[]) => {
  if (products?.length) {
    return products.map(product => ({
      ...product,
      // Actual_Buyer_Pricing_CWT: product.Buyer_Pricing_CWT,
      // Actual_Buyer_Pricing_Ea: product.Buyer_Pricing_Ea,
      // Actual_Buyer_Pricing_Ft: product.Buyer_Pricing_Ft,
      // Actual_Buyer_Pricing_LB: product.Buyer_Pricing_LB,
      // Actual_Buyer_Pricing_Net_Ton: product.Buyer_Pricing_Net_Ton,

      // Actual_Seller_Pricing_CWT: product.Seller_Pricing_CWT,
      // Actual_Seller_Pricing_Ea: product.Seller_Pricing_Ea,
      // Actual_Seller_Pricing_Ft: product.Seller_Pricing_Ft,
      // Actual_Seller_Pricing_LB: product.Seller_Pricing_LB,
      // Actual_Seller_Pricing_Net_Ton: product.Seller_Pricing_Net_Ton,

      // Actual_Neutral_Pricing_CWT: product.Neutral_Pricing_CWT,
      // Actual_Neutral_Pricing_Ea: product.Neutral_Pricing_Ea,
      // Actual_Neutral_Pricing_Ft: product.Neutral_Pricing_Ft,
      // Actual_Neutral_Pricing_LB: product.Neutral_Pricing_LB,
      // Actual_Neutral_Pricing_Net_Ton: product.Neutral_Pricing_Net_Ton,
    }));
  }
  return products;
}

export const toTitleCase = (str: string): string => str.toLowerCase().replace(/\b\w/g, char => char.toUpperCase());

export const getOrderIncrementUnit = (unit: string) => {
  if (!unit) return '';
  if (unit.toLowerCase() === 'cwt') {
      return 'CWT';
  } else if (unit.toLowerCase() === 'ea' || unit.toLowerCase() === 'pc') {
      return 'Ea';
  } else if (unit.toLowerCase() === 'ft') {
      return 'Ft';
  } else if (unit.toLowerCase() === 'lb') {
      return 'Lb';
  }
};

export const calculateLineWeight = (data: any) => {
  const qty = data.quantity ? parseFloat(data.quantity) : 0;
  const qtyUnit = data.qtyUnit ? data.qtyUnit : data.product.QUM_Dropdown_Options.split(",")[0];
  const orderIncrementLb = parseFloat(data.product[`${orderIncrementPrefix}${priceUnits.lb}`].replace(/[\$,]/g, '')) || 0;
  const orderIncrementFt = parseFloat(data.product[`${orderIncrementPrefix}${priceUnits.ft}`].replace(/[\$,]/g, '')) || 0;
  const lbsPerFt = orderIncrementLb / orderIncrementFt
  const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
  const actualOrderIncrement = parseFloat(data.product[`${orderIncrementPrefix}${qtyUnit === priceUnits.ea ? priceUnits.pc : qtyUnit}`].replace(/[\$,]/g, ''));
  return orderIncrementFtPrice * qty / actualOrderIncrement;
}

export const calculateTotalWeightForProduct = (dataArray: any) => {
  let totalOrderWeights = 0;
  dataArray?.forEach((data: any) => {
      let lineWeight = 0;
      const qty = data.quantity ? parseFloat(data.quantity) : 0;
      // const price = data.buyerExtended ? parseFloat(data.buyerExtended) : 0;
      if(data?.product && Object.keys(data?.product).length > 0 && qty > 0 && !data?.isCancelled){
          if (data?.product) {
            lineWeight = calculateLineWeight(data);
          }
      }
      totalOrderWeights = +(formatToTwoDecimalPlaces(totalOrderWeights).replace(/[\$,]/g, "")) + +(formatToTwoDecimalPlaces(lineWeight).replace(/[\$,]/g, ""));
  });
  return totalOrderWeights;
}