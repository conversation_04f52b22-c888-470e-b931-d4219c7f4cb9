import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { ErrorMessageResponses, reactQueryKeys } from "../utils/constant";
import { CommonStringDto, MapTransactionToPoPayloadDto } from "@bryzos/giss-common-lib";

const usePostMapTransactionToPo = () => {
  const queryClient = useQueryClient();

  return useMutation(async (data: MapTransactionToPoPayloadDto) => {
    try {
      const response = (await axios.post(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/cass/mapTransactionToPo`, data)).data;

      queryClient.invalidateQueries([reactQueryKeys.getProbablePo]);
      queryClient.invalidateQueries([reactQueryKeys.getPos]);
      queryClient.invalidateQueries([reactQueryKeys.getMappedCompanies]);

      if (response.data) {
        if (typeof response.data === "object" && "error_message" in response.data) {
          if(response.data.error_message.includes(ErrorMessageResponses.cassMapppedUnableToClosePo)){
            return "Successfully updated"
          }else{
            throw new Error(response.data.error_message);
          }
        } else {
          return response.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostMapTransactionToPo;
