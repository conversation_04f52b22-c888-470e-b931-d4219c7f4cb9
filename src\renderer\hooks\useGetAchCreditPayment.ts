import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQuery<PERSON>eys } from "../utils/constant";

const useGetAchCreditPayment = () => {
  return useQuery([reactQueryKeys.getAllAchCreditPayment], async () => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/getAchCreditPayementApprovalList`
      );
      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default useGetAchCreditPayment;
