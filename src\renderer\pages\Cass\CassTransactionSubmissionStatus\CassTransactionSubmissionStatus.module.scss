.CassTransactionMain{
    .selectDropdownCass{
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        @media (max-width:767px) {
            align-items: flex-start;
            .col1{
                width: 100%;
            }
        }
        .totalSumListMain{
            .totalSummaryTitle{
                font-family: Noto Sans;
                font-size: 20px;
                font-weight: bold;
                @media (max-width:767px) {
                   font-size: 16px;
                   margin-top: 12px;
                }
            }
            .totalSumList{
                padding: 6px 30px;
                height: 100px;
                background-color: #e4e4e4;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                margin-top: 6px;
                margin-bottom: 0px;
                @media (max-width:767px) {
                    height: auto;
                    flex-direction: column;
                    width: 100%;
                    padding: 12px;
                }
                li{
                    display: inline-block;
                    margin-right: 12px;
                    padding: 6px 12px;
                    white-space: pre-wrap;
                    color: #fff;
                    border-radius: 4px;
                    @media (max-width:767px) {
                        width: 100%;
                        margin-bottom: 12px;
                        margin-right: 0px;
                        &:last-child{
                            margin-bottom: 0px;
                        }
                    }
                    &.totalSuccess{
                        background-color: #8bc34a;
                    }
                    &.totalFailure{
                        background-color: #ff5d47;
                    }
                    &.totalPending{
                        background-color: #ffc107;
                    }
                    &.totalSum{
                        color: #122b40;
                        font-size: 20px;
                        padding: 0px 0px 0px 12px;
                    }
                }
            }
        }
       
    }
    .CassTransactionMainTitle{
        font-family: Noto Sans;
        font-size: 17px;
        font-weight: 500;
        margin-bottom: 8px;
    }
    .leftColCassTransaction{
        display: flex;
        column-gap: 12px;
        @media (max-width:767px) {
            flex-direction: column;
            row-gap: 12px;
        }
    }

    .CassTransactionChk{
        display: flex;
        align-items: center;
        column-gap: 6px;
        margin-top: 16px;
        p{
            font-weight: bold;
        }
        label{
            display: flex;
            align-items: center;
            font-family: Noto Sans;
            font-size: 14px;
            input{
                margin-right: 6px;
            }
        }
    }
    .selectDateCol{
        display: flex;
        align-items: center;
        column-gap: 12px;
    }
}

.searchBox {
    display: flex;
    align-items: flex-end;
    column-gap: 12px;

    &.selectDateColMain{
        @media (max-width:767px) {
           display: flex;
           column-gap: 8px;
           flex-direction: unset;
           .selectDateCol{
            flex-direction: column;
            align-items: flex-start;
            label{
                margin-bottom: 4px;
                font-size: 14px;
            }
           }
        }
    }

    @media screen and (max-width: 768px) and (min-width: 320px) {
        display: flex;
        flex-direction: column;
    }

    .showdropdwn {
        width: 82px;
        height: 38px;
        padding: 4px;
        @media screen and (max-width: 768px) and (min-width: 320px) {
           width: 100%;
        }
    }

    .searchInput {
        box-shadow: none;
        outline: none;
        height: 38px;
        padding: 6px 12px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        @media (max-width:767px) {
            width: 100%;
            margin-top: 16px;
        }
    }

    .rightEditSection {
        display: flex;
        margin-left: auto;
        @media (max-width:767px) {
            width: 100%;
        }
        .editBtn {
            width: 70px;
            height: 38px;
            font-size: 14px;
            color: #fff;
            background-color: var(--primaryColor);
            border-color: #122b40;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 12px;
            border: 0px;
        }
    }
}

.backDrop{
    position: fixed;
    background-color: rgba(0, 0, 0, 0.3);
    width: 100%;
    height: 100%;
    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;
    z-index: 999;
}

.loaderImg {
    text-align: center;
    margin-left: 50%;
    margin-right: auto;
    margin-top: 36vh;
    width: auto;
    position: absolute;
    z-index: 1000;
}

.loaderPosition {
    display: flex;
    justify-content: center;
}

.selectDropdown {
    width: 100%;
    max-width: 300px;
    height: 40px;
    min-width: 300px;
    font-family: Noto Sans;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        width: 100%;
    }
}

.agGridAdmin {
    --ag-icon-font-code-asc: '\25B2';
    --ag-icon-font-code-desc: '\25BC';
    --ag-icon-font-code-none: '\25B2\25BC';

    .ag-center-cols-viewport {
        min-height: 5000px !important;
    }

    .ag-icon-asc::before {
        content: var(--ag-icon-font-code-asc);
    }

    .ag-icon-none::before {
        content: var(--ag-icon-font-code-none);
        color: green;
        padding: 2px;
        margin-bottom: 5px;
        font-size: 20px !important;
    }

    .ag-root-wrapper {
        .ag-root-wrapper-body {
            .ag-body-horizontal-scroll-viewport {
                overflow-x: auto;

                &::-webkit-scrollbar {
                    width: 8px;
                    height: 6px;
                }

                &::-webkit-scrollbar-track {
                    box-shadow: inset 0 0 6px #a8b2bb;
                    border-radius: 4px;
                }

                &::-webkit-scrollbar-thumb {
                    background: #a8b2bb;
                    border-radius: 4px;
                }
            }

            .ag-header-row {
                .ag-header-cell {
                    padding-left: 20px;
                    padding-right: 20px;
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    color: #fff;
                    background: #676f7c;

                    &:hover {
                        color: #fff;
                        background: #676f7c;
                    }
                }

                .ag-header-cell:not(.ag-column-resizing)+.ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover {
                    color: #fff;
                    background: #676f7c;
                }
            }

            .ag-body-viewport-wrapper.ag-layout-normal {
                overflow-x: scroll;
                overflow-y: scroll;
            }

            ::-webkit-scrollbar {
                -webkit-appearance: none;
                width: 8px;
                height: 6px;
            }

            ::-webkit-scrollbar-thumb {
                border-radius: 4px;
                background: #a8b2bb;
                box-shadow: inset 0 0 6px #a8b2bb;
            }

            .ag-body {
                .ag-body-viewport {
                    .ag-center-cols-clipper {
                        .ag-row-odd {
                            background-color: #f2f2f2;
                        }

                        .ag-cell {
                            cursor: pointer;
                        }

                        .red-border {
                            border: 1px solid red;
                        }
                    }
                }
            }
        }
    }
}

.ag_theme_quartz {
    --ag-foreground-color: #676f7c;
    --ag-background-color: white;
    --ag-header-foreground-color: white;
    --ag-header-background-color: #676f7c;
    --ag-odd-row-background-color: #f2f2f2;
    --ag-header-column-resize-handle-color: rgb(126, 46, 132);
    --ag-font-size: 14px;
    --ag-font-family: monospace;
    --ag-icon-font-code-aggregation: "\f247";
    --ag-icon-font-color-group: red;
    --ag-icon-font-weight-group: normal;
}

.truncateText{
    p{
        width: 267px; 
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &::after{
            content: '';
            display: block;
          }
      }
} 

.StatusBtn{
    border: 0px;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: 500;
    width: 90px;
    height: 34px;
    border-radius: 4px;
    color: #fff;
    display: block;
    &.successStatus{
        background-color: #8bc34a;
    }
    &.rejectStatus{
        background-color: #ff5d47;
    }
    &.pendingStatus{
        background-color:#ffc107 ;
    }
}