import { useCallback, useContext, useEffect, useState } from "react";
import styles from "./PreApprovedEmails.module.scss";
import { addPreapprovedEmailsFormSchema, addPreapprovedEmailsFormSchemaType } from "../../../models/addPreapprovedEmails.model";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { CommonCtx } from "../../AppContainer";
import * as XLSX from "xlsx";
import FileDrop from "../../../components/common/FileDrop";
import { useImmer } from "use-immer";
import { ReactComponent as DelIcon } from '../../../../assests/images/close-icon.svg';
import useGetAdminReferenceData from "../../../hooks/useGetAdminReferenceData";
import ConfiramtionBox from "../../../components/common/ConfiramtionBox";
import { preApprovedEmailConstant } from "../../../utils/constant";
import { Autocomplete, TextField, Tooltip } from "@mui/material";
import clsx from "clsx";

const AddEmails = ({ closePopup, addEmails }) => {
    const defaultLine = {
        email: "",
        cohort: ""
    }
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const { data: adminReferenceData } = useGetAdminReferenceData();
    const [message, setMessage] = useState('');
    const [error, setOnDropError] = useState('');
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        getValues,
        trigger,
        reset,
        control,
        formState: { isValid, errors },
    } = useForm<addPreapprovedEmailsFormSchemaType>({
        defaultValues: {
            data: [{
                email: "",
                cohort: ""
            }]
        },
        resolver: yupResolver(addPreapprovedEmailsFormSchema),
        mode: "onBlur",
    });
    
    const { fields, append, remove, } = useFieldArray({
        control,
        name: "data"
    });
    let templateColumns: any[] = ['Email', 'Cohort'];
    const [userExcelData, setuserExcelData] = useImmer<any>(null);
    const [userWorksheetData, setuserWorksheetData] = useImmer<any>(null);
    const [dragData, setDragData] = useImmer<any>(null);
    const [finalColumnMap , setFinalColumnMap] = useImmer<any>({});
    const [userExcelColumnMap , setUserExcelColumnMap] = useImmer<any>({});
    const [onBoardedTypeSet , setOnBoardedTypeSet] = useState<Set<string>>();
    const [showConfirmationPopup, setShowConfirmationPopup] = useState<boolean>(false);
    const [confirmationText, setConfirmationText] = useState<string>(preApprovedEmailConstant.confirmationMessage);
    const [fileData, setFileData] = useState<any>(null);
    const [showOnlyErrors, setShowOnlyErrors] = useState<boolean>(false);
    const [confirmationPopupYes, setConfirmationPopupYes] = useState<boolean>(false);
    const [clearAllClick, setClearAllClick] = useState<boolean>(false);
    const [showErrorCheckBox, setShowErrorCheckbox] = useState<boolean>(false);

    useEffect(()=>{
        if(adminReferenceData?.onboard_source){
            const onBoardedTypeList = adminReferenceData.onboard_source.map(data => data.onboarded_app);
            setValue('onBoardList', onBoardedTypeList)
            setOnBoardedTypeSet(new Set(onBoardedTypeList));
        }
    },[adminReferenceData])
    
    const submit = async ({data}: any) => {
        if(data){
          const payload = data.map((obj) =>{
            obj.cohort = !obj.cohort ? null : obj.cohort;
            return obj; 
          })
            addEmails(payload)
        }
    }

    const onDropError = ({file, code}:any)=>{
        if(file){
            setOnDropError("");
            setMessage("");
            setOnDropError(`${file.name} is not supported. We only support *.xlsx and *.csv files.`);
        }
    }

    const onDrop = useCallback(({ file }: any) => {
        if (file) {
            setOnDropError("");
            setMessage("");
            const reader = new FileReader();
            reader.onload = (e: any) => {
                if (
                    typeof e.target?.result !== "string" &&
                    typeof e.target?.result !== "undefined" &&
                    e.target?.result
                ) {
                    const check: ArrayBufferLike = e.target?.result;
                    const data = new Uint8Array(check);
                    const workbook = XLSX.read(data, { type: "array" });
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    setuserWorksheetData(worksheet);
                    setuserExcelData(excelData);

                    if (excelData.length !== 0) {
                        const excelColumns: any = excelData[0];
                        templateColumns.forEach((templateColumn: string) => {
                            setFinalColumnMap((data: any) => {
                                data[templateColumn] = null;
                                return data;
                            });
                        });
                        excelColumns.forEach((excelColumn: string) => {
                            setUserExcelColumnMap((data: any) => {
                                data[excelColumn] = null;
                                return data;
                            });
                        });
                        excelColumns.forEach((excelColumn: string) => {
                            templateColumns.forEach((templateColumn: string) => {
                                if (
                                    templateColumn.replace(/[\s_/$()#[\]\-.]+/g, '').toLowerCase() ===
                                    excelColumn.replace(/[\s_/$()#[\]\-.]+/g, '').toLowerCase()
                                ) {
                                    setFinalColumnMap((data: any) => {
                                        data[templateColumn] = excelColumn;
                                        return data;
                                    });
                                    setUserExcelColumnMap((data: any) => {
                                        data[excelColumn] = templateColumn;
                                        return data;
                                    });
                                }
                            });
                        });
                    } else {
                        showPopupFormAnyComponent("Invalid excel file");
                        file.target.value = "";
                    }
                }
            };
            reader.readAsArrayBuffer(file);
        }
    }, []);

    
  const onDragLeave = (e: any) => {
    e.preventDefault();
    let element = e.currentTarget;
    element.classList.remove("dragged-over");
  }

  const onDragEnter = (e: any) => {
    e.preventDefault();
    let element = e.currentTarget;
    element.classList.add("dragged-over");
    e.dataTransfer.dropEffect = "move";
  }

  const onDragEnd = (e: any) => {
    e.currentTarget.classList.remove("dragged");
  }

  const onDragOver = (e: any) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  }

  const onDropColumns = (e: any, columnName: any, columnData: any,  status: string) => {
    e.preventDefault();
    e.currentTarget.classList.remove("dragged-over");
    if(status === 'excel'){
      setFinalColumnMap((data: any) => {
        Object.keys(data).forEach((column: string) => {
          if(dragData === data[column]){
            data[column] = null
          }
        })
        return data;
      })
      setUserExcelColumnMap((data: any) => {
        Object.keys(data).forEach((column: string) => {
          if(dragData === column){
            data[column] = null
          }
        })
        return data;
      })
    }
    if(status === 'bryzos'){
      setFinalColumnMap((data: any) => {
        Object.keys(data).forEach((column: any) => {
          if(dragData === data[column]){
            data[column] = null
          }
        })
        data[columnName] = dragData;
        return data;
      })
      setUserExcelColumnMap((data: any) => {
        if(columnData){
          data[columnData] = null;
        } 
        data[dragData] = columnName;
        return data;
      })
    }
  }

  const onDragStart = (e: any, data: any) => {
    let element = e.currentTarget;
    element.classList.add("dragged");
    e.dataTransfer.setData("text/plain", e.currentTarget.id);
    e.dataTransfer.effectAllowed = "move";
    setDragData(data);
  }

  const verifyFinalColumnMap = () => {
    let finalColumnValueCount = 0 ;
    Object.keys(finalColumnMap).forEach((finalColumnKey: any) => {
      if(finalColumnMap[finalColumnKey] !== null){
        finalColumnValueCount += 1;
      }
    })
    if(Object.keys(finalColumnMap).length === finalColumnValueCount){
      showVerifyColumnList();
    }
  }

  
  const showVerifyColumnList = () => {
    let userExcelFirstColumn: any[] = [];
    let checkData: any;
    let templateHeaderToUserExcelIndexArr: any[] = [];
    let checkForExcelTemplate: any[] = [];
    const emailSet = new Set();
    let filterCheckForExcelTemplate: any[] = [];
    userExcelFirstColumn[0] = Object.keys(finalColumnMap);
    templateColumns.forEach((templateHeader) => {
      templateHeaderToUserExcelIndexArr.push(
        userExcelData[0].findIndex(
          (item: any) => item === finalColumnMap[templateHeader]
        )
      );
    });
    userExcelData.forEach((rowData: any[], index: any) => {
      if (index > 0) {
        checkForExcelTemplate.push(
          updateRowData(templateHeaderToUserExcelIndexArr, index)
        );
      }
    });
    checkForExcelTemplate.forEach((data) => {
        const spreadData = {...data};
        if(!emailSet.has(data.email)){
            emailSet.add(data.email);
            if(!onBoardedTypeSet?.has(data.cohort)){
                spreadData.cohort = null;
            }
            filterCheckForExcelTemplate.push(spreadData);
        }
    })
    checkData = filterCheckForExcelTemplate;
    setValue('data', checkData);
    trigger('data');
    cancelOnDragColumns();
  };

  
  const updateRowData = ( headerToIndexMap:any[], rowNumber: number)=>{
    let retObj:any = {};
    headerToIndexMap.forEach((header:any, index:any)=>{
      const cellName = toColumnName(header+1) + (rowNumber+1);
      if(userWorksheetData[cellName]?.v){
          retObj[templateColumns[index].toLowerCase()] = userWorksheetData[cellName].v;
      }
    });

    return retObj
  }

  const toColumnName = (columnNumber: any)=>{
    let columnName = "";
    while (columnNumber > 0) {
      let remainder = (columnNumber - 1) % 26;
      columnName = String.fromCharCode(65 + remainder) + columnName; 
      columnNumber = Math.floor((columnNumber - 1) / 26);
    }
    return columnName;
  }

  const addNewLine = () => {
    append({...defaultLine});
  }

  const inputFields = fields?.map((line, index)=>{
    const showErrorValuesCondition = (errors?.data?.some((v, i)=> v && i === index) || watch(`data.${index}.cohort`) === null);
    return(
        <tr key={index}>
       {((!showOnlyErrors) || (showOnlyErrors && showErrorValuesCondition)) &&
       <>
        <td>{index+1}</td>
        <td className={styles.inputBox}>
            <Tooltip
                title={errors.data?.[index]?.email?.message}
                placement="top-end"
                classes={{
                    popper: styles.errorStyle,
                    tooltip: styles.tooltip,
                }}
            >
                <input 
                    type="email"
                    placeholder="Email ID"
                    style={{ width: '100%', padding: '10px', resize: 'none' }}
                    {...register(`data.${index}.email`)}
                    className={`${errors.data?.[index]?.email?.message ? styles.textareaError : ''}`}
                />
            </Tooltip>
        </td>
        <td>
                <Controller
                    name={register(`data.${index}.cohort`).name}
                    control={control}
                    render={({
                      field: { onChange, onBlur, value, name, ref },
                      fieldState: { error },
                    }) => (
                      <Tooltip
                          title={(watch(`data.${index}.cohort`) === null) ? 'Invalid Cohort' : ''}
                          placement="top-end"
                          classes={{
                              popper: styles.errorStyle,
                              tooltip: styles.tooltip,
                          }}
                      >
                        <Autocomplete
                            className={clsx(styles.selectDropdown,'cassMappingDropdown', `${(watch(`data.${index}.cohort`) === null) ? 'textareaErrorDrodown' : ''}`)}
                            options={getValues('onBoardList') ?? []}
                            value={getValues('onBoardList')?.find((data: any) => data === watch(`data.${index}.cohort`)) ?? null}
                            getOptionLabel={(option: any) => option ?? ""}
                            renderInput={(params) => (
                                <TextField {...params} placeholder="Choose Cohort Name" />
                            )}
                            onChange={(event, data: any) => {
                                onChange(data ? data : null);
                            }}
                            classes={{
                                root: styles.autoCompleteDesc,
                                popper: styles.autocompleteDescPanel,
                                paper: styles.autocompleteDescInnerPanel,
                                listbox: styles.listAutoComletePanel,
                            }}
                        />
                      </Tooltip>
                    )}
                />
        </td>
        <td>
            {(fields.length > 1 && index > 0) && <button className={styles.clearIcon} onClick={() => removeLine(index)}><DelIcon/></button>}
        </td>
       </>
        }
        </tr>
    )

  })

  const cancelOnDragColumns = () => {
    setUserExcelColumnMap({})
    setFinalColumnMap({})

  }

  const removeLine = (index) => {
    remove(index);
  }

    const showConfirmation = ({ file }: any) => {
        if(isDataAvailable()){
            setFileData({file});
            setShowConfirmationPopup(true);
        }else{
            onDrop({file});
        }
    }

    const confirmationYes = () => {
        const onBoardList = getValues('onBoardList');
        if(!clearAllClick){
            if(fileData){
                onDrop(fileData);
            }else{
                setConfirmationPopupYes(true);
            }
        }else{
            setClearAllClick(false);
        }
        setValue('data', [defaultLine]); 
        reset();
        setValue('onBoardList', onBoardList);
        setShowOnlyErrors(false);
        confirmationPopupClose();
    }

    const confirmationPopupClose = () => {
        setShowConfirmationPopup(false);
        setFileData(null);
    }

  const handleDisableVerifyBtn = () => {
    let finalColumnValueCount = 0 ;
    Object.keys(finalColumnMap).forEach((finalColumnKey: any) => {
      if(finalColumnMap[finalColumnKey] !== null){
        finalColumnValueCount += 1;
      }
    })
    if(Object.keys(finalColumnMap).length !== finalColumnValueCount) return true;
    return false;
  }

  const isDataAvailable = () => {
    return watch('data')?.some(dataObj =>
        Object.values(dataObj).some(value => value !== null && value !== undefined && value !== '')
    );
  }

    return <div className={styles.addEmailPopupDiv}>
        {(Object.keys(userExcelColumnMap).length !== 0 && Object.keys(finalColumnMap).length !== 0) ? 
        <>
            <div className={styles.uploadRefernceData}>
              <div className={styles.uploadRefernceDataHeader}
              onDragLeave={(e) => onDragLeave(e)}
              onDragEnter={(e) => onDragEnter(e)}
              onDragEnd={(e) => onDragEnd(e)}
              onDragOver={(e) => onDragOver(e)}
              
              >
                <div className={styles.title}>Your Header</div>
                <span className={styles.dragBtnGrid}>
                {Object.keys(userExcelColumnMap)?.map((userExcelColumn: any)=>{
                  return <button className={styles.dragBtn} key={userExcelColumn}
                  draggable = {userExcelColumnMap[userExcelColumn] === null}
                      onDragStart={(e) => onDragStart(e, userExcelColumn)}
                      onDragEnd={(e) => onDragEnd(e)}
                      onDrop={(e) => onDropColumns(e, userExcelColumn, userExcelColumnMap[userExcelColumn],'excel')}
                  > 
                    {userExcelColumn} 
                  </button>
                })}
                </span>
               
              </div>
              <div
              onDragLeave={(e) => onDragLeave(e)}
              onDragEnter={(e) => onDragEnter(e)}
              onDragEnd={(e) => onDragEnd(e)}
              onDragOver={(e) => onDragOver(e)}
              
              >
                <div className={styles.title}>Required Header </div>
                {Object.keys(finalColumnMap)?.map((finalColumn: string, index: number) => {

                  return <div className={styles.dragColumn}
                  key={index}
                  onDrop={(e) => onDropColumns(e, finalColumn,  finalColumnMap[finalColumn], 'bryzos')}
                  >
                    <span className={styles.dragColumnlbl}>{finalColumn}</span>
                    <button
                      draggable = {finalColumnMap[finalColumn] !== null}
                     onDragStart={(e) => onDragStart(e, finalColumnMap[finalColumn])}
                     onDragEnd={(e) => onDragEnd(e)}
                     
                    >{finalColumnMap[finalColumn] ?? finalColumn}</button>
                    </div>
                })}
              </div>
              <div className={styles.yesAndnoBtn}>
                <button className={styles.okBtn} onClick={verifyFinalColumnMap} disabled={handleDisableVerifyBtn()} > Continue </button>
                <button className={styles.okBtn} onClick={cancelOnDragColumns}> Cancel </button>
              </div>
            </div>
        </>
        :
        <>
            <div className={styles.addCohortMain}>
              <div className={styles.addBtnSection}>
                <button className={styles.addInputBtn} onClick={addNewLine} disabled={showOnlyErrors} >Add</button>
                <button className={styles.addInputBtn} onClick={()=>{setClearAllClick(true); setShowConfirmationPopup(true);}}>Clear All</button>
              </div>
                <div className={styles.checkboxMain}>
                    <label className={clsx(styles.containerChk,"containerChk")}>
                        <input
                            type="checkbox"
                            checked={showOnlyErrors}
                            onChange={(e) =>{
                                setShowOnlyErrors(e.target.checked)
                            }}
                        />
                        <span className={clsx(styles.checkmark,"checkmark")} />
                    </label>
                    Show Only Errors
                </div>
            </div>
            <div className={styles.inputFieldContainer}>
              <table>
                <thead>
                  <tr>
                    <th>LN</th>
                    <th>EMAIL ID</th>
                    <th colSpan={2}>COHORT</th>
                  </tr>
                </thead>
                <tbody>
                    {(showOnlyErrors && (!errors?.data && !watch('data')?.some(val => val.cohort === null))) ?
                        <tr>
                            <td className={styles.noErrorTxt} colSpan={3}> No Errors Found </td>
                        </tr>
                        :
                        inputFields

                    }
                </tbody>
              </table>
            </div>
            <div className={styles.messageContainer}>
            {message && <>
                <div className={styles.message}>{message}</div>
            </>
            }
            {error && <><div className={styles.error}>{error}</div></>}
            </div>
            <FileDrop 
                types={['.xlsx','.csv']} 
                success={showConfirmation} 
                error={onDropError} 
                label={"Drag and drop xlsx or csv files to extract emails"}
                isDataAvailable={isDataAvailable()}
                setShowConfirmationPopup={setShowConfirmationPopup}
                setConfirmationPopupYes={setConfirmationPopupYes}
                confirmationPopupYes={confirmationPopupYes}
                />
            <p className={styles.error}>Note: <br/>
            1. Please ensure the file contains columns for emails and cohorts. <br/>
            2. If your file contains more than 300 rows then do not use this feature and get it uploaded from the Engineering Team. </p>
            <div className={styles.addEmailBtn}>
                <button className={styles.cancelBtn} onClick={handleSubmit(submit)} disabled={!isValid}>Add</button>
                <button className={styles.cancelBtn} onClick={closePopup}>Cancel</button>
            </div>
        </>
        }
        <ConfiramtionBox
            openConfirmationPopup={showConfirmationPopup}
            confirmationYes={confirmationYes}
            confirmationNo={confirmationPopupClose}
            confirmationText={confirmationText}
        />
    </div>
};

export default AddEmails;