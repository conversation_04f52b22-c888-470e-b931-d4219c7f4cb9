import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { parsePipeSeparatedFile, removeAlternateCharactersFromLines } from '../../../utils/helper';
import FileDrop from '../../../components/common/FileDrop';
import styles from '../LogReader.module.scss'
import clsx from 'clsx';
interface FileUploadProps {
    onFileParsed: (data: any[]) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileParsed }: FileUploadProps) => {
    const [showLoader, setShowLoader] = useState(false)
    const [fileUploadError, setFileUploadError] = useState('');
    const [isRemoveExtraSpace, setIsRemoveExtraSpace] = useState(true);

    const onDrop = useCallback(({ file }: any) => {
        setFileUploadError("");
        if (file) {
            setShowLoader(true);
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const fileContent = e.target?.result as string;
                    let contentData = fileContent;
                    if (isRemoveExtraSpace) {
                        contentData = removeAlternateCharactersFromLines(contentData);
                    }
                    const fileParsedData =  parsePipeSeparatedFile(contentData);
                    onFileParsed(fileParsedData);
                    setShowLoader(false);
                } catch (error) {
                    setShowLoader(false);
                    setFileUploadError(error?.message ?? 'Something went wrong please try again.')
                }
            };
            reader.readAsText(file);
        }
    }, []);

    const onDropError = ({ file, code }: any) => {
        if (file) {
            setFileUploadError(`${file.name} is not supported. We only support *.log and *.txt files.`);
        }
    }

    return (
        <>
            {
                showLoader ? <p className={styles.loadingText}>Loading please wait.</p>
                    :
                    <div className={styles.fileUploadMain}>
                        <div className={styles.fileDrop}>
                        <FileDrop
                            types={['.log', '.txt']}
                            success={({ file }) => onDrop({ file })}
                            error={onDropError}
                            label={"Drag and drop log file to extract logs"}
                        />
                        </div>
                       
                        <label className={clsx(styles.containerChk, "containerChk")}>
                            <input
                                type="checkbox"
                                checked={isRemoveExtraSpace}
                                onChange={(e) => setIsRemoveExtraSpace(e.target.checked)}
                            />
                            <span className="checkmark" />
                        </label>
                        remove extra space
                        <p className={styles.errorTxt}>{fileUploadError}</p>
                    </div>
            }
        </>
    );
};

export default FileUpload;
