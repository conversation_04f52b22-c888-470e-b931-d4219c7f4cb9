
.tblscroll.tblscroll {
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 35px;
  max-height: 700px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }

  table {
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 35px;
    border-collapse: collapse;
    border-spacing: 0;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px #a8b2bb;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
    }

    thead {
      tr {
        border: 1px solid gray;

        th {
          line-height: 1.2;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
          text-align: left;
          padding: 6px 12px;
          color: #fff;
          height: 35px;
          position: sticky;
          top: 0;
          background: #676f7c;
          color: #fff;
          z-index: 100;
          
          &.thDescription{
            width: 300px;
           }
 
        }

        td {
          line-height: 2.5;
          font-weight: 600;
          font-size: 16px;
          margin: 0;

          &:nth-child(even) {
            background-color: #f2f2f2;
          }
        }
      }
    }

    tbody {
      background-color: #fff;

      tr {
        margin: 0;

        &:nth-child(even) {
          background-color: #f2f2f2;
        }

        td {
          color: #343a40;
          font-size: 16px;
          margin: 0;
          padding: 6px 12px;
          height: 42px;
          word-wrap: break-word;
          overflow: hidden;
          text-wrap: wrap;

          &.videoDescription{
            min-width: 300px;
            white-space: break-spaces;
          }

          .messageText {
            box-shadow: none;
            outline: none;
            height: 38px;
            width: 100%;
            padding: 6px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out,
              box-shadow 0.15s ease-in-out;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .thumbImg {
            display: flex;
            img {
              width: 180px;
              height: 100px;
              object-fit: cover;
              border-radius: 4px;
              border: solid 1px transparent;
            }
          }

          &.videoTbl {
            .videoThumbBox {
              width: 180px;
              height: 100px;
              flex-grow: 0;
              border-radius: 4px;
              background-color: rgba(0, 0, 0, 0.3);
              position: relative;
              transition: all 0.1s;
              border: solid 1px transparent;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 4px;
              }

              .overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.3);
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
              }

              .VideoPlayIcon {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                cursor: pointer;
                z-index: 99;
              }
            }
          }

          &.videoViewCountColumn {
            text-align: center;
          }
        }
      }
    }
  }
}

.searchBox {
  margin-bottom: 15px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    display: flex;
    flex-direction: column;
  }

  .showdropdwn {
    width: 82px;
    height: 38px;
    padding: 4px;
  }

  .searchInput {
    box-shadow: none;
    outline: none;
    height: 38px;
    padding: 6px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    position: absolute;
    right: 20px;
  }
}

.loaderImg {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: left;
  margin-left: auto;
  margin-right: auto;
  margin-top: 400px;
  span {
    font-size: 15px;
    flex: 1;
    max-width: 380px;
    margin-left: 30px;
    font-weight: 600;
  }
}

.saveButton {
  width: 70px;
  height: 38px;
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    margin-left: 0px;
    margin-top: 10px;
  }
}

.saveBtnf {
  width: 120px;
  height: 38px;
  font-size: 16px;
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;
  position: absolute;
  right: 213px;
  border: 0px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    position: unset;
    margin-top: 10px;
  }
}

.editBtnf {
  width: 70px;
  height: 38px;
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;
}

.noteText {
  font-size: 18px;
  line-height: normal;
  margin-bottom: 40px;
  font-weight: 600;
  color: var(--primaryColor);
  text-align: center;
  color: red;
}

.uploadBox {
  height: 300px;
  margin-top: 8px;
  border-radius: 4px;
  border: dashed 1px #1c40e7;
  background-color: #e9f3ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .uploadText {
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: center;
    color: #1c40e7;
  }

  input {
    display: none;
  }
}

.uploadBoxImage {
  height: 300px;
  width: 100%;
  position: relative;
  margin-top: 8px;
  border-radius: 4px;

  &.uploadBoxThumbImage{
    margin-bottom: 8px;
    border: 1px solid #eee;
  }

  video {
    width: 100%;
    height: 100%;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.closeIcon {
  position: absolute;
  right: -10px;
  cursor: pointer;
  z-index: 99;
  top: -12px;
  svg {
    path {
      fill: #ff0000;
    }
  }
}

.newCarousel {
  position: relative;
  width: 100%;
  height: 100%;

  &.popupVideoPreview {
    height: 100%;

    .playBtn {
      width: 80px;
      height: 80px;
      background-color: transparent;
      border: 0px;
      padding: 0px;
    }
  }

  .playBtn {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    right: 0px;
    width: 128px;
    height: 124px;
    z-index: 101;
    cursor: pointer;

    svg {
      width: 100%;
      height: 100%;
    }

    @media (max-width: 991px) {
      width: 90px;
      height: 90px;
    }

    @media (max-width: 767px) {
      width: 65px;
      height: 60px;
    }
  }

  .videoOverlay {
    opacity: 0.5;
    background-color: #0f0f14;
    position: absolute;
    left: 0px;
    right: 0px;
    top: 25px;
    bottom: 0px;
    width: 100%;
    height: 100%;
    z-index: 99;
  }
}

.uploadVideoPopup.uploadVideoPopup {
  max-width: 850px;
  width: 100%;
  padding: 24px;

  &.multiFilesSelectionPopup{
    max-width: 1320px;
    overflow: hidden;
    .uploadThumbnailSection{
      gap: 10px;
      max-height: calc(100vh - 232px);
      overflow: auto;
      min-height: 400px;
      &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
      }
    
      &::-webkit-scrollbar-thumb {
        background-color: #707378;
        border-radius: 40px;
      }
    
      &::-webkit-scrollbar-track {
        background-color: transparent;
        border-radius: 8px;
        margin-bottom: 5px;
      }
    
      &::-webkit-scrollbar-corner {
        background-color: transparent;
      }

      .thumbnailDescription {
        font-size: 12px;
        margin-top: 8px;
        min-height: 73px;
        ul{
          padding-left: 16px;
          margin-top: 4px;
        }
      }
      .uploadVideo{
        flex: 0 0 305px;
        .thumbTitle{
          font-size: 14px;
          display: inline-flex;
          .uploadVideoNote{
            padding: 0px;
            margin-left: 2px;
            margin-top: -2px;
          }
        }
      }
    }
   
  }

  .uploadVideoTitle {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
  }

  .cassMapptingDropdownBtn {
    font-family: Noto Sans;
    width: 100%;
    max-width: 300px;
    height: 40px;
    font-weight: 400;
    line-height: 1.4375em;
    letter-spacing: 0.00938em;
    text-align: left;
    padding: 9px;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.87);
    border: solid 1px rgba(98, 107, 132, 0.42);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    margin-left: auto;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    .placeholder {
      opacity: 0.42;
    }
    .dataSelected {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      width: 75%;
    }
    .arrowIcon {
      padding: 0px;
      border: 0px;
      display: flex;
      background-color: transparent;
      svg {
        width: 22px;
        height: 22px;
        path {
          fill {
            color: currentColor;
          }
        }
      }
    }
  }

  .col {
    display: flex;
    column-gap: 12px;
    margin-bottom: 12px;
  }

  .inputField {
    display: flex;
    flex: 1;

    .lblInput {
      flex: 1;
      max-width: 100px;
      display: flex;
      align-items: center;
    }
    .checkbox {
      margin: 11px;
      margin-left: 0;
      width: 23px;
    }

    .lbl {
      flex: 1;
      display: flex;
      align-items: center;
      font-weight: bold;
    }

    .inputBox {
      flex: 1;
      max-width: 300px;
      height: 38px;
      padding: 6px;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.5;
      color: #495057;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
    }
  }

  .uploadVideoSection {
    display: flex;
    column-gap: 12px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    padding: 12px 0px;

    span {
      font-size: 20px;
      font-weight: 600;
    }

    .uploadVideo {
      flex-grow: 1;
      max-width: 50%;
    }

    .uploadHeading {
      font-size: 14px;
      font-weight: bold;
      line-height: 1.6;
      text-align: left;
      color: #0f0f14;
      text-transform: uppercase;
    }

    .uploadLabel {
      font-size: 14px;
      font-weight: bold;
      line-height: 1.6;
      color: #393e47;
      text-transform: uppercase;
      margin-top: 12px;

      @media (max-width: 767px) {
        font-size: 12px;
      }

      input {
        outline: none;
        padding-left: 8px;
        height: 30px;
        width: 100%;
        border-radius: 4px;
        border: solid 1px #9b9eac;
        background-color: #fff;
        height: 40px;
        font-size: 14px;
        font-weight: bold;
        line-height: 1.4;
        text-align: left;
        color: #0f0f14;

        &:focus {
          font-weight: normal;
        }
      }
    }

    .uploadBox {
      height: 300px;
      margin-top: 8px;
      border-radius: 4px;
      border: dashed 1px #1c40e7;
      background-color: #e9f3ff;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .uploadText1 {
        font-size: 12px;
        line-height: 1.6;
        text-align: center;
        color: #71737f;
        margin-top: 0px;
      }

      input {
        display: none;
      }
    }

    .continuePopBtn {
      .continueBtn {
        padding: 8px 30px;
        border-radius: 6px;
        background-color: #16b9ff;
        font-size: 14px;
        font-weight: bold;
        line-height: 1.6;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
        width: 100%;
        cursor: pointer;
        border: 0px;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        @media (max-width: 767px) {
          padding: 8px 20px;
          font-size: 10px;
        }
      }
    }

    .captureThumbnail {
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-size: 16px;
        line-height: 1.6;
        text-align: center;
        color: #71737f;
        display: flex;
        margin: 4px 0px;
      }
      button {
        font-family: Inter;
        padding: 6px 12px;
        font-size: 16px;
        cursor: pointer;
        color: var(--primaryColor);
        border-radius: 6px;
        background-color: transparent;
        border: 1px solid #333;
        width: 210px;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .yesAndnoBtn {
    display: flex;
    gap: 10px;
    margin-top: 16px;
    .okBtn {
      width: 100%;
      height: 40px;
      border-radius: 6px;
      text-decoration: none;
      gap: 8px;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

.showVideoPopup.showVideoPopup {
  max-width: 600px;
  width: 100%;
  padding: 45px 24px 24px 24px;
  .closeBtn {
    position: absolute;
    right: 12px;
    top: 12px;
    z-index: 9;
    button {
      background-color: transparent;
      border: 0px;
      padding: 0px;
      cursor: pointer;
    }
  }
  .uploadVideoImgPopup{
    max-height: 310px;
    width: 100%;
    display: flex;
    justify-content: center;
    img{
      max-width: 100%;
    }
  }
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  padding-right: 0px;
  border-radius: 0px 0px 4px 4px;
  overflow: hidden;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  overflow: auto;
  padding: 0px;
  max-height: 250px;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }

  li {
    font-size: 16px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #333;
    box-shadow: none;
    padding: 4px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;

    &:hover {
      border-radius: 2px;
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      background-color: #fff;
      color: #000;
    }
  }
}

.listAutoComletePanel1.listAutoComletePanel1 {
  width: 100%;
  overflow: auto;
  padding: 0px;
  max-height: 320px;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }
  li {
    font-size: 16px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #333;
    box-shadow: none;
    padding: 4px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;

    &:hover {
      border-radius: 2px;
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      background-color: #fff;
      color: #000;
    }
  }
}

.selectDropdown {
  width: 100%;
  max-width: 300px;
  height: 40px;
  min-width: 300px;
  font-family: Noto Sans;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 100%;
  }
}

.uploadVideoNote {
  font-family: Noto Sans;
  font-size: 14px;
  color: #ff0000;
  line-height: normal;
  font-weight: 600;
  padding: 12px 0px;
  &.editVideoNote {
    padding-top: 30px;
  }
}

.uploadVideoNoteReq{
  font-family: Noto Sans;
  font-size: 14px;
  color: #ff0000;
  line-height: normal;
  font-weight: 600;
}

.dialogContent {
  padding: 30px 10px;
  height: 90vh;
  width: 80vw;
}

.modalContent {
  display: flex;
  column-gap: 20px;
  flex-direction: column;
  justify-content: space-between;
  overflow: auto;
  align-items: center;
}

.radioContainer {
  display: flex;
  column-gap: 20px;
  justify-content: center;
    padding-top: 20px;

}

.previewContainer {
  flex: 1;
  display: flex;
  justify-content: center;
  width: 100%;
}
.previewContent {
  display: flex;
  align-items: center;
  color: white;
  column-gap: 10px;
  margin-top: 10px;

  h1 {
    font-family: var(--titleFont);
    font-size: 17.5px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.16;
    letter-spacing: normal;
    text-align: left;
    color: white;
    text-transform: uppercase;
    margin: 0px;
  }
  p {
    font-family: var(--subtitleFont);
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.7);
    margin: 0px;
  }
}
.videoTags {
  font-family: var(--titleFont);

  font-size: 17.5px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.16;
  letter-spacing: normal;
  text-align: left;
  color: white;
}

.preview {
  display: flex;
  width: 600px;
  height: 920px;
  border: 1px solid black;
  border-radius: 5px;
  background-image: #343841;
  font-size: 10px;
  display: flex;
  flex-direction: column;
  border: 1px solid black;
  border-radius: 5px;
  background-image: linear-gradient(157deg, #0f0f14 -57%, #393e47 69%);
  overflow: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-right: 16px;
  padding-left: 16px;
  padding-top: 24px;
  padding-bottom: 24px;
  .staticTitle {
    text-align: center;
    width: 100%;
    h1 {
      color: white;
      font-family: var(--titleFont);
      font-size: 17.5px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.16;
      letter-spacing: normal;
    }
    p {
      font-family: var(--subtitleFont);
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
    }
  }
}


.mobilepreview {
  display: flex;
  border: 1px solid black;
  border-radius: 5px;
  background-image: #343841;
  font-size: 10px;
  display: flex;
  flex-direction: column;
  width: 375px;
  height: 790px;
  border: 1px solid black;
  border-radius: 5px;
  background-image: linear-gradient(157deg, #0f0f14 -57%, #393e47 69%);
  overflow: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-right: 16px;
  padding-left: 16px;
  padding-top: 24px;
  padding-bottom: 24px;
  margin-bottom: 10px;

  scrollbar-width: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #707378;
    border-radius: 40px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 8px;
    margin-bottom: 5px;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}
.slider {
  display: flex;
  flex-direction: row;
  gap: 12px;
  overflow-x: auto;
  height: 100%;
  padding-top: 12px;
  padding-bottom: 12px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #707378;
    border-radius: 40px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 8px;
    margin-bottom: 5px;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}

.staticTitle {
  color: white;

  h1 {
    color: white;
    font-family: var(--titleFont);
    font-size: 17.5px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.16;
    letter-spacing: normal;
    margin: 0px;
  }
  p {
    font-family: var(--subtitleFont);
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.7);
  }
}

.ContentCarouselImage {
  width: 140px;
  height: 120px;
  border-radius: 4px;
  object-fit: fill;
}

.ContentCarousel {
  p {
    font-family: var(--subtitleFont);
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: normal;
    color: white;
    margin-bottom: 1px;
  }
}

.videoIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  color: white;
  pointer-events: none;
    border-radius: 50%;
    box-shadow: inset -0.4px 0.4px 0.4px -0.9px rgba(255, 255, 255, 0.35);
    border-style: solid;
    border-width: 0.6px;
    border-image-source: linear-gradient(202deg, #fff 24%, rgba(255, 255, 255, 0) 15%);
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.25)), linear-gradient(202deg, rgb(255 255 255 / 44%) 92%, rgba(255, 255, 255, 0) 15%);
    background-origin: border-box;
    background-clip: content-box, border-box;
    g{
      display: none;
    }
}

.imageContainer {
  position: relative;
  display: inline-block;
  cursor: pointer;
  border-radius: 4px;
  max-width: 140px;
  max-height: 120px;
  height: 120px;
  width: 140px;
}

.imageSafeContainer{
  position: relative;
  display: inline-block;
  cursor: pointer;
  border-radius: 4px;
  max-width: 198px;
  max-height: 138px;
  height: 138px;
  width: 198px;
}

.imageSafeContainer img{
  max-width: 198px;
  max-height: 138px;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.imageContainer img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: fill;
}
.views {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: center;

  img {
    width: 19px;
    height: 15px;
    padding: 1px 4.5px 1px 4.5px;
    align-self: center;
  }

  p {
    font-family: var(--subtitleFont);
    font-size: 10px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    align-self: center;
    text-align: center;
    margin: 0px;
    color: rgba(255, 255, 255, 0.7);
    margin-left: 3px;
  }
}

.videoRender {
  width: 100%;
  height: 174px;
}

.desktop-preview {
  width: 600px;
  height: 780px;

  .staticTitle {
    text-align: center;
    width: 100%;
    h1 {
      color: white;
      font-family: var(--titleFont);
      font-size: 17.5px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.16;
      letter-spacing: normal;
    }
    p {
      font-family: var(--subtitleFont);
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
    }
  }
}

.loader {
  width: 100%;
  height: 80vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-y: hidden;
}

.previewBtnf {
  width: 70px;
  height: 38px;
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;
  position: absolute;
  right: 297px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    position: unset;
    margin-top: 10px;
  }
}

.previewBtnUploadVideo {
  border: 0px;
  width: 70px;
  height: 38px;
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  margin-top: 4px;
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.safePreview {
  max-width: 90%;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border: 1px solid black;
}

.SafeContentCarousel {
  margin-bottom: 10px;
  p {
    font-family: var(--subtitleFont);
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: normal;
    color: black;
    margin: 0;
  }
}

.safeslider {
  display: flex;
  flex-direction: row;
  gap: 12px;
  overflow-x: auto;


  &::-webkit-scrollbar {
    width: 8px;
    height: 9px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dbdcde;
    border-radius: 40px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 8px;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: -webkit-sticky;
  position: sticky;
  z-index: 999;
  top: 0;
  backdrop-filter: blur(10px);
  background-color: hsla(0, 0%, 100%, 0.8);
  padding: 16px 30px;

  img {
    width: 160px;
  }
}

.videoPlayer {
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.3);
  border: 4px solid #fff;
  background-color: #000;
  margin: 24px 0 40px;
  width: 566px;
  height: 174px;
  align-content: center;
}

.pageVideoTitle {
  font-family: var(--titleFont);
  font-size: 17.5px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.16;
  letter-spacing: normal;
  text-align: center;
  color: #0f0f14;
}

.mainSection {
  padding: 5px 50px;
}

.scrollSafeContent {
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  margin-top: 20px;
  padding-right: 21px;
}
.scrollSafeContent:nth-child(odd)::after {
  content: "";
  position: absolute;
  background: #dbdcde;
  width: 1px;
  height: 97%;
  right: 0;
  bottom: 0px;
}
.scrollSafeContent:last-child::after {
  content: none; 
}

.safepreivewContent {
  display: flex;
  align-items: center;
  color: white;
  column-gap: 10px;

  h1 {
    font-family: var(--titleFont);
    font-size: 17.5px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.16;
    letter-spacing: normal;
    text-align: left;
    color: black;
    margin: 0px;
  }

  p {
    font-family: var(--subtitleFont);
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #71737f;
  }
}

#safeVideo {
  height: 174px;
}

.safeModalContent {
  width: 60%;
  max-width: 600px;
  padding: 46px 24px 24px;
  object-fit: contain;
  border-radius: 12px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: hsla(0,0%,100%,.8);
  box-shadow: 0 0 50px 0 rgba(154, 157, 161, 0.35);
  background-color: hsla(0,0%,100%,.8);
  backdrop-filter: blur(10px);
  margin: 12px;
  overflow: auto;
  position: relative;

  p {
    font-family: var(--titleFont);
    font-size: 17.5px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.16;
    letter-spacing: normal;
    text-align: left;
    color: black;
    margin-bottom: 0px;
    text-transform: uppercase;
  }
  .showmorebtn{
    font-family: var(--subtitleFont);
    font-size: 12.5px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.16;
    letter-spacing: normal;
    text-align: left;
    color: #16b9ff;
    margin-bottom: 0px;
  }
}

.safeContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.closeSafeButton {
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
}

.afterIntroContentSafe{
    display: grid;
    grid-template-columns: 100%;
    .afterScrollSafeContent{
      display: flex;
      justify-content: center;
      margin: 30px 0px 0px 0px;
      .safeslider{
        gap:24px;
        .imageSafeContainer{
          border-radius: 12px;
          max-width: 300px;
          max-height: 210px;
          width: 300px;
          height: 210px;
        }
        
        .imageSafeContainer img{
          max-width: 300px;
          max-height: 210px;
          display: block;
          width: 100%;
          height: 100%;
          object-fit: fill;
        }
        p {
          font-family: Inter;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
          text-align: center;
          color: #0f0f14;
          margin: 0px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
        }
      }
    }
}

.SliderContentSafe {
    display: grid;
    grid-template-columns: 50% 50%;
    gap: 21px;
    row-gap: 10px;
    margin-bottom: 40px;
}

.scrollSafeContainer {
  display: flex;
  flex-direction: column;
  row-gap: 30px;
}

.nowPlayingOverlay{
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  background-color: rgba(255, 255, 255, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border: 1px solid white;
  border-radius: 4px;

  h1{
    font-family: var(--titleFont);
    font-size: 12px;
    text-align: center;
    margin: 0px;
  }
}

.currentVideoTitle{
  color: white;
  padding-top: 20px;
  padding-bottom: 15px;

  h1{
    font-family: var(--titleFont);
    margin: 0px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.16;
    letter-spacing: normal;
    text-align: left;
    color: white;
    text-transform: uppercase;
    margin: 0px;
  }

  h3{
    font-family: var(--subtitleFont);
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
  }
  .showMoreLessBtn{
    background-color: transparent;
    cursor: pointer;  
    padding: 0;      
    margin: 0;  
    background: transparent;
    border: none;    
  }

  .descriptionVideo{
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    text-align: left;
    color: #fff;
    margin-top: 4px;
    button{
        font-family: Inter;
        font-size: 14px;
        color: #16b9ff;
        margin-left: 3px;
    }
}

  .showmorebtn{
    color: white;
    margin: 2px;
    cursor: pointer;
    font-family: var(--subtitleFont);
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #16b9ff;
    margin-left: 3px;
  }

  p{
    margin-bottom: 0px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  font-family: var(--subtitleFont);
  }

}

.showmorebtn{
  color: black;
    margin: 2px;
    cursor: pointer;
    font-size: 12px;
    font-family: var(--subtitleFont);
}

.eyeCurrentTitleIcon{
  width: 24px;
  height: 24px;
  font-family: var(--subtitleFont);
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.safeDescription{
  font-family: var(--subtitleFont);
  font-size: 14px;
  font-weight: 400;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #0f0f14;
}

.thumbnailDiv{
  max-width: 2000px;
  display: flex;
  justify-content: center;
  height: 400px;
  position: relative;
  padding-top: 34.35%;
  width: 100%;
  box-shadow: 0 8px 20px 0 rgba(0, 0, 0, .3);
  border: 4px solid #fff;
  background-color: #000;
  border-radius: 12px;
  overflow: hidden;
  margin: 24px 0 40px;
}

.safethumbnailDiv{
  display: flex;
  justify-content: center;
  background-color: black;
  position: relative;
  max-width: 552px;
  max-height: 332px;
  

  img{
    min-width: 552px;
    min-height: 332px;
    object-fit: fill;
  }
}

.safeThumbnaill.safeThumbnaill{
  height: 100%;
  max-height: 100%;
  width: 100%;
  object-fit: contain;
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
}

.previewThumbnail{
  max-width: 600px;
  max-height: 300px;
  object-fit: fill;
}

.ContentCarouselImage{
  &:hover {
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25);
    border: solid 1px #16b9ff;
  }
}
.videoIconThumbnail{
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 48px;
  color: white;
  pointer-events: none;
  width: 80px;
  height: 80px;
  cursor: pointer;
  transform: translate(-50%, -50%);
}

.safeBorder{
  border-radius: 12px;
  box-shadow: 0 8px 20px 0 rgba(0, 0, 0, .3);
  border: 4px solid #fff;
  background: black;
  overflow: hidden;
}


  .episodesThumbnailSection {
    overflow: auto;
    max-height: 900px;
    padding: 0px 12px 0px 0px;
  
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
  
    &::-webkit-scrollbar-thumb {
        background-color: rgba(217, 217, 217, 0.37);
        border-radius: 50px;
    }
  
    &::-webkit-scrollbar-track {
        background: transparent;
    }
  }
  

.episodesThumbnailSectionMobile{
  max-height: 600px;
  padding: 0px 12px 0px 0px;

  &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
  }

  &::-webkit-scrollbar-thumb {
      background-color: rgba(217, 217, 217, 0.37);
      border-radius: 50px;
  }

  &::-webkit-scrollbar-track {
      background: transparent;
  }
}


.share_video_url_div{
  display: flex;
  align-items: center;
  min-width: 200px;
  white-space: break-spaces;
  a{
    margin-right: 20px;
  }
}


.copyBtn {
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;
  padding: 5px;
}
.tickMark {
  width: 5% !important;
}


.uploadThumbnailSection {
  display: flex;
  column-gap: 12px;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  padding: 12px 0px;
  flex-wrap: wrap;

  span {
    font-size: 20px;
    font-weight: 600;
  }

  .uploadVideo {
    flex-grow: 1;
    max-width: 50%;
  }

  .uploadHeading {
    font-size: 14px;
    font-weight: bold;
    line-height: 1.6;
    text-align: left;
    color: #0f0f14;
    text-transform: uppercase;
  }

  .uploadLabel {
    font-size: 14px;
    font-weight: bold;
    line-height: 1.6;
    color: #393e47;
    text-transform: uppercase;
    margin-top: 12px;

    @media (max-width: 767px) {
      font-size: 12px;
    }

    input {
      outline: none;
      padding-left: 8px;
      height: 30px;
      width: 100%;
      border-radius: 4px;
      border: solid 1px #9b9eac;
      background-color: #fff;
      height: 40px;
      font-size: 14px;
      font-weight: bold;
      line-height: 1.4;
      text-align: left;
      color: #0f0f14;

      &:focus {
        font-weight: normal;
      }
    }
  }

  .uploadBox {
    height: 300px;
    margin-top: 8px;
    border-radius: 4px;
    border: dashed 1px #1c40e7;
    background-color: #e9f3ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .uploadText1 {
      font-size: 12px;
      line-height: 1.6;
      text-align: center;
      color: #71737f;
      margin-top: 0px;
    }

    input {
      display: none;
    }
  }

  .continuePopBtn {
    .continueBtn {
      padding: 8px 30px;
      border-radius: 6px;
      background-color: #16b9ff;
      font-size: 14px;
      font-weight: bold;
      line-height: 1.6;
      text-align: center;
      color: #fff;
      text-transform: uppercase;
      width: 100%;
      cursor: pointer;
      border: 0px;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      @media (max-width: 767px) {
        padding: 8px 20px;
        font-size: 10px;
      }
    }
  }

  .captureThumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    span {
      font-size: 16px;
      line-height: 1.6;
      text-align: center;
      color: #71737f;
      margin-top: 12px;
      display: block;
    }
    button {
      padding: 6px 12px;
      font-size: 16px;
      cursor: pointer;
      color: var(--primaryColor);
      border-radius: 6px;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

.thumbnailMain{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100% - 97px);
  margin-top: 8px;
}

.mediaUploadErrorMsg {
  color: red;
}

.confirmationPopup {
  h2 {
      display: none;
  }
  .continuePopup {
      padding: 20px;
      text-align: center;
      width: 300px;
      @media screen and (max-width: 768px) and (min-width: 320px) {
          width: 240px;
      }
      .continuetext {
          text-align: center;
          font-size: 20px;
          margin-bottom: 24px;
          color: var(--primaryColor);
      }
      .yesAndnoBtn {
          display: flex;
          gap: 10px;
          .okBtn {
              width: 100%;
              height: 45px;
              border-radius: 6px;
              text-decoration: none;
              gap: 8px;
              border: none;
              font-size: 16px;
              font-weight: 500;
              cursor: pointer;
              background-color: var(--primaryColor);
              color: #fff;
          }
      }
  }
}

.largeVideoCheckbox.largeVideoCheckbox{
  margin: 12px 0px;
  .inputField{
    align-items: center;
    font-size: 16px;
    .lblInput{
      max-width: fit-content;
      margin-right: 6px;
     
    }
    .checkbox{
      width: 20px;
      height: 15px;
      margin: 0px;
      margin-right: 10px;
    }
    .noteLargeVideo{
      font-size: 13px;
      font-weight: 500;
    }
  }
}

.addSubTitle{
  margin-top: 12px;
  .captionTitle{
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--primaryColor);
    span{
      font-size: 14px;
      margin-left: 6px;
      display: inline-block;
    }
  }

  .uploadFileMain{
    display: flex;
    align-items: center;
    font-family: Noto Sans;

    .closeIcon1{
      display: flex;
      margin-left: 8px;
      cursor: pointer;
      svg{
        path{
          fill: #ff0000;
        }
      }
    }
    .captionFileName{
      font-size: 16px;
      color: var(--primaryColor);
    }
  }

  .uploadContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .fileInput {
    display: none;
  }

  .uploadLabel {
    display: inline-block;
    padding: 8px 20px;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    background-color: var(--primaryColor);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
  }
}
.shareVideoIcon{
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  line-height: normal;
  text-align: left;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  svg{
      margin-right: 4px;
  }
}

.safeshareVideoIcon{
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  text-align: left;
  color: #0f0f14;
  display: flex;
  align-items: center;
  svg{
      margin-right: 4px;
  }
}
.viewsDiv{
  display: flex;
  gap: 16px;
  margin-top: 4px;
}