import { useImmer } from "use-immer";
import Loader from "../../components/common/Loader";
import useGetSafeUploads from "../../hooks/useGetSafeUploads";
import { useContext, useEffect, useRef, useState } from "react";
import ReactPaginate from "react-paginate";
import useSaveHideCarouselImage from "../../hooks/useSaveHideCarouselImage";
import { CommonCtx } from "../AppContainer";
import { Select, MenuItem, Tooltip } from "@mui/material";
import styles from "./SafeUploads.module.scss";
import { convertUtcToCtTimeUsingDayjs, filterArrray } from "../../utils/helper";
import MatPopup from "../../components/common/MatPopup";
import useGetSafeUploadComments from "../../hooks/useGetSafeUploadComments";
import useUpdateSafeUploadComments from "../../hooks/useUpdateSafeUploadComments";
import clsx from "clsx";
import { inputRegex } from "../../utils/constant";
import VideoPlayer from "../../components/VideoPlayer";
import { ReactComponent as LikeFilledIcon } from '../../../assests/images/ThumbUpFilled.svg';
import { ReactComponent as HeartFilledIcon } from '../../../assests/images/HeartFilled.svg';
import { cloneDeep } from "lodash";

const SafeUploads = () => {
    const [itemOffset, setItemOffset] = useState(0);
    const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(0);
    const [safeUploads, setSafeUploads] = useImmer<any[]>([]);
    const [filteredSafeUploads, steFilteredSafeUploads] = useImmer<any[]>([]);
    const [inputSearchValue, setInputSearchValue] = useState("");
    const [showSaveBtn, setShowSaveBtn] = useState(false);
    const [showCommentsPopup, setShowCommentsPopup] = useState(false);
    const [commentList, setCommentList] = useImmer<any[]>([]);
    const [disablePopupSaveBtn, setDisablePopupSaveBtn] = useState(true);
    const [errorTooltip, setErrorTooltip] = useState<any>({});
    const nameInputRef = useRef<any>({});
    const [popupErrorTooltip, setPopupErrorTooltip] = useState<any>({});
    const popupInputRef = useRef<any>({});
    const videoRef = useRef(null);
    
    
    const showPopupFormAnyComponent = useContext(CommonCtx);

    const endOffset = itemOffset + itemsPerPage;
    const pageCount = Math.ceil(filteredSafeUploads.length / itemsPerPage);

    const {
        data: safeUploadsData,
        isLoading: isSafeUploadsLoading,
        isFetching: isSafeUploadsFetching,
    } = useGetSafeUploads();

    const {
        mutate: saveHideCarousel,
        data: saveHideCarouselImageData,
        isLoading: isSaveHideCarouselImageLoading,
    } = useSaveHideCarouselImage();

    const {
        mutate: updateSafeUploadComments,
        data: updateSafeUploadCommentsData,
        isLoading: isupdateSafeUploadCommentsLoading,
    } = useUpdateSafeUploadComments();

    const {
        mutate: getSafeUploadComments,
        data: getSafeUploadCommentsData,
        isLoading: isGetSafeUploadCommentsLoading,
    } = useGetSafeUploadComments();

    useEffect(() => {
        const _safeUploadsData = safeUploadsData?.length ? safeUploadsData : [];
        setSafeUploads(_safeUploadsData);
        if (_safeUploadsData?.length > 0 && inputSearchValue.length !== 0) {
            search(inputSearchValue, _safeUploadsData)
        }else{
            steFilteredSafeUploads(_safeUploadsData);
        }
        clear();
    }, [safeUploadsData, isSafeUploadsFetching, isSafeUploadsLoading]);

    useEffect(() => {
        if (!isSaveHideCarouselImageLoading) {
            if (saveHideCarouselImageData) {
                showPopupFormAnyComponent(saveHideCarouselImageData);
                setErrorTooltip({})
            }
        }
    }, [
        saveHideCarouselImageData,
        saveHideCarouselImageData,
        isSaveHideCarouselImageLoading,
    ]);

    useEffect(() => {
        if (!isupdateSafeUploadCommentsLoading) {
            if (updateSafeUploadCommentsData) {
                showPopupFormAnyComponent(updateSafeUploadCommentsData);
            }
        }
    }, [
        updateSafeUploadCommentsData,
        isupdateSafeUploadCommentsLoading,
    ]);

    useEffect(() => {
        if (getSafeUploadCommentsData) {
            setCommentList(getSafeUploadCommentsData);
            setDisablePopupSaveBtn(true);
            setShowCommentsPopup(true);
        } else {
            setCommentList([]);
        }
    }, [getSafeUploadCommentsData]);

    const clear = () => {
        setShowSaveBtn(false);
        setDisablePopupSaveBtn(true);
        setCurrentPage(0);
        setItemOffset(0);
    };

    const handleHideOnCarousel = (e: any, i: number, data: any) => {
        setShowSaveBtn(true);
        const isChecked = e.target.checked;
        steFilteredSafeUploads((prev) => {
            prev[i].edit_show_on_carousel = isChecked;
        });
        const spreadSafeUploadsData = cloneDeep(safeUploads);
        const findSafeUploadRow = spreadSafeUploadsData.find((uploadData) => uploadData.id === data.id);
        if(findSafeUploadRow){
            findSafeUploadRow.edit_show_on_carousel = isChecked;
        }
        setSafeUploads(spreadSafeUploadsData)
    };

    const saveHideCarouselImage = () => {
        const dataArray: any[] = [];
        const errorTooltipData: any = {};
        let enableErrorTooltip = false;
        safeUploads.forEach((safeUpload, i) => {
            if (
                safeUpload?.edit_show_on_carousel === false ||
                safeUpload?.edit_show_on_carousel === true
            ) {
                
                if(safeUpload.company_name.trim().length === 0){
                    errorTooltipData['companyName'+safeUpload.id] = handleErrorMessage('Company name', safeUpload.company_name);
                    enableErrorTooltip = true;
                    nameInputRef.current['companyName'+safeUpload.id].focus();
                }
                dataArray.push({
                    id: safeUpload.id,
                    value: safeUpload.edit_show_on_carousel,
                    caption:
                        safeUpload?.caption?.length > 0
                            ? safeUpload.caption
                            : null,
                    company_name:
                        safeUpload?.company_name?.length > 0
                            ? safeUpload.company_name
                            : null,
                    user_name:
                        safeUpload?.user_name?.length > 0
                            ? safeUpload.user_name
                            : null,
                });
            }
        });

        if (dataArray.length > 0 && !enableErrorTooltip) {
            saveHideCarousel({ data: dataArray });
        }else{
            setErrorTooltip(errorTooltipData);
            setShowSaveBtn(false);
        }
    };

    const handleErrorMessage = (key: string, data: string) =>{
        let errorMessage = key + ' is required.';
        return errorMessage;
    }

    const handlePageClick = (event: any) => {
        const newOffset = (event.selected * itemsPerPage) % safeUploads.length;
        setCurrentPage(event.selected);
        setItemOffset(newOffset);
    };

    const getPreview = (fileObj:any)=>{//(fileUrl: string, fileName: string) => {
        if (fileObj.safepage_url_s3) {
            const extension = isVideo(fileObj.safepage_url_s3);
            return extension ? (
                <div className={clsx(styles.videoThumbBox,'videoThumbBox')}>
                   <VideoPlayer
                    url={fileObj.safepage_url_s3}
                    width={"100%"}
                    height={"100%"}
                    videoRef={videoRef}
                />
                </div>
                
            ) : (
                <img src={udpateURL(fileObj.safepage_url_s3)} alt={fileObj.actual_filename} height="50" />
            );
        } else {
            return <></>;
        }
    };

    function isVideo(fileUrl: string) {
        const videoExtensions = [".mp4", ".mov", ".wmv", ".avi", ".flv"];

        const extension = fileUrl.substring(fileUrl.lastIndexOf(".")).toLowerCase();
        return videoExtensions.includes(extension)
            ? extension.replace(".", "")
            : null;
    }

    function udpateURL(imgUrl:string): string | undefined {
            return import.meta.env.VITE_SAFE_UPLOADS_IMGIX_PREFIX + imgUrl.split(".com")[1]+import.meta.env.VITE_SAFE_UPLOADS_IMGIX_SUFFIX;
    }

    const search = (searchString: string, safeUploadsData: any = safeUploads) => {
        setCurrentPage(0);
        setItemOffset(0);
        setInputSearchValue(searchString);
        if (searchString) {
            const _filterArrray = filterArrray(safeUploadsData, searchString, [
                "company_name",
                "user_name",
                "actual_filename",
                "safepage_url_s3",
                "created_date",
            ]);
            if (_filterArrray?.length) {
                steFilteredSafeUploads(_filterArrray);
            } else {
                steFilteredSafeUploads([]);
            }
        } else {
            steFilteredSafeUploads(safeUploadsData);
        }
    };


    const setInputText = (inputEvent: any, key: string, index: number, data: any) => {
        setShowSaveBtn(true);
        const errorTooltipData = errorTooltip;
        const spreadSafeUploadsData = cloneDeep(safeUploads);
        steFilteredSafeUploads((prev: any) => {
            const safeUpload = prev[index];
            safeUpload[key] = inputEvent.target.value;
            if(key === "company_name"){
                errorTooltipData['companyName'+safeUpload.id] = null;
            }
            safeUpload.edit_show_on_carousel = !!(safeUpload.edit_show_on_carousel ===
                undefined
                ? safeUpload.show_on_carousel
                : safeUpload.edit_show_on_carousel);
            return prev;
        });
        const findSafeUploadRow = spreadSafeUploadsData.find((uploadData) => uploadData.id === data.id);
        if(findSafeUploadRow){
            findSafeUploadRow[key] = inputEvent.target.value;
            if(key === "company_name"){
                errorTooltipData['companyName'+findSafeUploadRow.id] = null;
            }
            findSafeUploadRow.edit_show_on_carousel = !!(findSafeUploadRow.edit_show_on_carousel ===
                undefined
                ? findSafeUploadRow.show_on_carousel
                : findSafeUploadRow.edit_show_on_carousel);
        }
        setSafeUploads(spreadSafeUploadsData)
        
        setErrorTooltip(errorTooltipData);
    };

    const setPoppupInputText = (inputEvent: any, key: string, index: number) => {
        setDisablePopupSaveBtn(false);
        const popupErrorTooltipData = popupErrorTooltip;
        setCommentList((prev: any) => {
            const commentData = prev[index];
            commentData[key] = inputEvent.target.value;
            if(key === 'user_name') popupErrorTooltipData['userName'+commentData.id] = null;
            if(key === 'comment') popupErrorTooltipData['comment'+commentData.id] = null;
            commentData.edit_show_on_comments = !!(commentData.edit_show_on_comments === undefined
                ? commentData.show_comment
                : commentData.edit_show_on_comments);
            return prev;
        });
        setPopupErrorTooltip(popupErrorTooltipData)
    };

    const confirmationPopupClose = () => {
        setShowCommentsPopup(false);
        setDisablePopupSaveBtn(true);
        setPopupErrorTooltip({})
      };

    const showCommentsPoppup = (id: string) => {
        getSafeUploadComments({
            id: id
          })
    }

    const submitCommentsPopup = () => {
        const dataArray: any[] = [];
        const popupErrorData: any = {}
        let enableErrorTooltip = false;
        commentList.forEach((commentData) => {
            popupErrorData[commentData.id] = null;
            if (
                commentData.edit_show_on_comments === false ||
                commentData.edit_show_on_comments === true
            ) {
                if(commentData.user_name.trim().length === 0 || !inputRegex.test(commentData.user_name) ){
                    popupErrorData['userName'+commentData.id] = handleErrorMessage('Name', commentData.user_name);
                    enableErrorTooltip = true;
                    popupInputRef.current['userName'+commentData.id].focus();
                }
                if(!commentData.comment || commentData.comment?.trim().length === 0){
                    popupErrorData['comment'+commentData.id] = 'Comment message is required.';
                    enableErrorTooltip = true;
                    popupInputRef.current['comment'+commentData.id].focus();
                }
                dataArray.push({
                    comment_id: commentData.id,
                    show_comment: commentData.edit_show_on_comments,
                    user_name:
                        commentData?.user_name?.length > 0
                            ? commentData.user_name
                            : null,
                    comment:
                        commentData?.comment?.length > 0
                            ? commentData.comment
                            : null,
                });
            }
        });
        if (dataArray.length > 0 && !enableErrorTooltip) {
            updateSafeUploadComments({data: dataArray});
            setShowCommentsPopup(false);
        }else{
            setPopupErrorTooltip(popupErrorData);
            setDisablePopupSaveBtn(true);
        }
    }

    const commentLineChangeHandler = (event: any, i: number) => {
        setDisablePopupSaveBtn(false);
        setCommentList((prev: any) => {
          prev[i].edit_show_on_comments = event.target.checked;
          return prev;
        });
    };

    return (
        <div>
            {isSafeUploadsLoading ||
                isSafeUploadsFetching ||
                isSaveHideCarouselImageLoading ||
                isupdateSafeUploadCommentsLoading ||
                isGetSafeUploadCommentsLoading ? (
                <div className={styles.loaderImg}>
                    <Loader />
                </div>
            ) : (
                <div>
                    <p className={styles.noteText}>
                        NOTE - The feed on the safe page will showcase the most recent 20 posts where &apos;Show on Feed&apos; is selected.
                    </p>
                    <div className={styles.searchBox}>
                        <Select
                            className={styles.showdropdwn}
                            value={itemsPerPage}
                            onChange={(event) => {
                                setItemsPerPage(+event.target.value);
                            }}
                        >
                            {perPageEntriesOptions.map((item, index) => (
                                <MenuItem key={index} value={item}>
                                    <span>{item}</span>
                                </MenuItem>
                            ))}
                        </Select>
                        <>
                            {showSaveBtn && (
                                <button
                                    className={styles.saveBtnf}
                                    onClick={saveHideCarouselImage}
                                >
                                    Save
                                </button>
                            )}
                            <input
                                className={styles.searchInput}
                                type="text"
                                onChange={(event) => search(event.target.value)}
                                value={inputSearchValue}
                                placeholder="Search"
                            />
                        </>
                    </div>

                    <div className={styles.tblscroll}>
                        <table>
                            <thead>
                                <tr>
                                    <th>Show on <br/>Feed</th>
                                    <th>Actual Filename</th>
                                    <th className="hidden">Comments</th>
                                    <th>Download images</th>
                                    <th>Created Date</th>
                                    <th>Preview </th>
                                    <th>Reactions </th>
                                    <th className="hidden">Caption</th>
                                    <th>Company Name</th>
                                    <th>Name</th>
                                </tr>
                            </thead>
                                <tbody>
                                {filteredSafeUploads?.length > 0 ? (
                                    filteredSafeUploads
                                        .map((safeUpload: any, i) => (
                                            <tr key={safeUpload.id}>
                                                <td>
                                                    <input
                                                        type="checkbox"
                                                        checked={
                                                            !!(safeUpload.edit_show_on_carousel === undefined
                                                                ? safeUpload.show_on_carousel
                                                                : safeUpload.edit_show_on_carousel)
                                                        }
                                                        onChange={(e) => handleHideOnCarousel(e, i, safeUpload)}
                                                    />
                                                </td>
                                                <td>{safeUpload.actual_filename}</td>
                                                <td className="hidden">
                                                    <button
                                                        className={styles.showCommentsBtn}
                                                        onClick={() => {
                                                            showCommentsPoppup(safeUpload.id);
                                                        }}
                                                    >
                                                        Show Comments
                                                    </button>
                                                </td>
                                                <td>
                                                    <a href={safeUpload.safepage_url_s3}>
                                                    <button className={styles.showCommentsBtn}>
                                                        Download
                                                    </button>
                                                    </a>
                                                </td>
                                                <td>{convertUtcToCtTimeUsingDayjs(safeUpload.created_date)}</td>
                                                <td >
                                                    {getPreview(safeUpload)}
                                                </td>
                                                <td >
                                                    {<div className={styles.reactionDiv}>
                                                        <span className={styles.reactionSpan}>
                                                            <span className={styles.reactionIcon}> <LikeFilledIcon /> </span>
                                                            <span className={styles.reactionText}>{safeUpload.thumbs_up_count}</span>
                                                        </span>
                                                        <span className={styles.reactionSpan}>
                                                            <span className={styles.reactionIcon}><HeartFilledIcon /></span>
                                                            <span className={styles.reactionText}>{safeUpload.heart_count}</span>
                                                        </span>
                                                    </div>}
                                                </td>
                                                <td className="hidden">
                                                    <input
                                                        className={styles.messageText}
                                                        type="text"
                                                        value={safeUpload.caption ?? ""}
                                                        onChange={(e) => setInputText(e, "caption", i, safeUpload)}
                                                    />
                                                </td>
                                                <td>
                                                    <Tooltip
                                                        title={errorTooltip['companyName'+ safeUpload.id]}
                                                        placement="top-end"
                                                        classes={{
                                                            popper: styles.errorStyle,
                                                            tooltip: styles.tooltip,
                                                        }}
                                                    >
                                                        <input
                                                            className={styles.messageText}
                                                            type="text"
                                                            value={safeUpload.company_name ?? ""}
                                                            onChange={(e) =>
                                                                setInputText(e, "company_name", i, safeUpload)
                                                            }
                                                            ref={(ref)=> nameInputRef.current['companyName'+safeUpload.id] = ref}
                                                        />
                                                    </Tooltip>
                                                </td>
                                                <td>
                                                    <Tooltip
                                                        title={errorTooltip['name'+safeUpload.id]}
                                                        placement="top-end"
                                                        classes={{
                                                            popper: styles.errorStyle,
                                                            tooltip: styles.tooltip,
                                                        }}
                                                    >
                                                        <input
                                                            className={styles.messageText}
                                                            type="text"
                                                            value={safeUpload.user_name ?? ""}
                                                            onChange={(e) =>
                                                                setInputText(e, "user_name", i, safeUpload)
                                                            }
                                                            ref={(ref)=>nameInputRef.current['name'+safeUpload.id] = ref}
                                                        />
                                                    </Tooltip>
                                                </td>
                                            </tr>
                                        ))
                                        .slice(itemOffset, endOffset)
                                    ) : (
                                        <tr>
                                            <td colSpan={7} className={"noDataFoundTd"}>
                                                No data found
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            
                        </table>
                    </div>
                    <div className="PaginationNumber">
                        <div className="saveBtn">
                            <ReactPaginate
                                breakLabel="..."
                                nextLabel=">"
                                onPageChange={handlePageClick}
                                pageRangeDisplayed={5}
                                pageCount={pageCount}
                                previousLabel="<"
                                renderOnZeroPageCount={(props) =>
                                    props.pageCount > 0 ? undefined : null
                                }
                                forcePage={pageCount > 0 ? currentPage : -1}
                            />

                            {showSaveBtn && (
                                <button
                                    className={styles.saveButton}
                                    onClick={saveHideCarouselImage}
                                >
                                    Save
                                </button>
                            )}
                        </div>
                    </div>
                        <MatPopup
                            className={clsx(styles.showCommentPopup, isGetSafeUploadCommentsLoading ? styles.showCommentPopupLoader : '')}
                            open={showCommentsPopup}
                            classes={{
                                paper: clsx(styles.commentListPopup,'commentListPopup' )
                            }}
                        >

                            <div className={styles.tblscrollPop}>
                                <div className={styles.continuePopup}>
                                    <p className={styles.continuetext}>Comment List</p>

                                    <div className={styles.tblscroll}>
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th> Show On Comment List </th>
                                                    <th>Name</th>
                                                    <th>Comment Message</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {commentList.map((commentData: any, i: number) => {
                                                    return (
                                                        <tr key={commentData.id}>
                                                            <td>
                                                                <input type="checkbox"
                                                                    checked={
                                                                        !!(commentData.edit_show_on_comments === undefined
                                                                            ? commentData.show_comment
                                                                            : commentData.edit_show_on_comments)
                                                                    }
                                                                    onChange={(e) => commentLineChangeHandler(e, i)}
                                                                />
                                                            </td>
                                                            <td>
                                                                <Tooltip
                                                                    title={popupErrorTooltip['userName'+commentData.id]}
                                                                    placement="top-end"
                                                                    classes={{
                                                                        popper: styles.errorStyle,
                                                                        tooltip: styles.tooltip,
                                                                    }}
                                                                >
                                                                    <input
                                                                        type="text"
                                                                        onChange={(e) => setPoppupInputText(e, "user_name", i)}
                                                                        value={commentData.user_name ?? ''}
                                                                        ref={(ref)=> popupInputRef.current['userName'+commentData.id] = ref}
                                                                    />
                                                                </Tooltip>
                                                            </td>
                                                            <td>
                                                                <Tooltip
                                                                    title={popupErrorTooltip['comment' + commentData.id]}
                                                                    placement="top-end"
                                                                    classes={{
                                                                        popper: styles.errorStyle,
                                                                        tooltip: styles.tooltip,
                                                                    }}
                                                                >
                                                                    <input
                                                                        type="text"
                                                                        onChange={(e) => setPoppupInputText(e, "comment", i)}
                                                                        value={commentData.comment ?? ''}
                                                                        ref={(ref) => popupInputRef.current['comment'+commentData.id] = ref}
                                                                    />
                                                                </Tooltip>
                                                            </td>
                                                        </tr>
                                                    )
                                                })}
                                            </tbody>
                                        </table>
                                    </div>
                                    <div className={styles.yesAndnoBtn}>
                                        <button className={styles.okBtn} onClick={submitCommentsPopup} disabled={disablePopupSaveBtn}>
                                            Save
                                        </button>
                                        <button
                                            className={styles.okBtn}
                                            onClick={confirmationPopupClose}
                                        >
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>

                        </MatPopup>
                </div>
            )}
        </div>
    );
};

export default SafeUploads;
