import * as yup from "yup";

export const makeApaymentSchema = yup.object().shape({
    selectedPoObj: yup.object().nullable().default(null),
    selectedPoNumber: yup.string().nullable().default(null),
    selectedPaymentMethod: yup.string().nullable().default(null),
    sellerInvoiceNumber: yup.string().nullable().default(null),
    sellerInvoiceNumberBH: yup.string().nullable().default(null),
    sellerInvoiceNumberST: yup.string().nullable().default(null),
    sellerInvoiceNumberAH: yup.string().nullable().default(null),
    sellerInvoiceNumberBNPL: yup.string().nullable().default(null),

    sellerPayoutAmount: yup.number().nullable().default(null),
    sellerPayoutStatementDescriptor: yup.string().nullable().default(null),
    sellerPayoutInternalNote: yup.string().nullable().default(null),
    sellerPayoutEmailId: yup.string().nullable().trim().default(null),

    bryzosHoldingsAmount: yup.number().nullable().default(null),
    bryzosHoldingsStatementDescriptor: yup.string().nullable().default(null),
    bryzosHoldingsInternalNote: yup.string().nullable().default(null),

    salesTaxAmount: yup.number().nullable().default(null),
    salesTaxStatementDescriptor: yup.string().default(""),
    salesTaxInternalNote: yup.string().nullable().default(null),

    bnplAmount: yup.number().nullable().default(null),
    bnplStatementDescriptor: yup.string().nullable().default(null),
    bnplInternalNote: yup.string().nullable().default(null),

  createNewAccount: yup.boolean().oneOf([true, false]),

  adHocAccountNumber: yup.string().required("Required").nullable().default(null).typeError("Required"),
  adHocRoutingNumber: yup.string().required("Required").nullable().default(null).typeError("Required"),
  adHocAmount: yup.string().required("Required").nullable().default(null).typeError("Required"),
  adHocStatementDescriptor: yup.string().required().nullable().default(null),
  adHocStatementEmailId: yup.string().required().nullable().trim().default(null),
  adHocInternalNote: yup.string().nullable().default(null),
  adHocBryzosBuyer: yup.boolean().oneOf([true, false]),
  sellerId: yup.string().nullable().default(null),
  sellerCompany: yup.string().nullable().default(null),
  pgpmMappingId: yup.string().nullable().default(null),
  selectedSeller: yup.object().nullable().default(null).when("createNewAccount", { is: (value: boolean) => !value, then: (s) => s.required("Required") }),
  companyName: yup.string().nullable().default(null).when("createNewAccount", { is: (value: boolean) => value, then: (s) => s.required("Required") }),
  cassDisbursementRoutingNumber: yup.string().nullable().default(null).typeError("Required").when("createNewAccount", { is: (value: boolean) => value, then: (s) => s.required("Required") }),
  cassDisbursementAccountNumber: yup.string().nullable().default(null).typeError("Required").when("createNewAccount", { is: (value: boolean) => value, then: (s) => s.required("Required") }),
});