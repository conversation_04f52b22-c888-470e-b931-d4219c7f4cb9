import React, { useState } from 'react';
import { Select, MenuItem, FormControl, InputLabel } from '@mui/material';

interface TimePickerProps {
  value: string; // initial value in "hh:mm" format
  onChange?: (time: string) => void; // optional onChange handler
  hourStep?: number; // step for hours dropdown
  minuteStep?: number; // step for minutes dropdown
  showMinutes?: boolean; // option to show/hide minutes dropdown
}

const MenuProps = {
  classes:{
    paper:'timerWrapper'
  }
};

const TimePicker: React.FC<TimePickerProps> = ({ value, onChange, hourStep = 1, minuteStep = 1, showMinutes = true }) => {
  const [hour, setHour] = useState<number>(parseInt(value.split(':')[0]) % 12 || 12);
  const [minute, setMinute] = useState<number>(parseInt(value.split(':')[1].substring(0, 2)) || 0);
  const [period, setPeriod] = useState<'AM' | 'PM'>(parseInt(value.split(':')[0]) >= 12 ? 'PM' : 'AM');

  const handleHourChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newHour = parseInt(e.target.value);
    setHour(newHour);
    triggerOnChange(newHour, minute, period);
  };

  const handleMinuteChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMinute = parseInt(e.target.value);
    setMinute(newMinute);
    triggerOnChange(hour, newMinute, period);
  };

  const handlePeriodChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newPeriod = e.target.value as 'AM' | 'PM';
    setPeriod(newPeriod);
    triggerOnChange(hour, minute, newPeriod);
  };

  const triggerOnChange = (hour: number, minute: number, period: 'AM' | 'PM') => {
    if (onChange) {
      const adjustedHour = period === 'PM' ? (hour === 12 ? 12 : hour + 12) : (hour === 12 ? 0 : hour);
      const timeString = `${adjustedHour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      onChange(timeString);
    }
  };

  const renderOptions = (step: number, max: number) => {
    const options = [];
    for (let i = 1; i <= max; i += step) {
      options.push(
        <MenuItem key={i} value={i}>
          {i.toString().padStart(2, '0')}
        </MenuItem>
      );
    }
    return options;
  };

  return (
    <div style={{ display: 'flex', gap: '12px' }}>
      <FormControl className='dropdownWrapper'>
      <InputLabel>Hour</InputLabel>
        <Select className='customeTimerSelect' value={hour} onChange={handleHourChange} displayEmpty label="Hour"  MenuProps={MenuProps}>
          {renderOptions(hourStep, 12)}
        </Select>
      </FormControl>
      {showMinutes && (
        <>
          <span>:</span>
          <FormControl className='dropdownWrapper'>
          <InputLabel>Minute</InputLabel>
            <Select className='customeTimerSelect' value={minute} onChange={handleMinuteChange} displayEmpty label="Minute" MenuProps={MenuProps}>
              {renderOptions(minuteStep, 59)}
            </Select>
          </FormControl>
        </>
      )}
      <FormControl className='dropdownWrapper'>
      <InputLabel>Period</InputLabel>
        <Select className='customeTimerSelect' value={period} onChange={handlePeriodChange} displayEmpty label="Period" MenuProps={MenuProps}>
          <MenuItem value="AM">AM</MenuItem>
          <MenuItem value="PM">PM</MenuItem>
        </Select>
      </FormControl>
    </div>
  );
};

export default TimePicker;
