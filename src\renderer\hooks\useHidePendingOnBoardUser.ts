import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

export type HidePendingOnBoardUserPayload = {
  data: {
    id: string;
    hide_reason: string;
  };
};

const useHidePendingOnBoardUser = () => {
  const queryClient = useQueryClient();

  return useMutation(async (payload: HidePendingOnBoardUserPayload) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/user/hidePendingOnBoardUser`,
        payload
      );

      queryClient.invalidateQueries([reactQueryKeys.getAllPendingOnBoardUsers]);

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message ?? "");
    }
  });
};

export default useHidePendingOnBoardUser;
