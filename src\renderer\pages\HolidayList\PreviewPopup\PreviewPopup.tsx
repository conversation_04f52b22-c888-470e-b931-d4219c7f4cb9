import { Controller, useForm } from "react-hook-form";
import styles from "../HolidayList.module.scss";

import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import dayjs from "dayjs";
import { dateTimeFormat, userRole } from "@bryzos/giss-ui-library";
import CustomDatePicker from "../../../components/common/CustomDatePicker";
import CustomTimePicker from '../../../components/common/TimePicker/CustomTimePicker';
import { Autocomplete, TextField } from "@mui/material";
import useGetAllUser from "../../../hooks/useGetAllUser";
import clsx from "clsx";
import Loader from "../../../components/common/Loader";
import { useEffect, useState } from "react";
import usePostGetDeliveryDatePreview from "../../../hooks/usePostGetDeliveryDatePreview";
import { ReactComponent as CheckIcon } from '../../../../assests/images/check-icon.svg';
import { ReactComponent as NotCheckIcon } from '../../../../assests/images/Close.svg';
import { ReactComponent as CloseIcon } from '../../../../assests/images/close-icon.svg';

export const holidayPreviewFormSchema = yup.object().shape({
    data: yup.object().shape({
        checkoutDate: yup
            .string()
            .required('Checkout Date is Required'),
        checkoutTime: yup.string().required("Checkout Start Time is Required"),
        user: yup.number().nullable().required()
    }),
    userObj: yup.object().nullable().default(null)

});

export type holidayPreviewFormSchemaType = yup.InferType<
    typeof holidayPreviewFormSchema
>;

const defaultHolidayPreviewSchema = {
    checkoutDate: '',
    checkoutTime: "09:00",
    user: ''
}

const PreviewPopup = ({ setShowPreviewPopup }) => {
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        trigger,
        control,
        formState: { isValid, errors },
    } = useForm<holidayPreviewFormSchemaType>({
        defaultValues: {
            data: defaultHolidayPreviewSchema
        },
        resolver: yupResolver(holidayPreviewFormSchema),
        mode: "onBlur",
    });

    const {
        data: allUsers
    } = useGetAllUser();

    const {
        mutateAsync: getDeliveryDatePreviewList,
        data: getDeliveryDatePreviewListData,
        isLoading: isGetDeliveryDatePreviewListLoading,
    } = usePostGetDeliveryDatePreview();

    const [sortedUserList, setSortedUserList] = useState([]);
    const [deliveryDatePreviewList, setDeliveryDatePreviewList] = useState([]);

    useEffect(()=>{
        if(allUsers){
            const sortUserList = allUsers.filter(user => user.type === userRole.buyerUser && user.is_active === 1)
            setSortedUserList(sortUserList);
        }
    },[allUsers])

    useEffect(() => {
        if(getDeliveryDatePreviewListData){
            setDeliveryDatePreviewList(getDeliveryDatePreviewListData);
        }
    },[getDeliveryDatePreviewListData])

    const handleClose = () => {
        setShowPreviewPopup(false);
    }

    const submit = (data) => {
        // console.log('check data  ', data.data)
        getDeliveryDatePreviewList(data.data)
    }

    return (
        <div className={styles.previewPopupMain}>
            {!sortedUserList.length || isGetDeliveryDatePreviewListLoading ? (
               <div className={styles.noDataFound}>
                 <Loader />
              </div>
            ) : (
                <>
                <div className={styles.previewPopupHeader}>
                    <div className={styles.deliveryDateTitle}>Delivery Date Preview</div>
                    <button className={styles.closeIcon} onClick={handleClose}><CloseIcon/></button>
                </div>
                <div className={styles.previewNote}>
                    Note: Select the Checkout Date and Buyer to preview the Delivery Dates (considering the Buyer's Receiving Hours and the device's local Checkout time)
                </div>
                    <div className={styles.inputRow}>
                        <div className={styles.checkoutDateMain}>
                            <div className={clsx(styles.checkoutDateCol,"previewDatePicker")}>
                            <Controller
                                control={control}
                                rules={{
                                    required: true,
                                }}
                                name={register(`data.checkoutDate`).name}
                                render={({ field: { value, onChange }, fieldState: { error } }) => {
                                    const datePickerValue = watch(`data.checkoutDate`) ? dayjs(watch(`data.checkoutDate`)) : null;
                                    return (
                                        <CustomDatePicker
                                            value={datePickerValue}
                                            onChange={(newValue) => {
                                                onChange(newValue?.format(dateTimeFormat.isoFormat));
                                            }}
                                            format={'MM/DD/YY, dddd'}
                                            minDate={dayjs()} 
                                            label="Checkout Date"/>
                                    )
                                }}
                            />

                            <Controller
                                control={control}
                                rules={{
                                    required: true,
                                }}
                                name={register(`data.checkoutTime`).name}
                                render={({ field: { value, onChange }, fieldState: { error } }) => {
                                    const customTimePickerOnChange = (newValue: string) => {
                                        onChange(newValue);
                                    }
                                    return (
                                        <CustomTimePicker
                                            showMinutes={false}
                                            value={watch('data.checkoutTime')}
                                            onChange={customTimePickerOnChange} ></CustomTimePicker>
                                    )
                                }}
                            />
                            </div>
                          
                        </div>

                        <div>
                            <Controller
                                control={control}
                                rules={{
                                    required: true,
                                }}
                                name={register(`data.user`).name}
                                render={({ field: { value, onChange }, fieldState: { error } }) => {
                                    return (
                                        <Autocomplete
                                        className={clsx(styles.selectDropdown,"selectDropdownPreview")}
                                            options={sortedUserList}
                                            {...register("userObj")}
                                            value={watch("userObj")}
                                            isOptionEqualToValue={(option: any, value) => option.id === value.id}
                                            getOptionLabel={(option: any) => `${option.email_id}`}
                                            renderInput={(params) => (
                                                <TextField {...params} placeholder="Select Users" label={"Buyer"} />
                                            )}
                                            classes={{
                                                root: styles.autoCompleteDesc,
                                                popper: styles.autocompleteDescPanel,
                                                paper: styles.autocompleteDescInnerPanel,
                                                listbox: clsx(styles.listAutoComletePanel1, 'adHoclistAutoComletePanel1')
                                            }}
                                            onChange={(e, data: any) => {
                                                setValue("userObj", data ?? null);
                                                if (data?.id) {
                                                    setValue("data.user", data?.id ?? null);
                                                } else {
                                                    setValue("data.user", null);
                                                }
                                                trigger("data.user")
                                            }}
                                            renderOption={(props, option: any) => (
                                                <span {...props} key={option.id}>{option.first_name} {option.last_name}<br />{option.email_id}</span>
                                            )}
                                        />
                                    )
                                }}
                            />
                        </div>

                      <button className={styles.checkAvailBtn} onClick={handleSubmit(submit)} disabled={!isValid}>Check Availability</button>

                    </div>

                        {!!(deliveryDatePreviewList.length) &&
                        <div className={styles.tblscroll}>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Checkout Date + n Days</th>
                                        <th>Delivery Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {deliveryDatePreviewList.map((deliveryDatePreviewObj: any, i) => (
                                        <tr key={deliveryDatePreviewObj.days_to_add+i}>
                                            <td>{deliveryDatePreviewObj.title}</td>
                                            <td>{dayjs(deliveryDatePreviewObj.value).format('MM/DD/YY, dddd')}</td>
                                            <td>{!deliveryDatePreviewObj.disabled ?
                                                <CheckIcon/> : <NotCheckIcon/>
                                                }</td>
                                        </tr>

                                    ))}
                                </tbody>
                            </table>
                       </div>
                        }
                </>
            )}
        </div>
    )
}

export default PreviewPopup;