

.agGridAdmin {
    --ag-icon-font-code-asc: '\25B2';
    --ag-icon-font-code-desc: '\25BC';
    --ag-icon-font-code-none: '\25B2\25BC';

    .ag-center-cols-viewport {
        min-height: 5000px !important;
    }

    .ag-icon-asc::before {
        content: var(--ag-icon-font-code-asc);
    }

    .ag-icon-none::before {
        content: var(--ag-icon-font-code-none);
        color: green;
        padding: 2px;
        margin-bottom: 5px;
        font-size: 20px !important;
    }

}

.ag-root-wrapper {
    .ag-root-wrapper-body {
        .ag-body-horizontal-scroll-viewport {
            overflow-x: auto;

            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }

            &::-webkit-scrollbar-track {
                box-shadow: inset 0 0 6px #a8b2bb;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #a8b2bb;
                border-radius: 4px;
            }
        }

        .ag-header-row {
            .ag-header-cell {
                padding-left: 20px;
                padding-right: 20px;
                line-height: 1.2;
                font-weight: 600;
                font-size: 16px;
                margin: 0;
                text-align: left;
                color: #fff;
                background: #676f7c;

                &:hover {
                    color: #fff;
                    background: #676f7c;
                }
            }

            .ag-header-cell:not(.ag-column-resizing)+.ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover {
                color: #fff;
                background: #676f7c;
            }
        }

        .ag-body-viewport-wrapper.ag-layout-normal {
            overflow-x: scroll;
            overflow-y: scroll;
        }

        ::-webkit-scrollbar {
            -webkit-appearance: none;
            width: 8px;
            height: 6px;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 4px;
            background: #a8b2bb;
            box-shadow: inset 0 0 6px #a8b2bb;
        }

        .ag-body {
            .ag-body-viewport {
                .ag-center-cols-clipper {
                    .ag-row-odd {
                        background-color: #f2f2f2;
                    }

                    .ag-cell {
                        cursor: pointer;
                    }

                    .red-border {
                        border: 1px solid red;
                    }
                }
            }
        }
    }
}

.ag_theme_quartz {
    --ag-foreground-color: #676f7c;
    --ag-background-color: white;
    --ag-header-foreground-color: white;
    --ag-header-background-color: #676f7c;
    --ag-odd-row-background-color: #f2f2f2;
    --ag-header-column-resize-handle-color: rgb(126, 46, 132);
    --ag-font-size: 14px;
    --ag-font-family: monospace;
    --ag-icon-font-code-aggregation: "\f247";
    --ag-icon-font-color-group: red;
    --ag-icon-font-weight-group: normal;
}

.header {
    display: flex;
    align-items: center;
    gap: 18px;
    padding: 10px 0px;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
  
    label {
      font-family: Noto Sans;
      font-size: 16px;
      font-weight: 500;
      color: #333;
      padding: 14px;
    }
  
    button {
      background-color: var(--primaryColor);
      font-family: Noto Sans;
      color: white;
      border: none;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 600;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s;
      &:hover{
        background-color: #676f7c;
      }
    }
  
    input {
      font-family: Noto Sans;
      flex-grow: 1;
      padding: 8px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 16px;
      outline: none;
      transition: border-color 0.3s ease-in-out;
      height: 40px;
    }
  }
  
  .logReaderPopup{
    width: 100%;
    padding: 30px 25px;
    .logReadertitle{
        font-family: Noto Sans;
        font-size: 20px;
        color: #333;
        font-weight: 600;
    }
    
    .loadingText{
        font-family: Noto Sans;
        text-align: left;
        color: #333;
        font-size: 18px;
        padding: 30px 0px;
    }
    .fileUploadMain{
        .fileDrop{
            margin-bottom: 12px;
        }
      .errorTxt{
        font-family: Noto Sans;
        font-size: 14px;
        color: #ff0000;
      }
    }

    .closeBtn{
        width: 100%;
        background-color: var(--primaryColor);
        font-family: Noto Sans;
        color: white;
        border: none;
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 5px;
        cursor: pointer;
    }
  }