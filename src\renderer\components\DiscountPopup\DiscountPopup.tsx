import React, { useEffect, useRef, useState } from "react";
import styles from "./DiscountPopup.module.scss";
import { useImmer } from "use-immer";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { DiscountFromSchema, DiscountFromSchemaType } from "../../models/discount.model";
import clsx from "clsx";
import dayjs from "dayjs";
import MatSelect from "../common/MatSelect";
import { Tooltip } from "@mui/material";
import MatPopup from "../common/MatPopup";
import PlusIcon from "../../../assests/images/plus-4.png";
import MinusIcon from "../../../assests/images/minus-2.png";
import { ReactComponent as WarningIcon } from "../../../assests/images/Warning.svg";
import { formatToTwoDecimalPlaces } from "@bryzos/giss-ui-library";

type Props = {
  pageName: string;
  popupHeading: string;
  discountData: any;
  pricingColumnList: [];
  defaultValues: [];
  submitUserDiscount: (data: any) => void;
  confirmationPopupClose: () => void;
};

const DiscountPopup: React.FC<Props> = ({ pageName, popupHeading, discountData, pricingColumnList, defaultValues, submitUserDiscount, confirmationPopupClose }) => {

  const {
    register,
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = useForm<DiscountFromSchemaType>({
    defaultValues: {
      discIsDiscounted: false,
      discDiscountRate: 0,
      discDiscountPeriod: 0,
      discDiscountPhaseoutPeriod: 0,
      discDiscountPricingColumn: "Neutral_Pricing",
      discIsDiscountVarOverriden: false,
      discDiscountPhaseoutStartdate: null,
      userCreatedDate: null,
      spreadRate: 0
    },
    resolver: yupResolver(DiscountFromSchema),
    mode: "onSubmit",
  });
  const [discountPricingColumnList, setDiscountPricingColumnList] = useImmer<any[]>([]);
  const [disableUserDicountBtn, setDisableUserDicountBtn] = useState(true);
  const [disableUserDicountFields, setDisableUserDicountFields] = useState(false);
  const [userDicountData, setUserDicountData] = useState<any>({});
  const [companyName, setCompanyName] = useState<string>("");
  const [cohortName, setCohortName] = useState<string>("");
  const [discountDefaultValues, setDiscountDefaultValues] = useState<any>({});
  const [discountOverridenPopup, setDiscountOverridenPopup] = useState(false);
  const [isBuyerSpreadMarkedUp,setIsBuyerSpreadMarkedUp] = useState(false);
  const [isSellerSpreadMarkedUp,setIsSellerSpreadMarkedUp] = useState(true);
  const datePickerInputRef = useRef<any>(null);
  const [spreadPayload,setSpreadPayload] = useState<any>(null)
  const [isOldBuyerSpreadMarkedUp,setIsOldBuyerSpreadMarkedUp] = useState(false)
  const [isOldSellerSpreadMarkedUp,setIsOldSellerSpreadMarkedUp] = useState(true);
  const [buyerValueChanged,setBuyerValueChanged] = useState(false)
  const [sellerValueChanged,setSelllerValueChanged] = useState(false)
  const [defaultOldBuyerValue,setDefaultOldBuyerValue] = useState(0)
  const [defaultOldSellerValue,setDefaultOldSellerValue] = useState(0)

  useEffect(() => {
    setDiscountPricingColumnList(pricingColumnList)
  }, [pricingColumnList])

  useEffect(() => {
    initializeDiscountData(discountData)
  }, [discountData])

  useEffect(() => {
    let defaultValueObj: any = {}
    defaultValues.forEach((defaultValue: any) => {
      defaultValueObj[defaultValue.key] = defaultValue.value
    })
    setDiscountDefaultValues(defaultValueObj)
  }, [defaultValues])

  useEffect(()=>{
    if(errors.discDiscountPhaseoutStartdate && datePickerInputRef.current){
      datePickerInputRef.current.focus();
    }
  },[errors.discDiscountPhaseoutStartdate])



  const initializeDiscountData = (userData: any) => {
    setValue("id", userData?.id)
    if (pageName === 'company') setCompanyName(userData?.company_name)
    if (pageName === 'cohort') setCohortName(userData?.onboarded_app)
    if (pageName === 'user') setValue("userCreatedDate", userData.created_date)
    let spreadRate = (100 * (userData.seller_spread_rate - 1)).toFixed(2);
    if(Number(spreadRate) < 0){
      spreadRate = (Number(spreadRate)* -1).toFixed(2);
      setIsSellerSpreadMarkedUp(false);
      setIsOldSellerSpreadMarkedUp(false)
    }
    if (userData?.disc_is_discounted) {
      let userDiscountRate:string = (100 * (1 - userData.disc_discount_rate)).toFixed(2);
      if(Number(userDiscountRate) < 0){
        userDiscountRate = (Number(userDiscountRate)* -1).toFixed(2)
        setIsBuyerSpreadMarkedUp(true)
        setIsOldBuyerSpreadMarkedUp(true)
      }
      setDefaultOldBuyerValue(+userDiscountRate);
      setDefaultOldSellerValue(+spreadRate);
      setValue("discIsDiscounted", userData.disc_is_discounted)
      setValue("discDiscountRate", +userDiscountRate)
      setValue("discDiscountPeriod", getDiscountPeriod(userData.disc_discount_period, userData.disc_discount_phaseout_startdate))
      setValue("discDiscountPhaseoutStartdate", userData.disc_discount_phaseout_startdate)
      setValue("discDiscountPhaseoutPeriod", userData.disc_discount_phaseout_period)
      setValue("discDiscountPricingColumn", userData.disc_discount_pricing_column)
      setValue("discIsDiscountVarOverriden", userData?.disc_is_discount_var_overriden)
      setValue("spreadRate", +spreadRate)
      setUserDicountData(getValues())
    } else if (getIsDiscounted(userData.disc_discount_percentage, userData.disc_discount_phaseout_period, userData.disc_discount_pricing_column)) {
      let userDiscountRate = userData.disc_discount_percentage;
        if(Number(userDiscountRate) < 0){
          userDiscountRate = (Number(userDiscountRate)* -1).toFixed(2)
          setIsBuyerSpreadMarkedUp(true)
          setIsOldBuyerSpreadMarkedUp(true)
        }
      setDefaultOldBuyerValue(+userDiscountRate);
      setDefaultOldSellerValue(+spreadRate);
      setValue("discIsDiscounted", true)
      setValue("discDiscountRate", +userDiscountRate)
      setValue("discDiscountPeriod", userData.disc_discount_period ?? 0)
      setValue("discDiscountPhaseoutStartdate", userData.disc_discount_phaseout_startdate)
      setValue("discDiscountPhaseoutPeriod", userData.disc_discount_phaseout_period)
      setValue("discDiscountPricingColumn", userData.disc_discount_pricing_column)
      setValue("spreadRate", +spreadRate)
      setUserDicountData(getValues())
    } else {
      setDisableUserDicountFields(true);
      setValue("discIsDiscountVarOverriden", userData?.disc_is_discount_var_overriden)
      if(getIsDiscounted(userData.disc_discount_rate, userData.disc_discount_phaseout_period, userData.disc_discount_pricing_column)){
        
        let userDiscountRate = (100 * (1 - userData.disc_discount_rate)).toFixed(2)
        if(Number(userDiscountRate) < 0){
          userDiscountRate = (Number(userDiscountRate)* -1).toFixed(2)
          setIsBuyerSpreadMarkedUp(true)
          setIsOldBuyerSpreadMarkedUp(true)
        }
        const fieldData = {
          "discIsDiscounted": true,
          "discDiscountRate": +userDiscountRate,
          "discDiscountPeriod": getDiscountPeriod(userData.disc_discount_period, userData.disc_discount_phaseout_startdate),
          "discDiscountPhaseoutStartdate": userData.disc_discount_phaseout_startdate,
          "discDiscountPhaseoutPeriod": userData.disc_discount_phaseout_period,
          "discDiscountPricingColumn": userData.disc_discount_pricing_column,
          "discIsDiscountVarOverriden": userData?.disc_is_discount_var_overriden,
          "spreadRate": +spreadRate
        }
        setUserDicountData(fieldData);
      }
    }
    // setShowDiscountPopup(true);
    setDisableUserDicountBtn(true)
  }

  const getIsDiscounted = (userDiscount:any, phaseOutPeriod: any, pricingColumn: string):boolean=>{
    return ((userDiscount !== undefined && userDiscount !== null)
              && (phaseOutPeriod >= 0) 
              && !!pricingColumn);
  }

  const handleIsDiscountedOnchange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.checked) {
      const discountData = getValues();
      setDisableUserDicountFields(true);
      setDisableUserDicountBtn(false)
      reset();
      setValue("id", discountData.id)
      setValue("discIsDiscountVarOverriden", discountData.discIsDiscountVarOverriden);
      if (pageName === 'user') setValue("userCreatedDate", discountData.userCreatedDate)
    } else {
      if (Object.keys(userDicountData).length === 0) {
        userDicountData.discDiscountRate = discountDefaultValues?.DEFAULT_DISCOUNT_PERCENTAGE;
        userDicountData.discDiscountPeriod = discountDefaultValues?.DEFAULT_DISCOUNT_TIME_PERIOD;
        userDicountData.discDiscountPhaseoutStartdate = pageName === "user" ? getDiscountStartDate(discountDefaultValues?.DEFAULT_DISCOUNT_TIME_PERIOD, null) : null;
        userDicountData.discDiscountPhaseoutPeriod = discountDefaultValues?.DISCOUNT_PHASEOUT_PERIOD;
        userDicountData.discDiscountPricingColumn = discountDefaultValues?.PRICING_COLUMN;
        userDicountData.spreadRate = +(100 * (discountDefaultValues?.DEFAULT_SELLER_SPREAD_RATE - 1)).toFixed(2);
      }
      userDicountData.discIsDiscounted = e.target.checked;
      setUserDiscountValues(userDicountData);
      setDisableUserDicountFields(false)
      handleChangeDiscountUserData();
    }
  }

  const setUserDiscountValues = (data: any) => {
    Object.keys(data).forEach((key: any) => {
      setValue(key, data[key]);
    })
  }

  const handleChangeDiscountUserData = () => {
    const discountPeriod =  getValues("discDiscountPeriod");
    const isDataFilled = getValues("discDiscountRate") >= 0;//getValues("discDiscountPhaseoutPeriod") >= 0 && getValues("discDiscountPricingColumn") !== "Choose One" && getValues("discDiscountRate") >= 0 && !!( discountPeriod !== null && (discountPeriod >= 0) || getValues("discDiscountPhaseoutStartdate"));
    if (isDataFilled) {
      setDisableUserDicountBtn(false)
    } else {
      setDisableUserDicountBtn(true)
    }
  }

  const submitData = (data: any) => {
    if(isBuyerSpreadMarkedUp){
      data.discDiscountRate = -1 * getValues('discDiscountRate');
    }

    if(!isSellerSpreadMarkedUp){
      data.spreadRate = -1 * getValues('spreadRate');
    }
    showConfirmationPopup(data);
  }

  const getDiscountPeriod = (discountPeriod: any, discountStartDate: any) => {
    let formatDiscountPeriod = discountPeriod;
    discountStartDate = typeof discountStartDate === "string" ? discountStartDate.split(" ")[0] : discountStartDate;
    if (discountStartDate && discountPeriod < 0 && discountPeriod === null && discountPeriod === undefined)
      formatDiscountPeriod = dayjs(discountStartDate).diff(dayjs(getValues("userCreatedDate")?.split(" ")[0]), "days")-1;
    return formatDiscountPeriod;
  }
  const getDiscountStartDate = (discountPeriod: any, discountStartDate: any) => {
    let formatDiscountStartDate = discountStartDate;
    if (discountPeriod && !discountStartDate)
      formatDiscountStartDate = dayjs(getValues("userCreatedDate")).add(+discountPeriod+1, 'days');
    return formatDiscountStartDate;
  }

  const handleOnChangeIsDicountOverriden = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDisableUserDicountBtn(false);
    if(!e.target.checked) setDiscountOverridenPopup(true);
  }

  const handleBuyerMarkupChange = (event:any)=>{
    if(disableUserDicountFields){
      return;
    }
    setBuyerValueChanged((buyerValueChanged) => !buyerValueChanged)
    setIsBuyerSpreadMarkedUp(event.target.value === "markUp");
    handleChangeDiscountUserData()
  }

  const handleSellerMarkupChange = (event:any)=>{
    if(disableUserDicountFields){
      return;
    }
    setSelllerValueChanged((sellerValueChanged) => !sellerValueChanged)
    setIsSellerSpreadMarkedUp(event.target.value === "markUp");
    handleChangeDiscountUserData()
  }

  const confirmationChangePopupYes = () => {
    confirmationChangePopupClose();
    let formData = {...spreadPayload};
    if(!getValues("discIsDiscounted")){
      userDicountData.discIsDiscounted = getValues("discIsDiscounted");
      userDicountData.discIsDiscountVarOverriden = !!getValues("discIsDiscountVarOverriden");
      userDicountData.id= getValues('id');
      formData = userDicountData;
    }
    submitUserDiscount(formData);
  };

  const confirmationChangePopupClose = () => {
    setSpreadPayload(null)
  };

  const showConfirmationPopup = (data) => {
    setSpreadPayload(data)
  }
  const SubmitConfirmation = () => {
    let oldBuyerValue = defaultOldBuyerValue;
    let oldSellerValue = defaultOldSellerValue;
    let newBuyerValue = getValues('discDiscountRate')
    let newSellerValue = getValues('spreadRate')

    if(!isOldBuyerSpreadMarkedUp){
      oldBuyerValue = -1 * defaultOldBuyerValue
    }
    if(!isOldSellerSpreadMarkedUp){
      oldSellerValue = -1 * defaultOldSellerValue
    }

    if(!isBuyerSpreadMarkedUp){
      newBuyerValue = -1 * Number(getValues('discDiscountRate'));
    }
    if(!isSellerSpreadMarkedUp){
      newSellerValue = -1 * Number(getValues('spreadRate'));
    } 
    
    const buyerValuePer = checkPercentageChange(oldBuyerValue,newBuyerValue)
    const sellerValuePer = checkPercentageChange(oldSellerValue,newSellerValue)
    return (
      <MatPopup
      className={styles.orderContinuePopup}
      open={!!spreadPayload}
    >
      <div className={styles.continuePopup}>
        <p className={styles.processTxt}>Do you want to proceed?</p>
        <div className={styles.popupDiv}>
          {
            !((oldBuyerValue === newBuyerValue) && (oldSellerValue === newSellerValue)) &&
            <>
              <span className={styles.popupTitle}> The spread has been updated:</span>
              <ul>
                {
                  (oldBuyerValue != newBuyerValue) && (<li> Buyer: ({oldBuyerValue}%) → ({newBuyerValue}%) </li>)
                }
                {
                  (oldSellerValue != newSellerValue) && (<li> Seller: ({oldSellerValue}%) → ({newSellerValue}%) </li>)
                }
              </ul>
              {(!(oldBuyerValue === 0 || newBuyerValue === 0)) && (buyerValueChanged && (<div className={styles.popupDesc}><WarningIcon /> Buyer spread has shifted from <b>{isBuyerSpreadMarkedUp ? "negative to positive." : "positive to negative."}</b></div>))}
              {(!(oldSellerValue === 0 || newSellerValue === 0)) && (sellerValueChanged && (<div className={styles.popupDesc}><WarningIcon />  Seller spread has shifted from <b> {(isSellerSpreadMarkedUp) ? "negative to positive." : "positive to negative."}</b> </div>))}
              {!!buyerValuePer && <div className={styles.popupDesc}><WarningIcon /> {(buyerValuePer >= 5 ? <span> Buyer spread <b>increased by {formatToTwoDecimalPlaces(buyerValuePer.toString())}%. </b> </span> : <span>Buyer spread <b>decreased by {formatToTwoDecimalPlaces(Math.abs(buyerValuePer).toString())}%.</b></span>)}</div>}
              {!!sellerValuePer && <div className={styles.popupDesc}><WarningIcon /> {(sellerValuePer >= 5 ? <span> Seller spread <b>increased by {formatToTwoDecimalPlaces(sellerValuePer.toString())}%. </b> </span> : <span>Seller spread <b>decreased by {formatToTwoDecimalPlaces(Math.abs(sellerValuePer).toString())}%.</b></span>)}</div>}
            </>
          }
        </div>
        <div className={styles.yesAndnoBtn}>
          <button className={styles.okBtn} onClick={confirmationChangePopupYes}>
            Yes
          </button>
          <button className={styles.okBtn} onClick={confirmationChangePopupClose}>
            No
          </button>
        </div>
      </div>
    </MatPopup>
    )
  }

  function checkPercentageChange(oldValue, newValue) {
    let change = newValue - oldValue;
    if (change >= 5 || change <= -5 ) {
        return change;
    } else {
        return false;
    }
}

  const handleSubmitOnClick = () => {
    const currentDate = dayjs();
    const discDiscountPhaseoutStartdate = dayjs(getValues('discDiscountPhaseoutStartdate'));
    const isValid = discDiscountPhaseoutStartdate.diff(currentDate, "days") > 0;
    if (!isValid) {
      setValue('discDiscountPeriod', discountDefaultValues?.DEFAULT_DISCOUNT_TIME_PERIOD);
      setValue('discDiscountPhaseoutPeriod', discountDefaultValues?.DISCOUNT_PHASEOUT_PERIOD);
      if (pageName === 'user') {
        const formatStartDate = dayjs().add(discountDefaultValues?.DEFAULT_DISCOUNT_TIME_PERIOD, "day").toDate();
        setValue('discDiscountPhaseoutStartdate', formatStartDate);
      } else {
        setValue('discDiscountPhaseoutStartdate', null);
      }
    }
    handleSubmit(submitData)()
  }

  return (
    <div className={styles.tblscrollPop}>
      <div className={styles.discountPopup}>
        <div>
          <p className={styles.note}>{watch('discIsDiscounted') &&<span>Note: In all cases, the spread is based on the NEUTRAL price.</span>}</p>
          <p className={styles.continuetext}> {popupHeading} </p>
        </div>
        {pageName === 'company' &&
          <div className={styles.discountDiv}>
            <span>Company Name :</span>
            <div className={styles.discountRightDiv}>
              {companyName}
            </div>
          </div>
        }
        {pageName === 'cohort' &&
          <div className={styles.discountDiv}>
            <span>Cohort Name :</span>
            <div className={styles.discountRightDiv}>
              {cohortName}
            </div>
          </div>
        }
        <div className={styles.discountDiv}>
          <span>Is Spread Applied:</span>
          <div className={styles.discountRightDiv}>
            <label className={styles.switch}>
              <input
                type={"checkbox"}
                {...register('discIsDiscounted')}
                onChange={(e) => {
                  register('discIsDiscounted').onChange(e)
                  handleIsDiscountedOnchange(e)
                }}
              />
              <span
                className={clsx(styles.slider, styles.round)}
              ></span>
            </label>
          </div>
        </div>
        <h3 className={styles.sectionHeader}>Buyer</h3>
        <div className={styles.discountDiv}>
          <span> Spread (%) : </span>
          <Tooltip
                  title={errors.discDiscountRate?.message}
                  placement="top"
                  classes={{
                    popper: styles.errorStyle,
                    tooltip: styles.tooltip,
                  }}
                >

          <div className={clsx(styles.discountRightDiv,styles.discountSpreadinputMain)} >
            <label className={styles.markedUpText}>{watch("discIsDiscounted") && watch("discDiscountRate") == 0 ? `Buyer Price Will Be Same As ${watch("discDiscountPricingColumn").replace("_", " ")}` : isBuyerSpreadMarkedUp ? 'Buyer Price Will Be Marked Up' : 'Buyer Price Will Be Marked Down'}</label>
            <div className={styles.discountSpreadinput}>
              <input className="enableArrow" type="number" {...register('discDiscountRate')} min={1} max={99} inputMode="numeric" onChange={(e) => { register("discDiscountRate").onChange(e); handleChangeDiscountUserData(); }} disabled={disableUserDicountFields} />
            <div className={styles.markedUpMain}>
            <label className={isBuyerSpreadMarkedUp ? styles.markedUp : styles.notMarkedUp }>
                <input
                    type="radio"
                    value="markUp"
                    disabled={disableUserDicountFields}
                    checked={isBuyerSpreadMarkedUp === true}
                    onChange={handleBuyerMarkupChange}
                    style={{ display: 'none' }}  // Hide the default radio button
                />
                
                <img src={PlusIcon} className={disableUserDicountFields ? styles.disableBtn : styles.enableBtn }/>
                
            </label>
            <label>
                <input
                    type="radio"
                    value="markDown"
                    disabled={disableUserDicountFields}
                    checked={isBuyerSpreadMarkedUp === false}
                    onChange={handleBuyerMarkupChange}
                    style={{ display: 'none' }}  // Hide the default radio button
                />
                <img src={MinusIcon} className={clsx(disableUserDicountFields ? styles.disableBtn : styles.enableBtn, !isBuyerSpreadMarkedUp ? styles.markedUp : styles.notMarkedUp ) }/>
               
            </label>
            </div>
            </div>
          </div>
                </Tooltip>
        </div>
        {/* <div className={styles.discountDiv}>
          <span> Spread Period (in days) : </span>
          <Tooltip
                  title={errors.discDiscountPeriod?.message}
                  placement="top"
                  classes={{
                    popper: styles.errorStyle,
                    tooltip: styles.tooltip,
                  }}
                >

          <div className={clsx(styles.discountRightDiv,styles.discDiscountPeriod)}>

            <input
              type="number"
              {...register('discDiscountPeriod')}
              min={0}
              step="0.0001"
              onChange={(e) => {
                register('discDiscountPeriod').onChange(e);
                const discountPhaseoutStartDate = pageName === "user" ? getDiscountStartDate(e.target.value, null) : null;
                setValue("discDiscountPhaseoutStartdate", discountPhaseoutStartDate);
                if(pageName !== "user") setDisablePhaseoutStartDate(true);
                handleChangeDiscountUserData()
              }}
              disabled={disableUserDicountFields || disableDiscountPeriod} />
            {disableDiscountPeriod &&
              <Tooltip
                title={`The Spread Period is calculated at user level.`}
                placement="right-end"
                arrow
                classes={{
                  popper: styles.popper,
                  tooltip: styles.tooltip,
                  arrow: styles.arrowTooltip,
                }}
              >
                <button className={styles.exclamationMark}><span>!</span></button>
              </Tooltip>
            }
          </div>
                </Tooltip>
        </div>
        <div className={styles.discountDiv}>
          <span> Spread Phaseout Start Date : </span>
          <Tooltip
                  title={errors.discDiscountPhaseoutStartdate?.message}
                  placement="top"
                  classes={{
                    popper: styles.errorStyle,
                    tooltip: styles.tooltip,
                  }}
                >

          <div className={clsx(styles.discountRightDiv,styles.discDiscountPeriod,'discountStartDate')}>
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              name='discDiscountPhaseoutStartdate'
              render={({ field: { value, onChange }, fieldState: { error } }) => {
                return (
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      inputRef={datePickerInputRef}
                      value={value && dayjs(value)}
                      minDate={dayjs().add(1,'day')}
                      onChange={(newValue) => {
                        onChange(newValue);
                        const discountPeriod = pageName === "user" ? getDiscountPeriod(0, newValue) : 0;
                        setValue("discDiscountPeriod", discountPeriod)
                        if(pageName !== "user") setDisableDiscountPeriod(true);
                        handleChangeDiscountUserData()
                      }}
                      disabled={disableUserDicountFields || disablePhaseoutStartDate}
                    />
                  </LocalizationProvider>
                )
              }}
            />
           {disablePhaseoutStartDate &&
            <Tooltip
              title={"The Spread Phaseout Start Date is determined by adding the Spread Period to each user's creation date, resulting in a Spread Phaseout Start Date that might be different for each user based on when their account was created. If you prefer to have a uniform start date for all users, you should directly set the Spread Phaseout Start Date."}
              placement="right-end"
              arrow
              classes={{
                popper: styles.popper,
                tooltip: styles.tooltip,
                arrow: styles.arrowTooltip,
              }}
            >
              <button className={styles.exclamationMark}><span>!</span></button>
            </Tooltip>
          }
          </div>
                </Tooltip>
        
        </div>
        <div className={styles.discountDiv}>
          <span> Spread Phaseout Period (in days) : </span>
          <Tooltip
                  title={errors.discDiscountPhaseoutPeriod?.message}
                  placement="top"
                  classes={{
                    popper: styles.errorStyle,
                    tooltip: styles.tooltip,
                  }}
                >

          <div className={styles.discountRightDiv}>

            <input type="number" {...register('discDiscountPhaseoutPeriod')} onChange={(e) => { register("discDiscountPhaseoutPeriod").onChange(e); handleChangeDiscountUserData(); }} min={1} disabled={disableUserDicountFields} />
          </div>
                </Tooltip>
        </div> */}
        {/* <div className={styles.discountDiv}>
          <span> Base Pricing Column : </span>
          <div className={styles.discountRightDiv}>
            <MatSelect
              className={clsx(styles.InputFieldcss, 'inputPendingUsers')}
              fieldName={register("discDiscountPricingColumn").name}
              control={control}
              placeHolderText="Choose One"
              options={discountPricingColumnList.map(pricingColumn => ({ title: pricingColumn.replace("_", " "), value: pricingColumn }))}
              disabled={disableUserDicountFields}
              onChange={(e) => {
                register("discDiscountPricingColumn").onChange(e)
                handleChangeDiscountUserData()
              }}
            />
          </div>
        </div> */}
        <h3 className={styles.sectionHeader}>Seller</h3>
        <div className={styles.discountDiv}>
          <span> Spread (%) : </span>
          <Tooltip
            title={errors.spreadRate?.message}
            placement="top"
            classes={{
              popper: styles.errorStyle,
              tooltip: styles.tooltip,
            }}
          >
           <div className={clsx(styles.discountRightDiv,styles.discountSpreadinputMain)} >
            <label className={styles.markedUpText}>{watch("discIsDiscounted") && watch("spreadRate") == 0 ? `Seller Price Will Be Same As ${watch("discDiscountPricingColumn").replace("_", " ")}` : isSellerSpreadMarkedUp?'Seller Price Will Be Marked Up':'Seller Price Will Be Marked Down'}</label>
            <div className={styles.discountSpreadinput}>
              <input className="enableArrow" type="number" {...register('spreadRate')} min={0} max={100} inputMode="numeric" onChange={(e) => { register("spreadRate").onChange(e);handleChangeDiscountUserData(); }} disabled={disableUserDicountFields} />
              <div className={styles.markedUpMain}>
              <label className={isSellerSpreadMarkedUp ? styles.markedUp : styles.notMarkedUp }>

                <input
                    type="radio"
                    value="markUp"
                    disabled={disableUserDicountFields}
                    checked={isSellerSpreadMarkedUp === true}
                    onChange={handleSellerMarkupChange}
                    style={{ display: 'none' }}  // Hide the default radio button
                />
                  <img src={PlusIcon} className={disableUserDicountFields ? styles.disableBtn : styles.enableBtn}/>

                {/* <span
                    style={{
                        display: 'inline-block',
                        padding: '5px',
                        cursor: disableUserDicountFields ? 'default':'pointer',
                        fontWeight: isSellerSpreadMarkedUp ? 'bold' : 'normal',
                    }}
                >+</span> */}
            </label>
            <label>
                <input
                    type="radio"
                    value="markDown"
                    disabled={disableUserDicountFields}
                    checked={isSellerSpreadMarkedUp === false}
                    onChange={handleSellerMarkupChange}
                    style={{ display: 'none' }}  // Hide the default radio button
                />
                <img src={MinusIcon} className={clsx(disableUserDicountFields ? styles.disableBtn : styles.enableBtn, !isSellerSpreadMarkedUp ? styles.markedUp : styles.notMarkedUp ) }/>

                {/* <span
                    style={{
                        display: 'inline-block',
                        padding: '5px',
                        cursor: disableUserDicountFields ? 'default':'pointer',
                        fontWeight: !isSellerSpreadMarkedUp ? 'bold' : 'normal' ,
                    }}
                >-</span> */}
            </label>
            </div>
            </div>
            </div>
          </Tooltip>
        </div>
        {(pageName === 'user') &&
          <div className={clsx(styles.discountDiv, styles.bottomAction)}>
            <input type="checkbox" {...register('discIsDiscountVarOverriden')} onChange={(e) => { register("discIsDiscountVarOverriden").onChange(e); handleOnChangeIsDicountOverriden(e); }} />
            <span className={styles.checkBoxDiv}> Is Spread Overriden </span>
          </div>}
        <div className={styles.yesAndnoBtn}>
          <button
            className={styles.okBtn}
            onClick={() => {handleSubmitOnClick()}}
            disabled={disableUserDicountBtn}
          >
            Submit
          </button>
          <button className={styles.okBtn} onClick={confirmationPopupClose}>
            Cancel
          </button>
        </div>
      </div>
      <MatPopup
        className={styles.approveRejectPopup}
        open={discountOverridenPopup}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>
            When you deselect this checkbox, any user-specific discounts will be ignored, and discounts applicable at the company OR cohort levels will be applied to the user instead.
          </div>
          <button
            className={styles.okBtn}
            onClick={() => setDiscountOverridenPopup(false)}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <SubmitConfirmation/>
    </div>
  );
};

export default DiscountPopup;
