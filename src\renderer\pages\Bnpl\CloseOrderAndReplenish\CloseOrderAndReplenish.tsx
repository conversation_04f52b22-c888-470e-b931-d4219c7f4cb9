import { useImmer } from "use-immer";
import { useEffect, useState } from "react";
import { MenuItem, Select } from "@mui/material";
import ReactPaginate from "react-paginate";
import styles from "./CloseOrderAndReplenish.module.scss";
import Loader from "../../../components/common/Loader";
import { filterArrray, format2DecimalPlaces } from "../../../utils/helper";
import MatPopup from "../../../components/common/MatPopup";
import useGetAllBuyerOrderData from "../../../hooks/useGetAllBuyerOrderData";
import usePostCloseBuyerOrderData from "../../../hooks/usePostCloseBuyerOrderData";

const CloseOrderAndReplenish = () => {
  const [modifiedCancelOrderData, setModifiedCancelOrderData] = useImmer([]);
  const [filteredOrders, setFilteredOrders] = useImmer([]);

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [poLists, setPoLists] = useImmer<any>([]);
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [showPoLinesPopup, setShowPoLinesPopup] = useImmer(false);
  const [disabledSubmitPoLineBtn, setDisabledSubmitPoLineBtn] = useImmer(true);
  const [closePoLineData, setClosePoLineData] = useImmer<any>(null);
  const [isAllPoLineChecked, setIsAllPoLineChecked] = useImmer<boolean>(false);

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredOrders.length / itemsPerPage);

  const { data: closeBuyerOrders, isLoading: isCloseBuyerOrderDataLoading } =
    useGetAllBuyerOrderData();

  const {
    mutate: saveCloseBuyerOrder,
    data: saveCloseBuyerOrderData,
    isLoading: isSaveCloseBuyerOrderDataLoading,
  } = usePostCloseBuyerOrderData();

  useEffect(() => {
    if (closeBuyerOrders?.length >= 0) {
      const modifiedArr = closeBuyerOrders.map((obj: any) => ({
        ...obj,
        editMode: false,
      }));
      setModifiedCancelOrderData(modifiedArr);
      setFilteredOrders(modifiedArr);
    }
  }, [closeBuyerOrders]);

  useEffect(() => {
    if (saveCloseBuyerOrderData) {
      setApiResponseMessage(saveCloseBuyerOrderData);
    }
  }, [saveCloseBuyerOrderData]);

  const search = (event: any) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(event.target.value);
    if (event.target.value) {
      const _filterArrray = filterArrray(
        modifiedCancelOrderData,
        event.target.value,
        [
          "po_buyer_po_number",
          "po_buyer_internal_po",
          "po_buyer_po_price",
          "po_sales_tax",
          "buyer_total_price",
          "total_po_price",
          "buyer_company_name",
        ]
      );
      if (_filterArrray?.length) {
        setFilteredOrders(_filterArrray);
      } else {
        setFilteredOrders([]);
      }
    } else {
      setFilteredOrders(modifiedCancelOrderData);
    }
  };


  const confirmationPopupClose = () => {
    setShowPoLinesPopup(false);
    setClosePoLineData(null);
    setIsAllPoLineChecked(false);
  };

  const handlePageClick = (event: any) => {
    const newOffset = (event.selected * itemsPerPage) % filteredOrders.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  const displayPoLinesHandler = (poNumber: any, poLines: any) => {
    setShowPoLinesPopup(true);
    let createPoList = [];
    
    if(poLines.length === 1){
      createPoList = poLines.map((poLine: any) => {
        poLine.isChecked = true;
        return poLine
      })
      setDisabledSubmitPoLineBtn(false)
    }else{
      createPoList = poLines.map((poLine: any) => {
        if(poLine.isChecked === false) return poLine;
        poLine.isChecked = false;
        return poLine
      })
      setDisabledSubmitPoLineBtn(true)
    }
    setPoLists(createPoList)
    setClosePoLineData({
      "data": {
        "po_number": poNumber,
        "purchase_order_line_id": {}
      }
    })
  };

  const poLineChangeHandler = (event: any, i: number) => {
    setPoLists((prev: any) => {
      prev[i].isChecked = event.target.checked;
      const checkedPo = prev.filter((po: any) => po.isChecked === true);
      setDisabledSubmitPoLineBtn(checkedPo?.length < 1)
      setIsAllPoLineChecked(checkedPo.length === poLists?.length);
      return prev;
    });
  };

  const allPoLineChecked = (event: any) => {
    const isChecked = event.target.checked;
    setPoLists((prev: any) => {
      prev.forEach((po: any) => { po.isChecked = isChecked; });
      return prev;
    });
    setIsAllPoLineChecked(isChecked);
    setDisabledSubmitPoLineBtn(!isChecked);
  }
  const submitSelectedPoLineClose = () => {
    poLists.forEach((poList: any, i: number) => {
      if (poList.isChecked === true) {
        if(closePoLineData){
          closePoLineData.data.purchase_order_line_id = { ...closePoLineData.data.purchase_order_line_id, [poList.po_line]: poList.id }
        }
      }
    })
    if (closePoLineData) {
      saveCloseBuyerOrder(closePoLineData);
    }
    
    confirmationPopupClose();
  }

  return (
    <div>
      {isCloseBuyerOrderDataLoading ||
        isSaveCloseBuyerOrderDataLoading ? (
      <div className={styles.loaderImg}>
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <input
              className={styles.searchInput}
              type="text"
              onChange={search}
              value={inputSearchValue}
              placeholder="Search"
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>PO Number</th>
                  <th>Internal PO Number</th>
                  <th>Price</th>
                  <th>Sales Tax</th>
                  <th>Total Price</th>
                  <th>Main Company</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {filteredOrders?.length ? (
                  filteredOrders
                    .slice(itemOffset, endOffset)
                    .map((data: any, index: number) => (
                      <tr key={data.po_buyer_po_number + index}>
                        <td>{data.po_buyer_po_number}</td>
                        <td>{data.po_buyer_internal_po}</td>
                        <td>{format2DecimalPlaces(data.po_buyer_po_price)}</td>
                        <td>{format2DecimalPlaces(data.po_sales_tax)}</td>
                        <td>{format2DecimalPlaces(data.total_po_price)}</td>
                        <td>{data.buyer_company_name}</td>
                        <td>
                          <button
                            className={styles.buyerCancelOrderBtn}
                            onClick={() =>
                              displayPoLinesHandler(data.po_buyer_po_number, data.purchase_order_line)
                            }
                          >
                            Buyer Close order
                          </button>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={7} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
          <MatPopup
            className={styles.approveRejectPopup}
            open={!!apiResponseMessage}
          >
            <div className={styles.successfullyUpdated}>
              <div className={styles.successfullytext}>
                {apiResponseMessage}
              </div>
              <button
                className={styles.okBtn}
                onClick={() => setApiResponseMessage("")}
              >
                Ok
              </button>
            </div>
          </MatPopup>
          <MatPopup
            className={styles.orderContinuePopup}
            open={showPoLinesPopup}
          >
            <div className={styles.tblscrollPop}>
              <div className={styles.continuePopup}>
                <>
                {poLists.length > 1 ?
                  <p className={styles.continuetext}>Select the line and press yes to close</p>
                  :
                  <p className={styles.continuetext}>Press yes to close the order</p>
                }
                </> 
                <div className={styles.overFlowForPop}>
                <table>
                  <thead>

                    <tr>
                      <th>
                        {poLists.length > 1 &&
                          
                          <label className="containerChk">
                            <input type="checkbox" checked={isAllPoLineChecked} onChange={(e) => allPoLineChecked(e)} />
                            <span className="checkmark" />
                          </label>
                        }
                      </th>
                      <th>PO Line</th>
                      <th>Description</th>
                    </tr>

                  </thead>
                  <tbody>
                    {poLists.map((poList: any, i: number) => {
                      const lines = poList.description.split('\n');
                      const firstLine = lines[0];
                      const restLines = lines.slice(1);
                      return (
                        <tr key={poList.po_line}>
                          <td>
                            {poLists.length > 1 &&
                            <label className="containerChk">
                            <input type="checkbox" checked={poList.isChecked} onChange={(e) => poLineChangeHandler(e, i)} />
                            <span className="checkmark" />
                          </label>
                    }
                          </td>
                          <td>{poList.po_line}</td>
                          <td className={styles.listDescription}>
                            <p className={styles.lineHead}>{firstLine}</p>
                            {restLines.map((line: any, index: number) => ( <p key={index}>{line}</p> ))}
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
                </div>
                <div className={styles.yesAndnoBtn}>
                  <button className={styles.okBtn} onClick={submitSelectedPoLineClose} disabled= {disabledSubmitPoLineBtn}>
                    Yes
                  </button>
                  <button
                    className={styles.okBtn}
                    onClick={confirmationPopupClose}
                  >
                    No
                  </button>
                </div>
              </div>
            </div>

          </MatPopup>
        </div>
      )}
    </div>
  );
};

export default CloseOrderAndReplenish;
