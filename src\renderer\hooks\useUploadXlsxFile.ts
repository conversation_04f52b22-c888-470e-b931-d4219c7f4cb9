import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { reactQuery<PERSON>eys } from "../utils/constant";

const useUploadXlsxFile = () => {
  const queryClient = useQueryClient();
  return useMutation(async (xlsxFile: any) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_SERVICE}/reference-data/referenceProductUpload`,
        xlsxFile
      );

      queryClient.invalidateQueries([reactQueryKeys.getAllReferenceDataProduct]);
      queryClient.invalidateQueries([reactQueryKeys.getReferenceDataVersions]);

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data?.data
        ) {
          throw new Error(response.data.data.error_message);
          
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error.response?.data.message ?? "");
    }
  });
};

export default useUploadXlsxFile;
