import { useImmer } from "use-immer";
import { useContext, useEffect, useState } from "react";
import { MenuItem, Select } from "@mui/material";
import ReactPaginate from "react-paginate";
import styles from "./CloseOrder.module.scss";
import Loader from "../../../components/common/Loader";
import { filterArrray, format2DecimalPlaces } from "../../../utils/helper";
import MatPopup from "../../../components/common/MatPopup";
import useGetAchOrder from "../../../hooks/useGetAchOrder";
import useSaveBuyerCloseAchOrder from "../../../hooks/useSaveBuyerCloseAchOrder";
import { CommonCtx } from "../../AppContainer";
import { cloneDeep } from "lodash-es";

const CloseOrder = () => {
  const [modifiedAchOrders, setModifiedAchOrders] = useImmer([]);
  const [filteredAchOrders, setFilteredAchOrders] = useImmer([]);

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedPoData, setSelectedPoData] = useImmer<any>([]);
  const [showPoLinesPopup, setShowPoLinesPopup] = useImmer(false);
  const [disabledSubmitPoLineBtn, setDisabledSubmitPoLineBtn] = useImmer(true);
  const [isAllLineChecked, setIsAllLineChecked] = useImmer<boolean>(false);

  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredAchOrders.length / itemsPerPage);

  const {
    data: achOrderData,
    isLoading: isAchOrderLoading,
    isFetching: isAchOrderFetching,
  } = useGetAchOrder();

  const {
    mutate: saveBuyerCloseAchOrder,
    data: saveBuyerCloseAchOrderData,
    isLoading: isSaveBuyerCloseAchOrderLoading,
  } = useSaveBuyerCloseAchOrder();

  const showPopupFormAnyComponent = useContext(CommonCtx);

  useEffect(() => {
    if (achOrderData?.length >= 0) {
      const _achOrderData = achOrderData.map((obj: any) => ({
        ...obj,
        purchase_order_lines: obj.purchase_order_lines.map((line: any) => ({
          ...line,
          enteredAmount: "",
          isChecked: false,
        })),
      }));
      setModifiedAchOrders(_achOrderData);
      setFilteredAchOrders(_achOrderData);
    }
  }, [achOrderData]);

  useEffect(() => {
    if (saveBuyerCloseAchOrderData) {
      showPopupFormAnyComponent(saveBuyerCloseAchOrderData);
    }
  }, [saveBuyerCloseAchOrderData]);

  const search = (event: any) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(event.target.value);
    if (event.target.value) {
      const _filterArrray = filterArrray(
        modifiedAchOrders,
        event.target.value,
        [
          "payment_method",
          "sales_tax",
          "buyer_internal_po",
          "buyer_po_number",
          "buyer_po_price",
          "deposit_amount",
          "buyer_credit",
          "total_buyer_paid",
          "total_due_amount",
        ]
      );
      if (_filterArrray?.length) {
        setFilteredAchOrders(_filterArrray);
      } else {
        setFilteredAchOrders([]);
      }
    } else {
      setFilteredAchOrders(modifiedAchOrders);
    }
  };

  const hidePoLinesPopup = () => {
    setShowPoLinesPopup(false);
    setIsAllLineChecked(false);
    setSelectedPoData(null);
  };

  const handlePageClick = (event: any) => {
    const newOffset =
      (event.selected * itemsPerPage) % filteredAchOrders.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
  };

  const displayPoClosePopup = (data: any) => {
    setShowPoLinesPopup(true);
    const poLines = data.purchase_order_lines;

    if (poLines.length === 1) {
      poLines[0].isChecked = true;
      poLines[0].enteredAmount = poLines[0].due_amount;
      setDisabledSubmitPoLineBtn(false);
    } else {
      poLines.forEach((poLine: any) => {
        poLine.isChecked = false;
      });
      setDisabledSubmitPoLineBtn(true);
    }
    setSelectedPoData(data);
  };

  const poLineChangeHandler = (event: any, i: number) => {
    setSelectedPoData((prev: any) => {
      const _prev = cloneDeep(prev);
      _prev.purchase_order_lines[i].isChecked = event.target.checked;
      _prev.purchase_order_lines[i].enteredAmount = _prev.purchase_order_lines[i].due_amount;

      if (!event.target.checked) {
        _prev.purchase_order_lines[i].enteredAmount = "";
      }

      const checkedPo = _prev.purchase_order_lines.filter(
        (po: any) => po.isChecked === true
      );
      const isAnyInvalidAmount = _prev.purchase_order_lines.some(
        (po: any) => po.isChecked === true && Number(po.enteredAmount) <= 0
      );
      setDisabledSubmitPoLineBtn(checkedPo?.length < 1 || isAnyInvalidAmount);
      setIsAllLineChecked(
        checkedPo.length === selectedPoData?.purchase_order_lines?.length
      );
      return _prev;
    });
  };

  const allPoLineChecked = (event: any) => {
    const isChecked = event.target.checked;

    if (isChecked) {
      setSelectedPoData((prev: any) => {
        const _prev = cloneDeep(prev);
        _prev.purchase_order_lines.forEach((line: any) => {
          line.enteredAmount = line.due_amount;
        });
        return _prev;
      });
    } else {
      setPoLineAmount(null, "");
    }

    setSelectedPoData((prev: any) => {
      const _prev = cloneDeep(prev);
      _prev.purchase_order_lines.forEach((po: any) => {
        po.isChecked = isChecked;
      });
      return _prev;
    });
    setIsAllLineChecked(isChecked);
    setDisabledSubmitPoLineBtn(!isChecked);
  };

  const submitSelectedPoLineClose = () => {
    const purchaseOrderLine: any = [];

    selectedPoData.purchase_order_lines.forEach((poLine: any) => {
      if (poLine.isChecked === true) {
        purchaseOrderLine.push({
          id: poLine.id,
          po_line: poLine.po_line,
          buyer_paid_amt: poLine.enteredAmount,
        });
      }
    });

    saveBuyerCloseAchOrder({
      po_number: selectedPoData.buyer_po_number,
      purchase_order_line: purchaseOrderLine,
    });

    hidePoLinesPopup();
  };

  const handleAmountChange = (event: any, index: number) => {
    const amount = Number(event.target.value);


    setSelectedPoData((prev: any) => {
      const _prev = cloneDeep(prev);
      if (_prev.purchase_order_lines[index]) {
        _prev.purchase_order_lines[index].enteredAmount = amount >= 0 ? event.target.value : "";
      }

      const checkedPo = _prev.purchase_order_lines.filter((po: any) => po.isChecked === true);
      const isAnyInvalidAmount = _prev.purchase_order_lines.some((po: any) => po.isChecked === true && (isNaN(amount) || Number(po.enteredAmount) <= 0));
      setDisabledSubmitPoLineBtn(checkedPo.length < 1 || isAnyInvalidAmount);
      return _prev;
    });
  };

  const setPoLineAmount = (index: any = null, amount: number | string) => {
    if (index != null && index >= 0) {
      setSelectedPoData((prev: any) => {
        const _prev = cloneDeep(prev);
        if (_prev.purchase_order_lines[index]) {
          _prev.purchase_order_lines[index].enteredAmount = amount;
        }
        return _prev;
      });
    } else {
      setSelectedPoData((prev: any) => {
        const _prev = cloneDeep(prev);
        _prev?.purchase_order_lines?.forEach((line: any) => {
          line.enteredAmount = amount;
        });
        return _prev;
      });
    }
  };

  return (
    <div className="contentMain">
      {isAchOrderLoading ||
      isSaveBuyerCloseAchOrderLoading ||
      isAchOrderFetching ? (
        <div className="loaderImg">
          <Loader />
        </div>
      ) : (
        <div>
          <div className={styles.searchBox}>
            <Select
              className={styles.showdropdwn}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <input
              className={styles.searchInput}
              type="text"
              onChange={search}
              value={inputSearchValue}
              placeholder="Search"
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>PO Number</th>
                  <th>Internal PO</th>
                  <th>Payment Method</th>
                  <th>Sales Tax</th>
                  <th>Buyer PO Price</th>
                  <th>Deposit Amount</th>
                  <th>Buyer Credit</th>
                  <th>Total Buyer Paid</th>
                  <th>Amt To Be Paid</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {filteredAchOrders?.length ? (
                  filteredAchOrders
                    .slice(itemOffset, endOffset)
                    .map((data: any, index: number) => (
                      <tr key={data.buyer_po_number + index}>
                        <td>{data.buyer_po_number}</td>
                        <td>{data.buyer_internal_po}</td>
                        <td>{data.payment_method}</td>
                        <td>{format2DecimalPlaces(data.sales_tax)}</td>
                        <td>{format2DecimalPlaces(data.buyer_po_price)}</td>
                        <td>{format2DecimalPlaces(data.deposit_amount)}</td>
                        <td>{format2DecimalPlaces(data.buyer_credit)}</td>
                        <td>{format2DecimalPlaces(data.total_buyer_paid)}</td>
                        <td>{format2DecimalPlaces(data.total_due_amount)}</td>
                        <td>
                          <button
                            className={styles.buyerCancelOrderBtn}
                            onClick={() => displayPoClosePopup(data)}
                          >
                            Close order
                          </button>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={6} className={"noDataFoundTd"}>
                      No data found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={pageCount}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={pageCount > 0 ? currentPage : -1}
            />
          </div>
          <MatPopup
            className={styles.orderContinuePopup}
            open={showPoLinesPopup}
          >
            {selectedPoData?.purchase_order_lines && (
              <div className={styles.tblscrollPop}>
                <div className={styles.continuePopup}>
                  <>
                    <p className={styles.continuetext}>
                      {(selectedPoData.purchase_order_lines.length > 1 ? "Select line to enter the Amount you are Paying" : "Press yes to close the order")}
                    </p>
                  </>
                  <div className={styles.overFlowForPop}>
                    <table>
                      <thead>
                        <tr>
                          <th>
                            {selectedPoData.purchase_order_lines.length > 1 && (
                              <label className="containerChk">
                                <input
                                  type="checkbox"
                                  checked={isAllLineChecked}
                                  onChange={(e) => allPoLineChecked(e)}
                                />
                                <span className="checkmark" />
                              </label>
                            )}
                          </th>
                          <th>PO Line</th>
                          <th>Description</th>
                          <th>Buyer Paid</th>
                          <th>Price</th>
                          <th>Deposit Amount</th>
                          <th>Amt To Be Paid</th>
                          <th>Amount Paying</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedPoData.purchase_order_lines.map(
                          (poLine: any, i: number) => {
                            const lines = poLine.description.split("\n");
                            const firstLine = lines[0];
                            const restLines = lines.slice(1);
                            return (
                              <tr key={poLine.po_line}>
                                <td>
                                  {selectedPoData.purchase_order_lines.length >
                                    1 && (
                                    <label className="containerChk">
                                      <input
                                        type="checkbox"
                                        checked={poLine.isChecked}
                                        onChange={(e) =>
                                          poLineChangeHandler(e, i)
                                        }
                                      />
                                      <span className="checkmark" />
                                    </label>
                                  )}
                                </td>
                                <td>{poLine.po_line}</td>
                                <td className={styles.listDescription}>
                                  <p className={styles.lineHead}>{firstLine}</p>
                                  {restLines.map((line: any, index: number) => (
                                    <p key={index}>{line}</p>
                                  ))}
                                </td>
                                <td>{poLine.buyer_paid}</td>
                                <td>{poLine.price}</td>
                                <td>{poLine.deposit_amount}</td>
                                <td>{poLine.due_amount}</td>
                                <td>
                                  {selectedPoData.purchase_order_lines.length >
                                    0 && (
                                    <input
                                      type="text"
                                      className={styles.closePopupInput}
                                      onChange={(e) => handleAmountChange(e, i)}
                                      disabled={!poLine.isChecked}
                                      value={poLine.enteredAmount}
                                    />
                                  )}
                                </td>
                              </tr>
                            );
                          }
                        )}
                      </tbody>
                    </table>
                  </div>
                  <div className={styles.yesAndnoBtn}>
                    <button
                      className={styles.okBtn}
                      onClick={submitSelectedPoLineClose}
                      disabled={disabledSubmitPoLineBtn}
                    >
                      Yes
                    </button>
                    <button className={styles.okBtn} onClick={hidePoLinesPopup}>
                      No
                    </button>
                  </div>
                </div>
              </div>
            )}
          </MatPopup>
        </div>
      )}
    </div>
  );
};

export default CloseOrder;
