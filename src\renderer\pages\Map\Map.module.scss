.mainContent {

  .addCompanyTitle {
    font-size: 20px;
    color: #000;
    line-height: normal;
    margin-top: 0px;
    font-weight: 600;
  }

  .addCompanyTitle1 {
    font-size: 20px;
    color: #000;
    line-height: normal;
    margin-top: 12px;
    font-weight: 600;
    border-top: 1px solid #eee;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .topSeaction {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    input {
      margin-left: auto;
      height: 34px;
      padding: 6px 12px;
      font-size: 14px;
      color: #343a40;
    }
  }

  .addCompanyBtn {
    background-color: #343a40;
    color: #fff;
    font-size: 14px;
    height: 34px;
    padding: 6px 24px;
    border-radius: 4px;
    border: 0px;
    cursor: pointer;
  }

  .certStateList {
    display: flex;
    flex-wrap: wrap;
    column-gap: 8px;
    margin-bottom: 12px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    padding-top: 10px;
    padding-bottom: 10px;
    height: 175px;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      box-shadow:none;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
    }
  }

  .certTile {
    width: auto;
    padding-bottom: 12px;

    .certBox {
      border-radius: 8px;
      background: #50C878;
      padding: 4px 9px;
         
      &.certExpire {
        background: #ff5d47;
      }

      .certHeader {
        display: flex;
        align-items: center;
        justify-content: end;
        color: var(--primaryColor);
        font-size: 16px;
        padding: 0px 2px;
        text-align: center;

        svg {
          cursor: pointer;
          margin-left: 6px;

          path {
            fill: var(--primaryColor);
            stroke: var(--primaryColor);
          }
        }
      }

      .contentMain {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;

        a {
          font-size: 16px;
          text-decoration: none;
          color: #343a40;

          svg {
            width: 32px;
            height: 32px;
          }
        }

      }
    }
  }

  .mapContent {
    width: 500px;
    height: 380px;
    border: 1px solid #eee;
    margin:0 auto;

  }

  .closePopupBtn {
    position: absolute;
    right: 12px;
    top: 12px;
    z-index: 2;
    cursor: pointer;
    background-color: transparent;
    padding: 0px;
    border: 0px;

    svg {
      path {
        fill: #000;
      }
    }
  }


  .continuePopup {
    padding: 20px;
    text-align: center;
    width: 400px;

    .companyNameInput {
      box-shadow: none;
      outline: none;
      width: 100%;
      height: 42px;
      padding: 6px 12px;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.5;
      color: #495057;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
    }

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 310px;
    }

    .addCompanyTitle {
      font-size: 20px;
      color: #000;
      line-height: normal;
      margin-top: 0px;
      margin-top: 12px;
      font-weight: 600;
    }

    
    .continuetext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: #2a3f54;
    }

    .yesAndnoBtn {
      display: flex;
      gap: 16px;
      margin-top: 16px;

      .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: #2a3f54;
        color: #fff;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

    }
  }
}


.orderContinuePopup {
  h2 {
    display: none;
  }

  .continuePopup {
    padding: 20px;
    text-align: center;
    width: 400px;

    .companyNameInput {
      box-shadow: none;
      outline: none;
      width: 100%;
      height: 42px;
      padding: 6px 12px;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.5;
      color: #495057;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
    }

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 310px;
    }

    .addCompanyTitle {
      font-size: 20px;
      color: #000;
      line-height: normal;
      margin-top: 0px;
      margin-top: 12px;
      font-weight: 600;
    }

    .continuetext {
      text-align: center;
      font-size: 20px;
      margin-top: 0px;
      margin-bottom: 24px;
      color: #2a3f54;
      &.continuetext1{
        margin-top: 0px;
        margin-bottom: 0px;
        font-weight: bold;
      }  
    }

    .yesAndnoBtn {
      display: flex;
      gap: 16px;
      margin-top: 16px;

      .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: #2a3f54;
        color: #fff;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

    }
  }

}

.addCertificatePopup {

  .stateStyle {
    width: 100%;
    border: 1px solid #eee;
    padding: 12px;
    overflow: auto;
    height: 400px;
    margin-top: 20px;
    margin-bottom: 20px;
    gap: 8px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      box-shadow:none;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
    }

    .highlightState {
      background-color: #343a40;
      color: #fff;
    }

    .resaleCertList {
      border: 1px solid #343a40;
      padding: 10px;
      display: inline-flex;
      flex-direction: column;
      justify-content: center;
      align-items: baseline;
      border-radius: 4px;
      cursor: pointer;

      span {
        font-size: 14px;
      }

      &.disabledCert {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .showdropdwn {
    width: 100%;
  }

  .submitBtnMain {
    margin-top: 20px;

    .submitBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: #2a3f54;
      color: #fff;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

}

.lblCert {
  color: var(--primaryColor);
  font-size: 16px;
  margin-right: 20px;
  font-weight: 600;
}

.uplaodCertMain {
  margin-bottom: 20px;

  input {
    display: none;
  }

  .uplaodCertBtn {
    display: inline-block;
    background-color: var(--primaryColor);
    color: white;
    padding: 6px 12px;
    border-radius: 0.3rem;
    cursor: pointer;
    margin-top: 1rem;
  }

  .uplaodCertName {
    color: var(--primaryColor);
    font-size: 14px;
    margin-left: 20px;
  }
}

.selectDropdownMain {
  display: flex;
  align-items: center;

  .showdropdwn {
    flex: 1;
    max-width: 70%;
  }
}

.statesTooltip.statesTooltip {
  .tooltip.tooltip {
    font-size: 14px;
    margin-top: 4px;
  }
}