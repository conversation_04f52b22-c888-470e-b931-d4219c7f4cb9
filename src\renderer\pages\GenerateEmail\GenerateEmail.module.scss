.generateEmailPage {
  border: 1px solid #f1f1f1;
  padding: 24px;
  width: 100%;
  max-width: 600px;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 7px;

  .InputFieldcss {
    // display: block;
    width: 100%;
    height: 40px;
    padding: 8px 12px;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 5px;
    margin-bottom: 10px;

    &.MuiInputBase-root {
      &:hover {
        .MuiSelect-select {
          border-color: transparent;
        }

        fieldset {
          border-color: #397aff;
          border-width: 1px;
        }
      }

      &.Mui-focused {
        fieldset {
          border-color: #397aff;
          border-width: 1px;
          background-color: rgba(57, 122, 255, 0.1);
        }

        .MuiSelect-select {
          color: #397aff;
        }
      }
    }
  }

  .emailText {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
  }

  .checkBox {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    cursor: pointer;
  }

  .InputFieldPass {
    width: 60%;
    height: 40px;
    padding: 10px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    margin: 15px;
    margin-left: 55px;
  }

  label {
    margin-right: 16px;
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
  }

  .loginBtn {
    width: 90px;
    height: 35px;
    color: #fff;
    background-color: var(--primaryColor);
    border-radius: 5px;
    cursor: pointer;
    margin-top: 12px;

    &:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
  }
}

.searchBox {
  margin-bottom: 15px;

  .showdropdwn {
    width: 82px;
    height: 38px;
    padding: 4px;
  }

  .searchInput {
    box-shadow: none;
    outline: none;
    width: 238px;
    height: 38px;
    padding: 6px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 100%;
      margin-bottom: 5px;
    }

  }
}

.generateEmailBtn {

  height: 38px;
  display: inline-block;
  text-align: center;
  padding: 6px;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  background-color: #343a40;
  font-size: 15px;
  color: #fff;
  cursor: pointer;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;

  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }

  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 100%;
  }

}

.approveRejectPopup {
  h2 {
    display: none;
  }

  .successfullyUpdated {
    padding: 20px;
    text-align: center;
    width: 300px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 240px;
    }

    .successfullytext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);
    }

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
    }

  }
}

.updateQuantityBtn {
  display: inline-block;
  text-align: center;
  padding: 6px 17px;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  background-color: #343a40;
  font-size: 15px;
  color: #fff;
  cursor: pointer;
  margin-right: 3px;

}

.updateQuantityinput {
  height: 31px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  margin-right: 2px;
}

.loaderImg {

  text-align: center;
  margin-left: auto;
  margin-right: auto;
  margin-top: 200px;

}

.orderContinuePopup {
  h2 {
    display: none;
  }
}

.continuePopup {
  padding: 20px;
  text-align: center;
  width: 300px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 240px;
  }

  .continuetext {
    text-align: center;
    font-size: 20px;
    margin-bottom: 24px;
    color: var(--primaryColor);
  }

  .yesAndnoBtn {
    display: flex;
    gap: 10px;

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      gap: 8px;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
    }

  }
}