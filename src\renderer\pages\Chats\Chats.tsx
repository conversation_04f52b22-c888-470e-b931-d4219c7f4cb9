import { useEffect, useState, useRef } from "react";
import useGetChats from "../../hooks/useGetChats";
import useGetAllChats from "../../hooks/useGetAllChats";
import Loader from "../../components/common/Loader";
import { Select, MenuItem, SelectChangeEvent, IconButton, Tooltip, Menu, ListItemIcon, ListItemText } from "@mui/material";
import styles from "./Chats.module.scss";
import usePostStartChat from "../../hooks/usePostStartChat";
import { normalizeMessage, cleanContentEditableHtml } from "./chatUtils";
import AttachmentBubble from "./AttachmentBubble/AttachmentBubble";
import MessageBubble from "./MessageBubble/MessageBubble";
import clsx from "clsx";

// Add these imports for the formatting icons
import FormatBoldIcon from '@mui/icons-material/FormatBold';
import FormatItalicIcon from '@mui/icons-material/FormatItalic';
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined';

// Add these imports for the message menu
import MoreVertIcon from '@mui/icons-material/MoreVert';
import BlockIcon from '@mui/icons-material/Block';
import DeleteIcon from '@mui/icons-material/Delete';

const ATTACHMENT = 'attachment';

const Chats = () => {
    const { data: chatsData, isLoading: isChatsLoading } = useGetAllChats();
    const [allChats, setAllChats] = useState<any[]>([]);
    const [selectedChat, setSelectedChat] = useState<string>("");
    const {mutateAsync: startChat} = usePostStartChat();
    const [chatMessages, setChatMessages] = useState<any[]>([]);
    
    // New state for message input
    const [messageText, setMessageText] = useState("");
    const contentEditableRef = useRef<HTMLDivElement>(null);

    // State for message menu
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);
    const open = Boolean(anchorEl);

    useEffect(() => { 
        if (chatsData) {
            const mappedChats = chatsData.map(item => ({
                poNumber: item.reference_id
            }));
            setAllChats(mappedChats);
        }
    }, [chatsData]);

    useEffect(() => {
        if (selectedChat) {
            console.log('Selected chatId:', selectedChat);
            getChatDetails(selectedChat);
        }
    }, [selectedChat]);

    const getChatDetails = async (poNumber: string) => {
        const rawMessages = await startChat({reference_id: poNumber});
        console.log('Channel data:', rawMessages);
        console.log('Channel data chats:', rawMessages?.chats);
        if (rawMessages && rawMessages.chats?.length > 0) {
            const messages = rawMessages.chats.map(message=>normalizeMessage(message, 0, 'moderator'));
            console.log('messages:', messages)
            setChatMessages(messages);
        }
    }

    const handleChatChange = (event: SelectChangeEvent<string>) => {
        const selectedPoNumber = event.target.value;
        setSelectedChat(selectedPoNumber);
    };

    // New functions for text formatting
    const applyFormatting = (command: string) => {
        document.execCommand(command, false);
        contentEditableRef.current?.focus();
    };

    const handleContentChange = () => {
        if (contentEditableRef.current) {
            setMessageText(contentEditableRef.current.innerHTML);
        }
    };

    const handleSendMessage = () => {
        if (!messageText.trim()) return;
        
        // Clean the HTML content
        const cleanedContent = cleanContentEditableHtml(messageText);
        
        // Here you would add code to send the message
        console.log('Sending message:', cleanedContent);
        
        // Clear the input field
        setMessageText("");
        if (contentEditableRef.current) {
            contentEditableRef.current.innerHTML = "";
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    // Message menu handlers
    const handleMenuClick = (event: React.MouseEvent<HTMLElement>, messageId: string) => {
        setAnchorEl(event.currentTarget);
        setSelectedMessageId(messageId);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
        setSelectedMessageId(null);
    };

    const handleBanUser = () => {
        if (selectedMessageId) {
            console.log('Banning user for message:', selectedMessageId);
            // Add your ban user logic here
        }
        handleMenuClose();
    };

    const handleDeleteMessage = () => {
        if (selectedMessageId) {
            console.log('Deleting message:', selectedMessageId);
            // Add your delete message logic here
            setChatMessages(prev => prev.filter(msg => msg.id !== selectedMessageId));
        }
        handleMenuClose();
    };

    return (
        <div className="contentMain">
            <h1>Chats</h1>
            {isChatsLoading ? (
                <div className="loaderImg">
                    <Loader />
                </div>
            ) : (
                <div>
                    <p>Select a PO Number to open chat:</p>
                    <Select
                        className="editLinesDropdown"
                        MenuProps={{
                            classes: {
                                paper: styles.Dropdownpaper,
                                list: styles.muiMenuList,
                            }
                        }}
                        value={selectedChat}
                        onChange={handleChatChange}
                        displayEmpty
                    >
                        <MenuItem disabled value="">
                            <em>Select a PO Number</em>
                        </MenuItem>
                        {allChats.map((chat) => (
                            <MenuItem key={chat.poNumber} value={chat.poNumber}>
                                {chat.poNumber}
                            </MenuItem>
                        ))}
                    </Select>
                </div>
            )}
            { selectedChat && chatMessages.length > 0 ? (
                <div>
                    <h2>Chat with {selectedChat}</h2>
                    <div className={styles.messagesContainer}>
                        {chatMessages.map((message) => (
                            <div key={message.id} className={clsx(styles.message, message.isMyMessage ? styles.myMessage : styles.othersMessage)}>
                                <div className={styles.messageHeader}>
                                    <div className={styles.messageTimestamp}>{message.formattedTimestamp}</div>
                                    <div className={styles.messageActions}>
                                        <IconButton
                                            size="small"
                                            onClick={(e) => handleMenuClick(e, message.id)}
                                            className={styles.menuButton}
                                        >
                                            <MoreVertIcon fontSize="small" />
                                        </IconButton>
                                    </div>
                                </div>
                                {message.type === ATTACHMENT ? (
                                    <AttachmentBubble
                                        name={message.name}
                                        extension={message.extension}
                                        url={message.url}
                                        isMyMessage={message.isMyMessage}
                                        isImgix={false}
                                    />
                                ) : (
                                    <MessageBubble
                                        text={message.text}
                                        isMyMessage={message.isMyMessage}
                                    />
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Message actions menu */}
                    <Menu
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleMenuClose}
                        anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                        }}
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                    >
                        <MenuItem onClick={handleBanUser}>
                            <ListItemIcon>
                                <BlockIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText>Ban User</ListItemText>
                        </MenuItem>
                        <MenuItem onClick={handleDeleteMessage}>
                            <ListItemIcon>
                                <DeleteIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText>Delete Message</ListItemText>
                        </MenuItem>
                    </Menu>

                    {/* New message input area with formatting options */}
                    <div className={styles.messageInputContainer}>
                        <div className={styles.formattingToolbar}>
                            <Tooltip title="Bold">
                                <IconButton onClick={() => applyFormatting('bold')}>
                                    <FormatBoldIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title="Italic">
                                <IconButton onClick={() => applyFormatting('italic')}>
                                    <FormatItalicIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title="Underline">
                                <IconButton onClick={() => applyFormatting('underline')}>
                                    <FormatUnderlinedIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                        <div 
                            ref={contentEditableRef}
                            className={styles.contentEditable}
                            contentEditable
                            onInput={handleContentChange}
                            onKeyDown={handleKeyDown}
                            placeholder="Type a message..."
                        />
                        <button 
                            className={styles.sendButton}
                            onClick={handleSendMessage}
                            disabled={!messageText.trim()}
                        >
                            Send
                        </button>
                    </div>
                </div>
            ):(<></>)}
        </div>
    );
};

export default Chats;
