import React, { useState, useCallback, useContext } from 'react';
import { CircularProgress } from '@mui/material';
import clsx from 'clsx';
import styles from './ReferenceDataUpload.module.scss';
import FileDrop from '../../components/common/FileDrop';
import useUploadReferenceData from '../../hooks/useUploadReferenceData';
import { CommonCtx } from '../AppContainer';

interface FileUploadState {
  file: File | null;
  isSelected: boolean;
  error: string | null;
  isUploading: boolean;
  uploadSuccess: boolean;
}

const ReferenceDataUpload = () => {
  const showPopupFormAnyComponent = useContext(CommonCtx);

  // Single hook for API calls
  const {
    mutate: uploadReferenceData,
    isLoading: isUploading,
    data: uploadData,
    error: uploadError
  } = useUploadReferenceData();

  // State for both upload sections
  const [productUpload, setProductUpload] = useState<FileUploadState>({
    file: null,
    isSelected: false,
    error: null,
    isUploading: false,
    uploadSuccess: false
  });

  const [pricingUpload, setPricingUpload] = useState<FileUploadState>({
    file: null,
    isSelected: false,
    error: null,
    isUploading: false,
    uploadSuccess: false
  });

  // Handle file selection for product sheet
  const handleProductFileSelect = useCallback(({ file }: { file: File }) => {
    setProductUpload(prev => ({
      ...prev,
      file,
      isSelected: true,
      error: null,
      uploadSuccess: false
    }));
  }, []);

  // Handle file selection for pricing sheet
  const handlePricingFileSelect = useCallback(({ file }: { file: File }) => {
    setPricingUpload(prev => ({
      ...prev,
      file,
      isSelected: true,
      error: null,
      uploadSuccess: false
    }));
  }, []);

  // Handle file upload errors
  const handleFileError = useCallback((
    { message }: { file: File; message: string; code: number },
    type: 'search' | 'pricing'
  ) => {
    if (type === 'search') {
      setProductUpload(prev => ({
        ...prev,
        error: message,
        file: null,
        isSelected: false
      }));
    } else {
      setPricingUpload(prev => ({
        ...prev,
        error: message,
        file: null,
        isSelected: false
      }));
    }
  }, []);

  // Remove selected file
  const removeFile = useCallback((type: 'search' | 'pricing') => {
    if (type === 'search') {
      setProductUpload({
        file: null,
        isSelected: false,
        error: null,
        isUploading: false,
        uploadSuccess: false
      });
    } else {
      setPricingUpload({
        file: null,
        isSelected: false,
        error: null,
        isUploading: false,
        uploadSuccess: false
      });
    }
  }, []);

  // Toggle selection checkbox
  const toggleSelection = useCallback((type: 'search' | 'pricing') => {
    if (type === 'search') {
      setProductUpload(prev => ({
        ...prev,
        isSelected: !prev.isSelected
      }));
    } else {
      setPricingUpload(prev => ({
        ...prev,
        isSelected: !prev.isSelected
      }));
    }
  }, []);

  // Format file size
  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    const selectedFiles: Array<{ file: File; type: 'search' | 'pricing' }> = [];

    if (productUpload.isSelected && productUpload.file) {
      selectedFiles.push({ file: productUpload.file, type: 'search' });
    }

    if (pricingUpload.isSelected && pricingUpload.file) {
      selectedFiles.push({ file: pricingUpload.file, type: 'pricing' });
    }

    if (selectedFiles.length === 0) {
      showPopupFormAnyComponent('Please select at least one file to upload.', 'Ok');
      return;
    }

    try {
      // Update uploading state for selected files
      if (productUpload.isSelected) {
        setProductUpload(prev => ({ ...prev, isUploading: true }));
      }
      if (pricingUpload.isSelected) {
        setPricingUpload(prev => ({ ...prev, isUploading: true }));
      }

      // Upload files sequentially: product first, then pricing
      for (const { file, type } of selectedFiles) {
        const formData = new FormData();
        formData.append('file', file);

        await new Promise<void>((resolve, reject) => {
          uploadReferenceData({ formData, file_type: type }, {
            onSuccess: (data) => {
              if (type === 'search') {
                setProductUpload({
                  file: null,
                  isSelected: false,
                  error: null,
                  isUploading: false,
                  uploadSuccess: false
                });
                showPopupFormAnyComponent('Search sheet uploaded successfully!', 'Ok');
              } else {
                setPricingUpload({
                  file: null,
                  isSelected: false,
                  error: null,
                  isUploading: false,
                  uploadSuccess: false
                });
                showPopupFormAnyComponent('Pricing sheet uploaded successfully!', 'Ok');
              }
              resolve();
            },
            onError: (error: any) => {
              if (type === 'search') {
                setProductUpload(prev => ({
                  ...prev,
                  isUploading: false,
                  error: error.message || 'Upload failed'
                }));
              } else {
                setPricingUpload(prev => ({
                  ...prev,
                  isUploading: false,
                  error: error.message || 'Upload failed'
                }));
              }
              reject(error);
            }
          });
        });
      }

      // Show final success message if multiple files were uploaded
      if (selectedFiles.length > 1) {
        showPopupFormAnyComponent('All files uploaded successfully!', 'Ok');
      }
    } catch (error: any) {
      showPopupFormAnyComponent(`Upload failed: ${error.message || 'Unknown error'}`, 'Ok');
    }
  }, [productUpload, pricingUpload, uploadReferenceData, showPopupFormAnyComponent]);

  // Clear all selections
  const handleClear = useCallback(() => {
    setProductUpload({
      file: null,
      isSelected: false,
      error: null,
      isUploading: false,
      uploadSuccess: false
    });
    setPricingUpload({
      file: null,
      isSelected: false,
      error: null,
      isUploading: false,
      uploadSuccess: false
    });
  }, []);

  const selectedCount = (productUpload.isSelected ? 1 : 0) + (pricingUpload.isSelected ? 1 : 0);
  const isAnyUploading = productUpload.isUploading || pricingUpload.isUploading;
  const hasAnyFiles = productUpload.file || pricingUpload.file;

  return (
    <div className={styles.referenceDataUpload}>
      
      <div className={styles.header}>
        <h1>Reference Data Upload</h1>

        <p className={styles.referenceDataUploadNote}>
          Please Note: By uploading your file, you confirm that it matches the expected template and column layout exactly. We do not perform any structural validation on uploaded files. If the format has changed, processing may fail or produce unexpected results.
        </p> 
        
        <p>Upload product search sheet and pricing sheet to update reference data. You can select either or both types of files to upload.</p>
      </div>

      <div className={styles.uploadSections}>
        {/* Product Sheet Upload Section */}
        <div className={clsx(
          styles.uploadSection,
          productUpload.isSelected && styles.selected,
          isAnyUploading && styles.disabled
        )}>
          <div className={styles.sectionHeader}>
            <h3>Search Sheet</h3>
          </div>
          
          <div className={styles.description}>
            Upload an Excel file containing search data. Supported formats: .xlsx, .xls
          </div>

          {productUpload.isUploading ? (
            <div className={styles.loadingContainer}>
              <CircularProgress size={20} />
              <span>Uploading search sheet...</span>
            </div>
          ) : productUpload.file ? (
            <div className={styles.fileInfo}>
              <div className={styles.fileName}>{productUpload.file.name}</div>
              <div className={styles.fileSize}>{formatFileSize(productUpload.file.size)}</div>
              <button
                className={styles.removeFile}
                onClick={() => removeFile('search')}
                disabled={isAnyUploading}
              >
                Remove file
              </button>
            </div>
          ) : (
            <FileDrop
              title="Drag and Drop Search Sheet"
              label="Drag and drop your product search file here or"
              buttonLabel="Browse Files"
              types={['.xlsx', '.xls']}
              success={handleProductFileSelect}
              error={(errorData: { file: File; message: string; code: number }) => handleFileError(errorData, 'search')}
              isDataAvailable={!!productUpload.file}
            />
          )}

          {productUpload.error && (
            <div className={styles.errorMessage}>{productUpload.error}</div>
          )}

          {productUpload.uploadSuccess && (
            <div className={styles.successMessage}>Product sheet uploaded successfully!</div>
          )}
        </div>

        {/* Pricing Sheet Upload Section */}
        <div className={clsx(
          styles.uploadSection,
          pricingUpload.isSelected && styles.selected,
          isAnyUploading && styles.disabled
        )}>
          <div className={styles.sectionHeader}>
            <h3>Pricing Sheet</h3>
          </div>
          
          <div className={styles.description}>
            Upload an Excel file containing pricing data. Supported formats: .xlsx, .xls
          </div>

          {pricingUpload.isUploading ? (
            <div className={styles.loadingContainer}>
              <CircularProgress size={20} />
              <span>Uploading pricing sheet...</span>
            </div>
          ) : pricingUpload.file ? (
            <div className={styles.fileInfo}>
              <div className={styles.fileName}>{pricingUpload.file.name}</div>
              <div className={styles.fileSize}>{formatFileSize(pricingUpload.file.size)}</div>
              <button
                className={styles.removeFile}
                onClick={() => removeFile('pricing')}
                disabled={isAnyUploading}
              >
                Remove file
              </button>
            </div>
          ) : (
            <FileDrop
              title="Drag and Drop Pricing Sheet"
              label="Drag and drop your pricing file here or"
              buttonLabel="Browse Files"
              types={['.xlsx', '.xls']}
              success={handlePricingFileSelect}
              error={(errorData: { file: File; message: string; code: number }) => handleFileError(errorData, 'pricing')}
              isDataAvailable={!!pricingUpload.file}
            />
          )}

          {pricingUpload.error && (
            <div className={styles.errorMessage}>{pricingUpload.error}</div>
          )}

          {pricingUpload.uploadSuccess && (
            <div className={styles.successMessage}>Pricing sheet uploaded successfully!</div>
          )}
        </div>
      </div>

      <div className={styles.submitSection}>
        <div className={styles.selectionInfo}>
          <span className={styles.count}>{selectedCount}</span> file(s) selected for upload
          {selectedCount === 2 && (
            <span style={{ color: '#1c40e7', fontWeight: 600, marginLeft: '10px' }}>
              (Will upload search first, then pricing)
            </span>
          )}
        </div>
        
        <div className={styles.actionButtons}>
          <button
            className={styles.clearButton}
            onClick={handleClear}
            disabled={isAnyUploading || !hasAnyFiles}
          >
            Clear All
          </button>
          
          <button
            className={styles.submitButton}
            onClick={handleSubmit}
            disabled={selectedCount === 0 || isAnyUploading}
          >
            {isAnyUploading ? (
              <>
                <CircularProgress size={16} color="inherit" style={{ marginRight: 8 }} />
                Uploading...
              </>
            ) : (
              `Upload ${selectedCount > 0 ? selectedCount : ''} File${selectedCount !== 1 ? 's' : ''}`
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReferenceDataUpload;
