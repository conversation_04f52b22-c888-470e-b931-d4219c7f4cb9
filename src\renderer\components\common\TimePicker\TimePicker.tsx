import * as React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { TextField } from '@mui/material';

// Define the TimePickerValue component
const TimePickerComponent: React.FC = () => {
  const [value, setValue] = React.useState<Dayjs | null>(dayjs('2022-04-17T15:30'));

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DemoContainer components={['TimePicker']}>
      <TimePicker
          value={value}
          className="timePicker"
          onChange={(newValue) => setValue(newValue)}
          views={['hours']} // Only show hours (hide minutes)
          renderInput={(params) => <TextField disabled {...params} placeholder="Select hour" />}
        />
      </DemoContainer>
    </LocalizationProvider>
  );
};

export default TimePickerComponent;
