import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import InputField from "../../components/common/InputField";
import { useEffect } from "react";
import useGetSettings from "../../hooks/useGetSettings";
import usePostSettings from "../../hooks/usePostSettings";
import Loader from "../../components/common/Loader";
import styles from "./Setting.module.scss";

const AutoCancellationDaysSchema = yup.object().shape({
  orderCancellationReminder: yup
    .string()
    .transform((value) => (typeof value === "string" ? value.trim() : value))
    .test(
      "is_valid_format",
      "If you want to enter multiple value enter numeric value with , separated",
      (value) => {
        if (value) {
          let result = true;
          value.split(",").some((element) => {
            if (!+element) {
              result = false;
              return true;
            }
          });
          return result;
        } else {
          return true;
        }
      }
    ),
  orderCancellationHours: yup
    .number()
    .transform((v) => (v ? v : null))
    .typeError("Please enter valid input")
    .default(null)
    .nullable(),
});

type AutoCancellationDaysSchemaType = yup.InferType<
  typeof AutoCancellationDaysSchema
>;

const Setting = () => {
  const {
    control,
    watch,
    reset,
    setError,
    clearErrors,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<AutoCancellationDaysSchemaType>({
    defaultValues: {},
    resolver: yupResolver(AutoCancellationDaysSchema),
  });

  const { data: settingsData, isLoading: isSettingsDataLoading } =
    useGetSettings();
  const {
    mutate: saveAutoCancellationDays,
    isLoading: issaveAutoCancellationDaysLoading,
    data: autoCancellationDays,
  } = usePostSettings();

  useEffect(() => {
    if (settingsData) {
      let text = settingsData.order_cancellation_reminder;
      text = text ? text.substring(1, text.length - 1) : null;

      reset({
        orderCancellationReminder: text,
        orderCancellationHours: settingsData.order_cancellation_hours,
      });
    }
  }, [settingsData]);

  const autoCancellationDaysSubmitHandler = (
    data: AutoCancellationDaysSchemaType
  ) => {
    if (
      data &&
      (data.orderCancellationReminder ||
        (data.orderCancellationHours && +data.orderCancellationHours))
    ) {
      clearErrors();
    } else {
      setError("root", {
        type: "atleast-one",
        message: "Please fill atleast one field",
      });
    }
    saveAutoCancellationDays({
      data: {
        order_cancellation_reminder: data.orderCancellationReminder
          ? `[${data.orderCancellationReminder}]`
          : null,
        order_cancellation_hours: data.orderCancellationHours + "",
      },
    });
  };

  return (
    <div>
      {isSettingsDataLoading || issaveAutoCancellationDaysLoading ? (
         <div className={styles.loaderImg}>
         <Loader />
       </div>
        
      ) : (
        <div className={styles.settingPage}>
          <form onSubmit={handleSubmit(autoCancellationDaysSubmitHandler)}>
            <div className={styles.orderReminderBox}>
              <p>Order Cancellation Reminder</p>
              <InputField
               className={styles.orderReminderInput}
                control={control}
                fieldName="orderCancellationReminder"
              /></div>

            <div className={styles.orderReminderBox}>
              <p>Order Cancellation Hours</p>
              <InputField
              className={styles.orderReminderInput}
                control={control}
                fieldName="orderCancellationHours"
                type="tel"
              />
            </div>
            <div>
              <input className={styles.submitBtn} type="submit" />
            </div>
          </form>
        </div>
      )}
      <p>{errors.root?.message}</p>
    </div>
  );
};

export default Setting;
