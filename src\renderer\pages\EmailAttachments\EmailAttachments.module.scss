.searchBox {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;

    input {
        padding: 0px 12px;
    }

    @media (max-width:1300px) {
      flex-direction: column;
    }

    .sortDataSection{
      display: flex;
      align-items: center;
      @media (max-width:1024px) {
       margin-bottom: 12px;
      }
      .showAllRecordsChk {
        display: flex;
        flex: 1;
        margin-left: 16px;
        @media (max-width:767px) {
          margin-left: 8px;
        }
        .lblInput {
          flex: 1;
          display: flex;
          align-items: center;
          font-size: 16px;
          margin-left: 3px;
          @media (max-width:767px) {
            font-size: 14px;
          }
        }
        input[type=checkbox] {        
          cursor: pointer;
        }
      
      }
    }

    .SortRightSection{
      display: flex;
      align-items: center;
      @media (min-width:1025px) {
        display: contents;
       }
      @media (max-width:767px) {
       align-items: flex-start;
       flex-direction: column;
      }
      .searchContainer{
        margin-left: 24px;
        input{
          height: 40px;
        }
        @media (max-width:1024px) {
          margin-left: auto;
        }
        @media (max-width:767px) {
          margin-left: unset;
          width: 100%;
          margin-top: 12px;
        }
      }
    }
}

.poLineMatchMain{
  display: flex;
  align-items: center;
  .poNumberExclamation {
    background: #ff0000;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: flex;
    height: 20px;
    width: 20px;
    align-items: center;
    justify-content: center;
    line-height: 1;
    font-size: 16px;
    margin-right: 8px;
  }

}


.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    max-height: 600px;

    &.editLinestbl {
        max-height: 480px;
    }

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

    table {
        width: 100%;
        white-space: nowrap;
        border-collapse: collapse;
        border-spacing: 0;

        thead {
            tr {

                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 4px 12px;
                    height: 35px;
                    position: sticky;
                    z-index: 9;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
                }
            }
        }

        tbody {
            background-color: #fff;
            position: relative;

            tr {
                margin: 0;
                position: relative;

                &:nth-child(even) {
                    background-color: #f2f2f2;

                    td {
                        &:first-child {
                            background-color: #f2f2f2;
                        }
                    }
                }
                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 8px 12px;
                    height: 42px;
                    position: relative;

                    &.spreadMarkup {
                        color: green;

                        span {
                            background: #d0ebd0;
                            padding: 4px 6px;
                            border-radius: 5px;
                        }
                    }

                    &.spreadMarkdown {
                        color: red;

                        span {
                            background: #ffc4c4;
                            padding: 4px 6px;
                            border-radius: 5px;
                        }
                    }

                    &.spreadZero {
                        color: grey;
                    }

                }
            }
        }
    }
}

.noDataFound {
    text-align: center;
}

.note {
    text-align: center;
    font-size: 16px;
    margin-bottom: 15px;
    color: var(--primaryColor);
    font-weight: bold;
    text-align: left;
    margin-top: 0px;
}

.cassFileNameDiv {
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;
    text-align:center;

    .cassFileNameExclamation {
        background: #0099ff;
        border-radius: 50%;
        color: #fff;
        cursor: pointer;
        display: flex;
        height: 18px;
        width: 18px;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }

    .cassFileName {
        line-height: 1;
    }

    .cassFileInfo {
        position: absolute;
        background-color: #fff;
        z-index: 1;
        padding: 20px;
        font-size: 12px;
        max-height: 325px;

        .cassFileInfo1 {
            height: 100%;
            max-height: 251px;
            overflow-y: scroll;

            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }

            &::-webkit-scrollbar-track {
                box-shadow: inset 0 0 6px #a8b2bb;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #a8b2bb;
                border-radius: 4px;
            }

        }

        .cassFileBtn {
            display: flex;
            align-items: center;
            justify-content: right;

            button {
                background-color: var(--primaryColor);
                color: #fff;
                border-radius: 4px;
                height: 30px;
                width: 60px;
                border: none;
            }
        }
    }
}

.showFile.showFile {
 svg{
  width: auto;
  height: auto;
 }
}


.sendEmailMain{
  display: flex;
  align-items: center;
  .emailSentTxt{
   font-family: Noto Sans;
   margin-left: 10px;
   font-weight: 500;
   color: #0099ff;
   font-size: 15px;
   white-space: break-spaces;
  }
}

.showBtn {
    cursor: pointer;
    background-color:transparent;
    padding: 0;
    border:0px;
    span{
      justify-content: center;
    }
    svg{
      width: 40px;
      height: 40px;
      path{
        fill: var(--primaryColor);
      }
    }
    a{
      padding: 0;  
    }
}


.extractedFieldsDataPopup.extractedFieldsDataPopup {
    overflow-y: hidden;
    width: 100%;
    max-width: 1200px;

    @media (max-width:767px) {
        margin: 16px;
    }

    .extractedFieldsContent {
        height: 100%;
        padding: 12px;

        .extractedFieldstitle {
            font-size: 24px;
            color: var(--primaryColor);
            line-height: normal;
            font-weight: 600;
            margin-bottom: 10px;

            @media (max-width:767px) {
                font-size: 20px;
            }
        }

        .closeBtn {
            cursor: pointer;
            background-color: transparent;
            padding: 0px;
            border: 0px;
            position: absolute;
            right: 16px;
            top: 12px;

            svg {
                width: 24px;
                height: 24px;

                @media (max-width:767px) {
                    width: 20px;
                    height: 20px;
                }

                path {
                    fill: var(--primaryColor);
                }
            }
        }

        .extractedFieldsInfo {
            height: 100%;
            display: flex;
            align-items: baseline;
            column-gap: 12px;

            @media (max-width:767px) {
                flex-direction: column;
                column-gap: 0px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                border-spacing: 0;

                thead {
                    tr {

                        th {
                            line-height: 1.2;
                            font-weight: 600;
                            font-size: 14px;
                            margin: 0;
                            text-align: left;
                            padding: 4px 12px;
                            height: 35px;
                            position: sticky;
                            z-index: 9;
                            top: 0;
                            background: #676f7c;
                            color: #fff;

                            @media (max-width:767px) {
                                font-size: 13px;
                            }
                        }
                    }
                }

                tbody {
                    background-color: #fff;
                    position: relative;
                    tr {
                        margin: 0;
                        position: relative;

                        &:nth-child(even) {
                            background-color: #f2f2f2;

                            td {
                                &:first-child {
                                    background-color: #f2f2f2;
                                }
                            }

                        }

                        &.tblSeprator {
                            td {
                                border: 1px solid var(--primaryColor);
                                height: unset;
                                padding: 0;
                            }

                            &:last-child{
                                td {
                                    border: 1px solid transparent
                                } 
                            }
                        }
                        td {
                            color: #343a40;
                            font-size: 14px;
                            margin: 0;
                            padding: 6px 10px;
                            height: 60px;
                            position: relative;
                            vertical-align: baseline;
                            min-width: 200px;

                            @media (max-width:767px) {
                                font-size: 13px;
                                padding: 6px;
                            }

                        }

                    }
                }
            }

        }
        .tblExtractedFieldsTbl {
            width: 100%;
            position: relative;
            max-height: 500px;
            overflow: auto;
            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
              }
          
              &::-webkit-scrollbar-thumb {
                background:#9da2b2;
                border-radius: 50px;
              }
          
              &::-webkit-scrollbar-track {
                background: transparent;
              }
        
            .scrollDownImage {
              position: absolute;
              right: 0;
              top: 255px;
        
              svg {
                cursor: pointer;
              }
            }
        
            table {
              width: 100%;
              border-spacing: 1px;
              border-collapse: collapse;
    
              tr {
        
                th {
                  font-family: Noto Sans;
                  font-size: 15px;
                  font-weight: 600;
                  line-height: 1.6;
                  text-align: left;
                  color:#fff;
                  padding: 4px 6px 4px 6px;
                  border-top:solid 1px rgba(255, 255, 255, 0.6);
                  border-bottom: solid 1px rgba(255, 255, 255, 0.6);
                  position: sticky;
                  top: 0px;
                  z-index: 99;
                  background-color: var(--primaryColor);
                  text-transform: capitalize;
        
                  span {
                    border-bottom: solid 1px rgba(255, 255, 255, 0.6);
                    display: block;
                    padding-bottom: 3px;
                  }
                }
              }
        
              tr {
                border-top: solid 1px #eee;
        
                td {
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 1.6;
                    text-align: left;
                    vertical-align: top;
                    color: var(--primaryColor);
                    padding: 16px 4px 4px 4px;
                
                }
              }
            }
          }
    }

}

.ag_theme_quartz {
    --ag-foreground-color: #676f7c;
    --ag-background-color: white;
    --ag-header-foreground-color: white;
    --ag-header-background-color: #676f7c;
    --ag-odd-row-background-color: #f2f2f2;
    --ag-header-column-resize-handle-color: rgb(126, 46, 132);
    --ag-font-size: 14px;
    --ag-font-family: monospace;
    --ag-icon-font-code-aggregation: "\f247";
    --ag-icon-font-color-group: red;
    --ag-icon-font-weight-group: normal;
}

.emailscontainer{
  overflow-y: hidden;
  .filterByEmailStatus{
    display: flex;
    flex-direction: row;
    align-items: center;
    strong{
      margin-right: 8px;
    }
    .filterByContainer{
      display: flex;
      flex-direction: row;
      gap: 10px;
      input[type=radio]{
        cursor: pointer;
        width: 15px;
        height: 15px;
        margin: 0 5px;
        padding-right: 8px;
      }
      span{
        position: relative;
        top: -1px;
      }
      @media (max-width: 475px) {
        flex-direction: column;
      }
    }
    @media (max-width: 475px) {
      align-items: flex-start;
    }
  }
  @media (max-width: 475px) {
    overflow: auto;
  }
}

.agGridAdmin {
    --ag-icon-font-code-asc: '\25B2';
    --ag-icon-font-code-desc: '\25BC';
    --ag-icon-font-code-none: '\25B2\25BC';
    .ag-center-cols-viewport {
        min-height: 5000px !important;
    }
    .ag-icon-asc::before {
        content: var(--ag-icon-font-code-asc);
      }
    .ag-icon-none::before {
        content: var(--ag-icon-font-code-none);
        color: green;
        padding: 2px;
        margin-bottom: 5px;
        font-size: 20px !important;
    }
    .ag-root-wrapper {
      .ag-root-wrapper-body {
        .ag-body-horizontal-scroll-viewport{
            overflow-x:auto;
            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }
        
            &::-webkit-scrollbar-track {
                box-shadow: inset 0 0 6px #a8b2bb;
                border-radius: 4px;
            }
        
            &::-webkit-scrollbar-thumb {
                background: #a8b2bb;
                border-radius: 4px;
            }
        }
        .ag-header-row {
          .ag-header-cell {
            padding-left: 20px;
            padding-right: 20px;
            line-height: 1.2;
            font-weight: 600;
            font-size: 16px;
            margin: 0;
            text-align: left;
            color: #fff;
            background: #676f7c;
            &:hover {
              color: #fff;
              background: #676f7c;
            }
          }
          .ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover{
            color: #fff;
            background: #676f7c;
          }
        }
        .ag-body-viewport-wrapper.ag-layout-normal {
          overflow-x: scroll;
          overflow-y: scroll;
        }
        ::-webkit-scrollbar {
          -webkit-appearance: none;
          width: 8px;
          height: 6px;
        }
        ::-webkit-scrollbar-thumb {
          border-radius: 4px;
          background: #a8b2bb;
          box-shadow: inset 0 0 6px #a8b2bb;
        }
        .ag-body {
          .ag-body-viewport {
            .ag-center-cols-clipper {
              .ag-row-odd {
                background-color: #f2f2f2;
              }
              .ag-cell{
                cursor: pointer;
              }
              .red-border{
                border: 1px solid red;
              }
            }
          }
        }
      }
    }
  }

  .header_initial {
    font-weight: bold;
    font-size: 15px;
    padding-right: 10px;
    padding-left: 10px;
}

.lessWidthColumn{
    max-width: 120px;
}
.vendorClass{
    white-space: normal !important;

}


.editLinesDropdown .editLinesDropdown{
    font-size: 14px;
    line-height: normal;
    color: #3b4665;
    cursor: pointer;
  
    input {
      background-color: transparent;
    }
  
    .MuiSelect-select {
      display: flex;
      align-items: center;
      border: solid 1px #c4c8d1;
      height: 32px;
      border-radius: 4px;
      width: 45px;
      padding: 0px 12px !important;
    }
  
    svg {
      right: 8px;
      top: calc(50% - 0.5em);
      transform: unset;
    }
  
    fieldset {
      border: 0;
    }
  
  }
  .hideColumnDropDown{
    margin-left: 10px;
  }

  .hideColumnDropDown.editLinesDropdown{
    font-size: 14px;
    line-height: normal;
    color: #3b4665;
    cursor: pointer;
    padding: 0px;
    margin-left: 5px;

  
    input {
      background-color: transparent;
    }
  
    .MuiSelect-select {
      display: flex;
      align-items: center;
      border: solid 1px #c4c8d1;
      height: 32px;
      border-radius: 4px;
      width: 45px;
      padding: 0px 12px !important;
    }
  
    svg {
      right: 8px;
      top: calc(50% - 0.5em);
      transform: unset;
    }
  
    fieldset {
      border: 0;
    }
  
  }

  .searchInput{
    margin-left: auto;
    @media (max-width: 767px) {
      width: 100%;
    }
  }

  .dropdown {
    position: relative;
    display: inline-block;
  }
  .dropdown_toggle {
    margin-bottom: 10px;
    font-size: 16px;
    cursor: pointer;
    background-color: white;
    border: 1px black solid;
    border-radius: 2px;
  }

  .dropdown_content {
    position: absolute;
    top: 50px;
    background-color: #f9f9f9;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    padding: 12px;
    border-radius: 4px;
  }
  .dropdown_list {
    display: flex;
    flex-direction: column;
  }
  .dropdown_item {
    margin-bottom: 8px;
    font-size: 14px;
  }
  .dropdown_checkbox {
    margin-right: 8px;
  }
  
  .cell_default{
    align-content: center;
    padding-right: 10px;
    padding-left: 10px;
  }

  .additionalItemsTable {
    width: 100%;
    border-spacing: 0;
    background: transparent !important;
    background-color: white;
    white-space: normal;

    tbody {
        background: transparent !important;
        position: relative;
    }     
}
.additionalItemsTable tbody tr {
    background-color: transparent !important;
    max-width: 200px !important;

      td:first-child {
          min-width: 200px !important;
          max-width: 200px !important;
      }
      td:nth-child(2) {
          min-width: 200px !important;
          max-width: 500px !important;
      }

}
.additionalItemsTable tbody td {
    background-color: transparent !important;
    height: 10px !important;
    max-width: 200px !important;
}


.cassAutocompleteTooltipStyle {
  .tooltip.tooltip {
      color: #fff;
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #fff;
      margin-top: 6px;
  }
}
.truncateText{
  width: 100%; 
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.truncateText::after{
  content: '';
  display: block;
}

.sendEmailPopup {
  h2 {
      display: none;
  }
  .sendEmailPopupDiv {
      padding: 20px;
      text-align: center;
      width: 380px;
      @media screen and (max-width: 768px) and (min-width: 320px) {
          width: 280px;
      }
      .sendEmailText {
          text-align: center;
          font-size: 20px;
          margin-bottom: 24px;
          color: var(--primaryColor);
      }
      .yesAndnoBtn {
          display: flex;
          gap: 10px;
          .okBtn {
              width: 100%;
              height: 45px;
              border-radius: 6px;
              text-decoration: none;
              gap: 8px;
              border: none;
              font-size: 16px;
              font-weight: 500;
              cursor: pointer;
              background-color: var(--primaryColor);
              color: #fff;
          }
      }
  }
}

.sendEmailGrid{
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;

  span{
      width: 30px;
      font-size: 14px;
      text-align: left;
  }

  .sendEmailinput {
      width: 120px;
      border-radius: 4px;
      border: solid 1px #c4c8d1;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.57;
      letter-spacing: normal;
      text-align: left;
      color: #3b4665;
      height: 34px;
      padding: 0px 10px;
      flex: 1;
      &:read-only{
          background-color: #e3e3e3;
      }

      &:focus{
          outline: none;
      }
  }
}

.sendEmailPopupInputTooltip.sendEmailPopupInputTooltip {
  .tooltip.tooltip {
      color: #fff;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #fff;
      margin-top: 6px;
  }
}
.inputField {
  display: flex;
  flex: 1;

  .lblInput {
    flex: 1;
    display: flex;
    align-items: center;
    margin-right: 20px;
    font-size: 16px;
  }
  .checkbox {
    margin: 11px;
    margin-left: 0;
    width: 23px;
  }

  .lbl {
    flex: 1;
    display: flex;
    align-items: center;
    font-weight: bold;
  }

  .inputBox {
    flex: 1;
    max-width: 300px;
    height: 38px;
    padding: 6px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
  }
}

.searchContainer{
  display: flex;
  position: relative;
  input{
    padding-right: 30px;
  }
  .clearInputIcon{
    position: absolute;
    top: 8px;
    right: 6px;
    cursor: pointer;
    z-index: 99;
    background-color: transparent;
    border: 0px;
    padding: 0px;
    display: flex;
    align-items: center;
    svg{
      width: 24px;
      height: 24px;
      path{
        fill:var(--primaryColor);
      }
    }
  }
}


.hideUnhideModalPopup {
    padding: 20px;
    text-align: center;

    @media screen and (max-width: 767px) and (min-width: 320px) {
      width: 240px;
    }
}

.hideUnhidePopup.hideUnhidePopup {
  padding: 24px;

  .yesAndnoBtn {
      display: flex;
      gap: 10px;
      margin-top: 24px;
      .okBtn {
          width: 100%;
          height: 40px;
          border-radius: 6px;
          text-decoration: none;
          gap: 8px;
          border: none;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          background-color: var(--primaryColor);
          color: #fff;
          &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
          }
      }
  }

  &.hideUnhide{
    .inputField{
      .lblInput{
        max-width: 140px;
      }
      input[type=checkbox]{
        margin-left: 0px;
      }
    }
  }


}

.hideUnhideBtn {
  cursor: pointer;
  background-color: transparent;
  color: #fff;
  font-size: 14px;
  padding: 8px 15px;
  border-radius: 4px;
  text-decoration: none;
  width: 85px;
  border: 0px;
}

.customHeader{
  text-align: center;
  white-space: pre-wrap !important;
 }


 .hideUnhidePopup {
  h2 {
      display: none;
  }

  .hideUnhidePopup {
      padding: 20px;
      text-align: center;
      width: 300px;

      @media screen and (max-width: 768px) and (min-width: 320px) {
          width: 240px;
      }

      .hideUnhidePopuptext {
          text-align: center;
          font-size: 20px;
          margin-bottom: 24px;
          color: var(--primaryColor);
      }

      .yesAndnoBtn {
          display: flex;
          gap: 10px;

          .okBtn {
              width: 100%;
              height: 45px;
              border-radius: 6px;
              text-decoration: none;
              gap: 8px;
              border: none;
              font-size: 16px;
              font-weight: 500;
              cursor: pointer;
              background-color: var(--primaryColor);
              color: #fff;
          }

      }
  }
}
