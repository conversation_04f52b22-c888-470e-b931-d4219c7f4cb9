.container {
    padding: 20px;
    background-color: #f8f9fc;
    min-height: 100vh;
}

.header {
    margin-bottom: 30px;
    
    h2 {
        font-size: 24px;
        font-weight: 600;
        color: var(--primaryColor);
        margin-bottom: 8px;
    }
    
    p {
        font-size: 16px;
        color: #525f7f;
        margin: 0;
    }
}

.formContainer {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
}

.dropdownSection {
    display: flex;
    gap: 40px;
    
    @media screen and (max-width: 768px) {
        flex-direction: column;
        gap: 30px;
    }
}

.componentSection {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.label {
    font-size: 16px;
    font-weight: 500;
    color: #3b4665;
    margin-bottom: 0;
}

.dropdownButtonRow {
    display: flex;
    align-items: center;
    gap: 15px;
    
    @media screen and (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
}

.dropdownGroup {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; // Prevent flex item from overflowing
}

.dropdown {
    width: 100%;
    padding-left: 12px;
    
    .MuiSelect-select {
        padding: 12px;
        font-size: 14px;
        color: #3b4665;
        border: 1px solid #c4c8d1;
        border-radius: 4px;
        background-color: #fff;
        
        &:focus {
            border-color: var(--primaryColor);
        }
    }
    
    .MuiOutlinedInput-notchedOutline {
        border: none;
    }
}

.revertBtn {
    height: 45px;
    padding: 0 20px;
    border-radius: 6px;
    border: none;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    background-color: var(--primaryColor);
    color: #fff;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 180px;
    
    &:hover:not(:disabled) {
        background-color: #333;
        transform: translateY(-1px);
    }
    
    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }
    
    &:active:not(:disabled) {
        transform: translateY(0);
    }
    
    @media screen and (max-width: 768px) {
        width: 100%;
        min-width: auto;
    }
}

.revertBothBtn {
    background-color: #28a745;
    
    &:hover:not(:disabled) {
        background-color: #218838;
    }
}

.loadingText {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 18px;
    color: var(--primaryColor);
    font-weight: 500;
}

// Override for MatSelect component
:global(.w-100) {
    width: 100% !important;
    
    .MuiSelect-select {
        padding: 12px !important;
        font-size: 14px !important;
        color: #3b4665 !important;
        border: 1px solid #c4c8d1 !important;
        border-radius: 4px !important;
        background-color: #fff !important;
        
        &:focus {
            border-color: var(--primaryColor) !important;
        }
    }
    
    .MuiOutlinedInput-notchedOutline {
        border: none !important;
    }
    
    &.Mui-focused {
        .MuiSelect-select {
            border-color: var(--primaryColor) !important;
        }
    }
}
