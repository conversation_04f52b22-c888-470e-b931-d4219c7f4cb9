import * as yup from "yup";
import { errorText } from "../utils/constant";

const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const addPreapprovedEmailsFormSchema = yup.object().shape({
    data: yup.array().of(yup.object().shape({
        email: yup
            .string()
            .required('Email is Required')
            .email('Enter Valid Email')
            .matches(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/, {
                message: 'Enter Valid Email'
            }),
        cohort: yup.string().nullable().test("isRequired", 'Invalid Cohort', function(value) {
            if(value === null || value === '') return true;
            const list = this.options?.from?.[1].value.onBoardList;
            if(list?.find((type: string | null | undefined) => type === value)) return true;
            return false;
          })
    })),
    onBoardList: yup.array()
});

export type addPreapprovedEmailsFormSchemaType = yup.InferType<
    typeof addPreapprovedEmailsFormSchema
>;
