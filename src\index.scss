body {
  margin: 0;
  // font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
  //   'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
  //   sans-serif;
  font-family: Noto Sans, sans-serif;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

:root{
  --primaryColor:#343a40 ;
  --secondaryColor:#204d74;
  --white:#fff;
  --titleFont:'Syncopate';
  --subtitleFont:'Inter'
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.dFlex{
  display: flex;
}

.h100{
  height: 100%;
}

input[type="checkbox"] {
  accent-color: var(--primaryColor)
}

.PaginationNumber {
  .saveBtn{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  ul {
    list-style: none;
    display: flex;
    padding: 0px;

    li {
      a {
        position: relative;
        display: block;
        padding: 8px 12px;
        margin-left: -1px;
        line-height: 1.25;
        color: var(--primaryColor);
        background-color: #fff;
        border: 1px solid #dee2e6;
        cursor: pointer;

        @media screen and (max-width: 768px) and (min-width: 320px) {
          padding: 4px 6px;
        }
      }


      &.selected {
        a {
          background-color: var(--primaryColor);
          color: #fff;
        }

      }

    }

  }


}

.containerChk {
  display: inline-block;
  position: relative;
  cursor: pointer;
  padding-left: 28px;
  text-align: left;

  .lblChk {
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    text-align: left;
  }

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .checkmark {
    position: absolute;
    top: -14px;
    left: 0;
    z-index: 1;
    width: 18px;
    height: 18px;
    border-radius: 1.7px;
    border: solid 0.7px #3b4665;
    background-color: #ebedf0;
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  input:checked~.checkmark {
    background-color: #3b4665;
    border: solid 0.7px #3b4665;
  }

  input:checked~.checkmark:after {
    display: block;
  }

  .checkmark:after {
    left: 6px;
    top: 3px;
    width: 3px;
    height: 7px;
    border: solid #fff;
    border-width: 0 1.5px 1.5px 0;
    transform: rotate(45deg);
  }
}

.chkNotification{
  display: inline-block;
  margin-right: 30px;
   .containerChk{
          padding-left: 24px;
   }
   label{
    margin: 0px;
   }
}

.cassChkBox{
  .containerChk{
    .checkmark{
      top:1px
    }
  }
}


.agGridAdmin {
  .ag-root-wrapper { 
    .ag-root-wrapper-body {
      .ag-header-row {
        .ag-header-cell {
          padding-left: 20px;
          padding-right: 20px;
          line-height: 1.2;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
          text-align: left;
          color: #fff;
          background: #676f7c;
          &:hover {
            color: #fff;
            background: #676f7c;
          }
        }
        .ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover{
          color: #fff;
          background: #676f7c;
        }
      }
     
      .ag-body-viewport-wrapper.ag-layout-normal {
        overflow-x: scroll;
        overflow-y: scroll;
      }
      ::-webkit-scrollbar {
        -webkit-appearance: none;
        width: 8px;
        height: 6px;
      }
      ::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background: #a8b2bb;
        box-shadow: inset 0 0 6px #a8b2bb;
      }
      .ag-body {
        .ag-body-viewport {
          .ag-center-cols-clipper {
            .ag-row-odd {
              background-color: #f2f2f2;
            }
            .ag-cell{
              cursor: pointer;
            }
            .red-border{
              border: 1px solid red;
            }
          }
        }
      }
    }
  }
  
}

.ag-body-horizontal-scroll{
  .ag-horizontal-left-spacer{
    overflow: auto;
  }
}

.refPopup.refPopup {
  .MuiDialog-container {
    .MuiPaper-root {
      width: 90%;
      max-width: 100%;
    }
  }
}

.orderContinuePopup.orderContinuePopup {
  .MuiPaper-root {
    width: 100%;
    max-width: 560px;
    @media screen and (max-width: 767px) and (min-width: 320px) {
      width: 100%;
      margin: 15px;
  }

    .MuiDialogTitle-root {
      display: none;
    }

    @media (min-width: 992px) {

      .pendingUsersInputMain {
        margin-bottom: 20px;
      }

      .lblCreateUser {
        width: 145px;
      }

      .inputPendingUsers {
        margin: 0px;
        .MuiSelect-select{
          display: flex;
          align-items: center;
        }
      }

      .submitBtnCreateUser {
        gap: 16px
      }
    }
  }
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.enableArrow::-webkit-outer-spin-button,
.enableArrow::-webkit-inner-spin-button {
  -webkit-appearance: button;
  margin: 0;
}

.errorResponseMessagePopup{
  .MuiDialogTitle-root{
    display: none;
  }
}

.contentMain {
  height: calc(100vh - 160px);

  .loaderImg {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    height: 100%;
  }
}

.creartUserCompanyInput.creartUserCompanyInput{
  .companyDropdown{
  @media (max-width:767px) {
    margin-left:0px;
  }
}
}

.companyDropdown.companyDropdown {
  width: 100%;
  max-width: 320px;
  height: 40px;
  padding: 0px;
   margin: 15px;
  
  .MuiInputBase-root{
    height: 40px;
    input{
      padding: 0px;
      font-size: 14px;
      &::placeholder{
        opacity: unset;
        color:rgb(117, 117, 117);
      }
    }
  }

}

.companyClickBtn{
  color: var(--primaryColor);
}

.pendingUserPopup{
  .creartUserCompanyInput{
    .lblCreateUser{
      @media (max-width:767px) {
        margin-bottom:8px;
      }
    }
  }
 
  .companyDropdown.companyDropdown {
    margin: 0px;
    display: flex;
    flex: 1;
    max-width: 100%;
    @media (max-width:767px) {
      margin-bottom: 12px;
    }
  }
}
.noDataFoundTd{
  text-align: center;
}
.snackbar_LOW{
  background-color: #ffd47b;
}
.snackbar_MEDIUM{
  background-color: #f5ad4f;
}
.snackbar_HIGH{
  background-color: #ff594a;
}

.discountStartDate{
  .MuiTextField-root{
    width: 100%;
  }
  .MuiInputBase-root{
   padding-right: 0px;
  }
  .MuiButtonBase-root{
    margin-left: 0px;
  }
  .MuiInputAdornment-root{
    margin: 0px;
  }
}

.safeImageMain {
  border: 1px solid #f1f1f1;
  padding: 20px;
  width: 100%;
  max-width: 100%;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 6px;

  @media (max-width: 767px) {
    margin: 16px auto;
    padding: 16px;
  }

  .title {
    font-size: 18px;
    line-height: normal;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--primaryColor);

    @media (max-width: 767px) {
      font-size: 16px;
      margin-bottom: 16px;
    }
  }
}
.imageKit{
  display: flex;
  align-items: center;
  gap:8px;
  label{
    display: flex;
    align-items: center;
    @media (max-width: 767px) {
      font-size: 12px;
    }
    input{
      margin-left: 8px;
    }
  }
    .saveBtn {
      display: inline-block;
      text-align: center;
      padding: 6px 18px;
      border: 1px solid transparent;
      border-radius: 0.25rem;
      background-color: #343a40;
      font-size: 14px;
      color: #fff;
      margin-left: 12px;
      cursor: pointer;
      @media (max-width: 767px) {
        margin-left: 8px;
        padding: 4px 20px;
      }
  }
}

.discountStartDate{
  .MuiInputBase-root{
   padding-right: 0px;
  }
  .MuiButtonBase-root{
    margin-left: 0px;
  }
  .MuiInputAdornment-root{
    margin: 0px;
  }
}
.lblCreateUser{
  flex: 1;
  max-width: 145px;
  @media (max-width: 767px) {
    margin-bottom: 8px;
  }
}

.editUserPopupMain{
  .pendingUsersInputMain{
    margin-bottom: 12px;
  }
}


.inputPendingUsersDropdown.inputPendingUsersDropdown{
     max-height: 260px;
     max-width: 320px;
     li{
      font-size: 14px;
     }
}

.adHocAutoComplete.adHocAutoComplete{
  width: 100%;
  input{
    border: 0px;
    height: auto;
    margin: 0px;
  }
}
.createNewAccountPopup {
  .MuiDialog-paper {
    width: 500px;
    padding: 24px;
    h1 {
      font-size: 20px;
      color: #000;
      line-height: normal;
      margin-bottom: 12px;
      margin-top: 0px;
    }
    table {
      tr {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10px;
        &:last-child {
          padding-bottom: 0px;
        }
        @media (max-width:767px) {
          flex-direction: column;
          align-items: flex-start;
        }
        td {
          @media (max-width:767px) {
            width: 100%;
          }
          &.lblcreateNewAccount {
            line-height: normal;
            color: #343a40;
            font-size: 15px;
            height: 20px;
            vertical-align: middle;
            @media (max-width:767px) {
              margin-bottom: 6px;
            }
          }

          input {
            width: 100%;
            height: 34px;
            padding: 4px 5px 4px 15px;
            font-size: 15px;
            border: 1px solid rgba(0, 0, 0, 0.23);
            border-radius: 4px;
            box-sizing: inherit;

          }
        }
      }
    }
    .btnCreate {
      width: 208px;
      padding: 10px 12px;
      border: 1px solid transparent;
      border-radius: 0.25rem;
      background-color: #343a40;
      font-size: 16px;
      color: #fff;
      cursor: pointer;
      margin-right: 10px;
      float: right;
      margin-top: 12px;

      &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }

      @media (max-width:767px) {
        width: 100%;
        margin-top: 10px;
        margin-right: 0px;
      }
    }
  }
}

.listAdHoc{
  .optionGrouplbl{
    margin: 3px 0px;
    font-size: 16px;
    font-weight: bold;
    opacity: 0.8;
  }
  ul{
    padding: 0px;
  }
}

.adHoclistAutoComletePanel1{
  ul{
    .MuiAutocomplete-option{
      flex-direction: column;
      align-items: flex-start;
      border-bottom: 1px solid #dee2e6;

      .compnyName{
        font-size: 14px;
        span{
          font-weight: bold;
        }
      }
    }
  }
 
}

.editLineSelectDropdown.editLineSelectDropdown {
  height: 100%;
  width: 100%;
  padding: 0px;
  margin-top: 4px;
  margin-bottom: 16px;

  .MuiInputBase-root {
    height: 100%;
    border: solid 1px #c4c8d1;
    width: 100%;
    input {
      padding: 0px;
      font-size: 14px;
      color: #3b4665;
      &:disabled {
        -webkit-text-fill-color: #000;
      }
    }
    &:focus {
      border-radius: 0px 0px 4px 4px;
    }
  }

  .MuiOutlinedInput-root {
    padding: 6px 6px 6px 10px;
  }

  .MuiSelect-select {
    display: flex;
    justify-content: center;
  }


  svg {
    right: 24px;
    top: calc(50% - 0.6em);
    transform: unset;
  }

  fieldset {
    border: 0;
  }
}

.editLinesDropdown.editLinesDropdown {
  font-size: 14px;
  line-height: normal;
  color: #3b4665;
  cursor: pointer;

  input {
    background-color: transparent;
  }

  .MuiSelect-select {
    display: flex;
    align-items: center;
    border: solid 1px #c4c8d1;
    height: 32px;
    border-radius: 4px;
    width: 45px;
    padding: 0px 12px;
  }

  svg {
    right: 8px;
    top: calc(50% - 0.5em);
    transform: unset;
  }

  fieldset {
    border: 0;
  }

  
  &.emailAttachDropdown{
    .MuiSelect-select {
      height: 38px;
    }
    @media (max-width: 767px) {
      width: 100%;
      flex: 1;
     }
     .MuiSelect-select {
      @media (max-width: 767px) {
        width: 100%;
      }
  
    }
  }


}
  .sendEmailPopper{
    z-index: 99;
    padding: 0px;
    height: 42px;
    left: 10px;
    border: 0px;
    
    .MuiBox-root{
      background-color: rgba(197, 210, 202,0.5);
      display: flex;
      align-items: center;
      height: 100%;
      border: 0px;
      padding: 0px 10px;
    }
  }

.cassMappingData.cassMappingData {
  background-color: #fff;
  width: 600px;
  height: 450px;
  border-radius: 4px;
  border: 1px solid #d4d4d4;
  margin-left: 5px !important;
  z-index: 9999;
}

.cassMappingDropdown.cassMappingDropdown{ 
  .MuiAutocomplete-groupLabel{
    padding: 0px;
    font-size: 14px;
  }
  input.MuiAutocomplete-input{
    padding: 0px;
    font-size: 14px;
  }

  .MuiAutocomplete-endAdornment{
    height: 100%;
    display: flex;
    align-items: center;
    top: 50%;
    -webkit-transform: translate(0, -50%);
    -moz-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
     transform: translate(0, -50%);

    .MuiSvgIcon-root{
      width: 22px;
      height: 22px;
    }

  }

}
.cassMappingDropdown.cassMappingDropdown{
  height: 38px;
  &.textareaErrorDrodown{
    .MuiOutlinedInput-notchedOutline{
      border-color: #ff0000;
    }
  }
  .MuiAutocomplete-endAdornment{
    height: 100%;
    display: flex;
    align-items: center;
    top: 50%;
    -webkit-transform: translate(0, -50%);
    -moz-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
     transform: translate(0, -50%);

    .MuiSvgIcon-root{
      width: 22px;
      height: 22px;
    }

  }
}
.cassMappinglist{
  .probablePoTxt{
    font-size: 16px;
    padding: 6px 20px;
    margin: 0px;
    position: sticky;
    background-color: #fff;
    top:0px;
  }
  ul.ulList{
    padding: 0px;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    height: 100%;
   
  }
}

.listAdHoc{
  .optionGrouplbl{
    margin: 3px 0px;
    font-size: 16px;
    font-weight: bold;
    opacity: 0.8;
  }
  ul{
    padding: 0px;
  }
}

.adHoclistAutoComletePanel1,.cassMappinglist{
  ul{
    .MuiAutocomplete-option{
      border-bottom: 1px solid #dee2e6;
      display: flex;
      align-items: flex-start;
      column-gap: 8px;
      font-family: Noto Sans, sans-serif;
      padding: 6px 12px;
      .compnyName{
        font-size: 14px;
        span{
          font-weight: bold;
        }
      }
    }
  }
  
}

.cassDataScrollContent {
  height: 100%;
  padding: 0px 6px !important;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }

  input[type=checkbox]{
    cursor: pointer;
  }

}

.cassDataTotal{
  background-color: #c4c8d1;
  font-size: 18px;
  color: #000;
  padding: 12px 12px;
  display: flex;
  align-items: center;
  font-weight: bold;
  letter-spacing: 1px;
  width: 100%;
}

.sendEmailPopper{
  z-index: 99;
  padding: 0px;
  height: 42px;
  left: 10px;
  border: 0px;
  
  .MuiBox-root{
    background-color: rgba(197, 210, 202,0.5);
    display: flex;
    align-items: center;
    height: 100%;
    border: 0px;
    padding: 0px 10px;
  }
}

.resaleCertLoader{
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  .loaderCert{
    margin-right: 20px;
  }
  .loadingTxt{
    font-size: 18px;
    color: var(--primaryColor);
  }
}

.hidden {
  display: none;
}

.videoThumbBox{
  .videoPlayerMain{
    height: 100%;
    .playNextBtn{
      svg{
        width: 40px;
        height: 40px;
      }
    }
    .custom-video-player{
       height: 100%;
       .leftbar-action{
        
        .time-display{
          font-size: 10px;
        }
       }
    }
  }
}
.safeBorder{
  height: 425px;
 
  .videoPlayerMain{
    height:425px;
    video{
      max-height:100%;
    }
    .custom-video-player{
      height: 425px;
      .controls{
        padding: 8px 8px 10px 8px;
      }
    }
  }
}


.safePopupDialog{
  .MuiBackdrop-root {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: hsla(0, 0%, 100%, .8);
  }
}

.ag-body-horizontal-scroll-viewport, .ag-body-vertical-scroll-viewport{
  overflow-x:auto;
  &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
  }

  &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px #a8b2bb;
      border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
  }
}

.ag-theme-quartz {
  --ag-icon-font-code-aggregation: "\f247";
  --ag-icon-font-code-arrows: "\f0b2";
  --ag-icon-font-code-group: "\F0328";
  --ag-icon-font-color-group: red;
  --ag-icon-font-weight-group: normal;
}

.custom-sort-none::before {
  content: ''; 
  display: block;
  width: 0;
  height: 0;
  border-left: 5px solid transparent; 
  border-right: 5px solid transparent; 
  border-bottom: 7px solid white; 
  margin: 2px auto; 
  margin-left: 5px;
  position: relative;
}

.custom-sort-none::after {
  content: ''; 
  display: block;
  width: 0;
  height: 0;
  border-left: 5px solid transparent; 
  border-right: 5px solid transparent;
  border-top: 7px solid #fff;
  margin: 2px auto;
  margin-left: 5px; 
}
.custom-sort-asc.sorted::before {
  content: ''; 
  display: block;
  width: 0;
  height: 0;
  border-left: 5px solid transparent; 
  border-right: 5px solid transparent; 
  border-bottom: 7px solid #fff;
  margin: 2px auto;
  margin-left: 5px;
}

.custom-sort-desc.sorted::before {
  content: '';
  display: block;
  width: 0;
  height: 0;
  border-left: 5px solid transparent; 
  border-right: 5px solid transparent; 
  border-top: 7px solid #fff;
  margin: 2px auto;
  margin-left: 5px;
}


.ag-icon::before{
  color: white;
  margin-left: 5px;
}

.ag-icon-asc::before {
  font-size: 20px;
}
.ag-icon-desc::before {
  font-size: 20px;
}

.poLineMatch {
  .ag-header-cell-label{
    font-weight: bold;
    font-size: 16px;
    justify-content: center;
  }
}

.ag-cell-wrapper{
  height: 100%;
}

.poLineMatchCol{
  .ag-cell-wrapper{
    height: 100%;
    text-align: center;
  }
}

.headerCenter {
  .ag-header-cell-label{
    font-weight: bold;
    font-size: 16px;
    justify-content: center;
    text-align: center;
  }
}

.iconCenterCell{
  .ag-cell-wrapper{
    height: 100%;
    text-align: center;
  }
}

.poNOMatchCol{
  .ag-cell-wrapper{
    padding: 0px 10px;
    font-weight: bold;
    height: 100%;
  }
}

.holidayListHeader.holidayListHeader{
  padding: 0px 10px;
}

.holidayListCell.holidayListCell{
  display: flex;
  align-items: center;
  padding: 0px 10px;
}


.logReaderGrid.logReaderGrid{
  .ag-header-cell{
    font-family: Noto Sans;
  }
  .ag-row{
    border-bottom: 1px solid #676f7c;
    font-family: Noto Sans;
    font-size: 14px;
  }
}

.LogListHeader.LogListHeader{
  padding: 0px 10px;
}

.LogListCell.LogListCell{
  display: flex;
  align-items: center;
  padding: 0px 10px;
}
/* SubTitle style */

.subTitleStyle{
  video::-webkit-media-text-track-container {
    height: calc(100% - 52px) !important;
    font-family: Inter;
    bottom: 45px !important;
  }
  video::cue{
    line-height: 1.5;  
  }
}

.ag-header-cell-label .ag-header-cell-text {
  white-space: pre-wrap !important;
}

.ag-dnd-ghost.ag-dnd-ghost {
  padding: 10px;
  line-height: 1.2;
  font-weight: 600;
  font-size: 16px;
  margin: 0;
  text-align: center;
  color: #fff;
  background-color: #676f7c;
  white-space: pre-wrap;
  border-radius: 8px ; 
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3); 
}

.ag-icon::before{
  display: none ;
}

.datePicker.datePicker{
   flex:1;
   label{
    font-family: Noto Sans;
    font-size: 16px;
    top:-7px;
    color: var(--primaryColor);
  }

  .MuiInputLabel-shrink{
      top:0px;
    }

  .MuiInputBase-root{
    height: 40px;
    padding: 6px 10px;
    font-weight: 400;
    line-height: 1.5;
    font-family: Noto Sans;
    font-size: 14px;
    color: var(--primaryColor);
    border-radius: 0.25rem;
    
    input{
      padding: 0px;
    }
   .MuiOutlinedInput-notchedOutline{
      border-color: #ced4da;
    }
  }
}

.previewDatePicker{
  .datePicker.datePicker{
    legend{
      span{
        padding-right: 12px;
      }
    }
  }
  .datePickerContainer{
    flex: 1;
  }
}

.selectDropdownPreview.selectDropdownPreview{
  label{
    top:-7px
  }
  .MuiInputLabel-shrink{
    top:0px;
  }
  .MuiFormControl-root{
    height: 100%;
  }
  .MuiOutlinedInput-root{
    font-family: Noto Sans;
    font-size: 14px;
    height: 100%;
  }
 
}

.timePicker.timePicker{
  &.MuiTextField-root{
    min-width: 100px;
     input{
         padding: 10px;
         font-family: Noto Sans;
         font-size: 14px;
         color: var(--primaryColor);
     }
  }
  
}

.dropdownWrapper{

  legend{
    span{
      padding-right: 10px;
    }
  }

  label{
    font-family: Noto Sans;
    font-size: 16px;
    color: var(--primaryColor);
  }

  .MuiInputLabel-root.Mui-focused{
      color: var(--primaryColor); 
 }
}

.customeTimerSelect.customeTimerSelect{
    font-family: Noto Sans;
    font-size: 14px;
    color: var(--primaryColor);
    .Mui-disabled{
      cursor: not-allowed !important;
    }

    &.Mui-focused{
      .MuiOutlinedInput-notchedOutline{
        border-color:var(--primaryColor);
      }
    }
  .MuiSelect-select{
    padding: 9.8px 10px;
  }
}

.timerWrapper.timerWrapper {
  max-height: 260px;

  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }
  ul{
    padding: 4px;
    li {
      font-family: Noto Sans;
      font-size: 15px;
      color: var(--primaryColor);
      padding: 4px 10px 4px 12px;
    }
  }
  
}


.datePickerSplitInvoice.datePickerSplitInvoice{
  .datePickerContainer{
    button{
      display: none;
    }
    .datePicker{
      .MuiInputBase-root{
          width: 89px;
          height: 28px;
          border-radius: 6px;
          border: solid 2px #16b9ff;
          padding: 0px 4px;
      }
      input{
        border: none;
        padding: 0px;
      }
      fieldset{
        border-color: transparent;
      }
    }
  }
  &:last-child{
    .datePickerContainer{
      margin-top: -5.5px;
    .datePicker{
      input{
        color: #1c40e7;
      }
    }
  }
  }
}


// Select dropdown styles
.editLineSelectDropdown.editLineSelectDropdown {
  height: 100%;
  width: 100%;
  padding: 0px;
  margin-top: 4px;
  margin-bottom: 16px;

  .MuiInputBase-root {
    height: 100%;
    border: solid 1px #c4c8d1;
    width: 100%;
    input {
      padding: 0px;
      font-size: 14px;
      color: #3b4665;
      &:disabled {
        -webkit-text-fill-color: #000;
      }
    }
  }

  .MuiOutlinedInput-root {
    padding: 6px 6px 6px 10px;
  }

  .MuiSelect-select {
    display: flex;
    justify-content: center;
  }
}

.editLinesDropdown.editLinesDropdown {

  .MuiSelect-select {
    width: 100%;
    display: flex;
    align-items: center;
    border: solid 1px #c4c8d1;
    height: 35px;
    border-radius: 4px;
    padding: 0px 12px;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
  }
}


.stateDropdownSplitInvoice{
  .MuiSelect-select{
    display: flex;
    align-items: center;
    padding: 3px 8px;
  }
  fieldset{
    border-color: transparent;
  }
}


.stateDropdownListSplit.stateDropdownListSplit{
     max-height: 260px;
     max-width: 320px;
     li{
      font-family: Noto Sans;
      font-size: 14px;
      padding: 4px 6px;
     }
}
