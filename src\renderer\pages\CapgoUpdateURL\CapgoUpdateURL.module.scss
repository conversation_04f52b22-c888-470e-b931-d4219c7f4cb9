.container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.capGoUrlMain {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  width: 100%;
  @media (max-width:480px) {
   padding: 12px;
  }

  .title {
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 6px;
    @media (max-width:767px) {
      font-size: 16px;
    }
  }

  .selectContainer {
    display: flex;
    @media (max-width:480px) {
      flex-direction: column;
    }

    .InputFieldcss {
      max-width: 340px;
      width: 100%;
      height: 40px;
      padding: 10px;
      font-weight: 400;
      line-height: 1.5;
      font-size: 16px;
      color:var(--primaryColor);
      background-color: #fff;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
      transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
      font-family: Noto Sans, sans-serif;
      @media (max-width:480px) {
       font-size: 14px;
      }
      &.MuiInputBase-root {
        &:hover {
          .MuiSelect-select {
            border-color: transparent;
          }

          fieldset {
            border-color: #397aff;
            border-width: 1px;
          }
        }

        &.Mui-focused {
          fieldset {
            border-color: #397aff;
            border-width: 1px;
            background-color: rgba(57, 122, 255, 0.1);
          }

          .MuiSelect-select {
            color: #397aff;
          }
        }
      }
    }

    .btnSave{
      font-family: Noto Sans, sans-serif;
      font-size: 16px;
      font-weight: 500;
      background-color: var(--primaryColor);
      color: var(--white);
      border: 0px;
      border-radius: 4px;
      height: 38px;
      padding: 6px 18px;
      cursor: pointer;
      margin-left: 12px;
      @media (max-width:480px) {
        margin-left: 0px;
        margin-top: 8px;
        font-size: 14px;
      }
    }
  }

}