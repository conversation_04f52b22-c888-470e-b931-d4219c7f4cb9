import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

const useGetAllUploadedVideos = (itemsPerPage: number, currentPage: number, search: string) => {
  return useQuery([reactQueryKeys.getAllUploadedVideos, itemsPerPage, currentPage, search], async () => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/getVideos?page=${currentPage}&limit=${itemsPerPage}&search=${search}`
      );
      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default useGetAllUploadedVideos;
