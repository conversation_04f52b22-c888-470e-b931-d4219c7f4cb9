import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const useInitiateMultiPartUpload = () => {

  return useMutation(async (videoFileName: any) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/videos/initiate-multipart-upload`,{
            data: {
                file_name: videoFileName,
                bucket_name: import.meta.env.VITE_S3_UPLOAD_VIDEO_THUMBNAIL_BUCKET_NAME
            }
        }
      );
      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default useInitiateMultiPartUpload;
