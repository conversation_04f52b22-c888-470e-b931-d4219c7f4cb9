import * as yup from "yup";
import { errorText } from "../utils/constant";

export const VideoUploadsFromSchema = yup.object().shape({
  title: yup.string().required(errorText.required).trim(),
  description: yup.string().nullable(),
  caption: yup.string().nullable(),
  video_s3_url: yup.string().nullable(),
  thumbnail_s3_url: yup.object().shape({
    thumbnail_app: yup.string(),
    thumbnail_safe: yup.string(),
    electron_player: yup.string(),
    intro_desktop: yup.string(),
    intro_mobile: yup.string(),
    intro_tablet: yup.string()
  }),
  subtitle_s3_url : yup.string().nullable(),
  videoTags: yup.array().of(
    yup.string().required(errorText.required)
  ).required(errorText.required).min(1, errorText.arrayMinimum),
  videoInternalTags: yup.array().of(
    yup.string().required(errorText.required)
  ),
  videoFile: yup.mixed(),
  thumbnailFiles: yup.object().shape({
    thumbnail_app: yup.mixed().test('fileRequired1', errorText.fileIsRequired, (value) => value !== undefined),
    thumbnail_safe: yup.mixed().test('fileRequired2', errorText.fileIsRequired, (value) => value !== undefined),
    electron_player: yup.mixed().test('fileRequired3', errorText.fileIsRequired, (value) => value !== undefined),
    intro_desktop: yup.mixed().test('fileRequired4', errorText.fileIsRequired, (value) => value !== undefined),
    intro_mobile: yup.mixed().test('fileRequired5', errorText.fileIsRequired, (value) => value !== undefined),
    intro_tablet: yup.mixed().test('fileRequired6', errorText.fileIsRequired, (value) => value !== undefined)
  }),
  share_video_url: yup.string().trim().nullable(),
  is_large_file : yup.boolean(),
  subtitleFile: yup.mixed(),
});

export type VideoUploadsFromSchemaType = yup.InferType<
  typeof VideoUploadsFromSchema
>;
