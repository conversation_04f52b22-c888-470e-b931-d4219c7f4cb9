import * as yup from "yup";

export const  AddTagSchema = yup.object().shape({
  name: yup.string().required("Required").trim(),
  display_title: yup.string().nullable().trim().required("Required"),
  display_subtitle: yup.string().nullable().trim().required("Required"),
  query_param: yup.string().trim().required("Required"),
  show_on_app: yup.boolean().required('Required'),
  show_on_safe: yup.boolean().required('Required'),
  add_at_top : yup.boolean(),
  shuffle_sequence : yup.boolean(),
});

export type AddTagSchemaType = yup.InferType<
  typeof  AddTagSchema
>;
