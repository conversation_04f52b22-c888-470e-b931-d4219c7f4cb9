.container {
  width: 322px;
  height: 520px;
  padding: 20px 20px 19px;
  border-radius: 20px;
  border-style: solid;
  border-width: 1px;
  border-image-source: radial-gradient(circle at 30% 4%, #fff, rgba(255, 255, 255, 0) 15%);
  border-image-slice: 1;
  background-image: linear-gradient(to bottom, #191a20, #191a20), radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
  background-origin: border-box;
  position: relative;
  overflow: hidden;
}

.summarySection {
  margin: 12px 0px 0px 0px;
  box-shadow: inset -2.1px -2px 4.1px 0 #000;
  border: solid 1px transparent;
  background: linear-gradient(#19191e, #19191e) padding-box, linear-gradient(to top, rgba(0, 0, 0, 0) 79%, rgba(255, 255, 255, 0.2901960784) 150%) border-box;
  background-color: rgba(217, 217, 217, 0.04);
  padding: 10px;
  border-radius: 13px 13px 0px 0px;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;

  &:last-child {
    margin-bottom: 0px;
  }

  .summaryRowLbl {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
  }

  .summaryRowNum {
    font-family: Inter;
    font-size: 18px;
    line-height: 1;
    text-align: right;
    color: #fff;
  }

  &.muted {

    .summaryRowLbl,
    .summaryRowNum {
      color: rgba(255, 255, 255, 0.4);
    }

  }

  &.total {
    padding: 1rem 0;
    font-weight: 600;
    font-size: 1.125rem;
  }
}

.totalPurchase {
  width: 100%;
  padding: 6px 10px 10px;
  background-image: linear-gradient(354deg, #000 142%, #191a20 4%);
  border-radius: 0px 0px 13px 13px;

  .totalPurchaseLbl {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
  }

  .totalPurchaseNum {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: right;
    color: #fff;
  }
}

.disclaimer {
  margin: 12px 0px 16px 0px;
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  line-height: normal;
  letter-spacing: -0.36px;
  text-align: center;
  color: rgba(255, 255, 255, 0.4);
}

.netTermsContainer {
  border: 1px solid #374151;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
  cursor: pointer;
  position: relative;
}

.netTermsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.netTermsAmount {
  color: #4ade80;
  font-weight: 600;
}

.netTermsIcon {
  color: #9ca3af;
  transition: transform 0.2s;

  &.open {
    transform: rotate(180deg);
  }
}

.netTermsDropdown {
  position: absolute;
  left: 0;
  top: 100%;
  margin-top: 0.25rem;
  width: 100%;
  background-color: #1f2937;
  border: 1px solid #374151;
  border-radius: 0.375rem;
  padding: 0.5rem;
  z-index: 10;
}

.netTermsOption {
  padding: 0.5rem;
  border-radius: 0.375rem;

  &:hover {
    background-color: #374151;
  }
}

.orderButton {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0 0;
  border-radius: 10px;
  background-color: #222329;
  font-family: Syncopate;
  font-size: 18px;
  font-weight: bold;
  line-height: 1.3;
  letter-spacing: -0.72px;
  text-align: center;
  color: rgba(255, 255, 255, 0.4);
  text-transform: uppercase;
  transition: all 0.1s;

  &:hover {
    box-shadow: 0 -4px 4px 0 rgba(0, 0, 0, 0.8);
    // background-image: linear-gradient(300deg, #ff7759 113%, #ff532d -1%);
    // background-image: linear-gradient(294deg, #ff7759 109%, #ff532d 3%);
    // -webkit-background-clip: text;
    // background-clip: text;
    // -webkit-text-fill-color: transparent;
    background-color: #302224;
    color: #ff7759;
  }

  &:focus-visible {
    box-shadow: 0 -4px 4px 0 rgba(0, 0, 0, 0.8);
    background-color: #302224;
    color: #ff7759;
  }
  &:disabled {
    cursor: not-allowed;
    &:hover {
      color: rgba(255, 255, 255, 0.4);
      background-color: #222329;
      box-shadow: none;
    }
  }
}

// Animation classes
.fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.slideUp {
  // animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menuSlideUp {
  animation: slideUp 250ms ease-out forwards;
  transform-origin: top center;
}

.dropdownList {
  animation: slideUp 250ms ease-out forwards;
  transform-origin: top center;
}

.dropdownDataMain.dropdownDataMain{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .paymentMethod {
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.3;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
  }
  .paymentValue {
    display: flex;
    justify-content: center;
    align-self: stretch;
    flex-grow: 0;
    font-family: Inter;
    font-size: 15px;
    font-weight: normal;
    line-height: 1.3;
    text-align: center;
    color: #32ff6c;
  }
}

.dropdownList.dropdownList{
  border-radius: 10px;
 position: relative;
  background-color: rgba(128, 130, 140, 0.28);
  padding: 4px;
  max-width: 280px;
  -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
  top: 308px !important;
  .muiMenuList{
      padding: 0px;
      z-index: 2;

      li{
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1;
        margin-bottom: 3px;
        padding: 10px 8px;
        text-align: center;
        height: 41px;
        width: 100%;
        color: rgba(255, 255, 255, 0.6);
        justify-content: center;
        div{
          display: flex;
          flex-direction: column;
          row-gap: 3px;
          i{
            font-weight: 300;
            font-size: 10px;
          }
        }
        &:hover {
          border-radius: 10px;
          background-color: rgba(255, 255, 255, 0.2);
          color: #fff;
          font-weight: bold;
        }
        // &[aria-selected="true"] {
        //   border-radius: 10px;
        //   // background-color: rgba(255, 255, 255, 0.2);
        //   color: #fff;
        //   font-weight: bold;
        // }
        &:focus{
          border-radius: 10px;
          background-color: rgba(255, 255, 255, 0.2);
          color: #fff;
          font-weight: bold;
        }
    }
  }
}
.creditLimitTooltip.creditLimitTooltip {
  padding: 10px 25px;
  max-width: 282px;
  height: 60px;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.15);
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.7px;
  text-align: center;
  color: #fff;
  margin-bottom: 16px !important;
  .tooltipArrow {
    color: rgba(255, 255, 255, 0.15);
  }
}

.methodOfPayment {
  display: flex;
  align-content: center;
  justify-content: center;
  width: 100%;
  height: 50px;
  border-radius: 10px;
  border: solid 1px rgba(255, 255, 255, .25);
  background-color: #191a20;
  padding: 0;
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.3;
  text-align: center;
  color: #fff9;
  align-items: center;
  position: relative;
}

.dropdownIcon {
  position: absolute;
  right: 24px;
  top: 14px;
  svg {
    width: 20px;
    height: 20px;
  }
}

