import { useQuery } from "@tanstack/react-query";
import axios from "axios";

const useGetUserSpreadRate = (user_id: string) => {
  return useQuery([user_id], async () => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/user/spread-rate/${user_id}`
      );
      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default useGetUserSpreadRate;
