import React, { useContext, useEffect, useState } from 'react'
import useGetAllUser from '../../hooks/useGetAllUser';
import { Autocomplete, FilterOptionsState, TextField } from '@mui/material';
import styles from "./FetchLogs.module.scss";
import Loader from '../../components/common/Loader';
import MatPopup from '../../components/common/MatPopup';
import usePostBroadcastNotifications from '../../hooks/usePostBroadcastNotifications';
import { CommonCtx } from '../AppContainer';

function FetchUserLogs() {
    const [inputValue, setInputValue] = useState("");
    const [selectedUsers, setSelectedUsers]: any = useState([])
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const [showConfirmationPopup, setShowConfirmationPopup] = useState(false)

    const {
        data: allUsers,
        isLoading: allUsersLoading,
        isFetching: allUsersFetching,
    } = useGetAllUser();

    const {
        mutate: sendGenerateNotification,
        data: sendGenerateNotificationData,
        isLoading: isSendGenerateNotificationLoading,
    } = usePostBroadcastNotifications();

    useEffect(() => {
        if (sendGenerateNotificationData) {
            showPopupFormAnyComponent(sendGenerateNotificationData);
        }
    }, [sendGenerateNotificationData]);


    const getFilterOptions = (options: any[], state: FilterOptionsState<any>) => {
        return options.filter(option => {
            const keyword = state.inputValue?.toLocaleLowerCase();
            const firstName = option.first_name?.toLocaleLowerCase();
            const lastName = option.last_name?.toLocaleLowerCase();
            const email = option.email_id?.toLocaleLowerCase();

            return firstName?.includes(keyword) || lastName?.includes(keyword) || email?.includes(keyword);
        });
    };

    const removeUser = (indexToRemove: number) => {
        setSelectedUsers((selectedUsers: any) =>
            selectedUsers.filter((_, index: number) => index !== indexToRemove)
        );
    };

    const fetchUserLogs = () => {
        const userIds = selectedUsers.map(user => user.userId);
        const payload = {
            data: {
                "notification_event": "USER_UI_UPLOAD_LOG",
                "send_to": "USERS",
                "send_to_users": userIds,
            }
        }
        sendGenerateNotification(payload)
    }

    const confirmationPopupYes = () => {
        fetchUserLogs()
        confirmationPopupClose();
        setSelectedUsers([])
    };

    const confirmationPopupClose = () => {
        setShowConfirmationPopup(false);
    };

    return (
        <div className={styles.fetchUserLogsContainer}>
            <h3>Fetch Users Log</h3>
            <div>
                <div className={styles.userListContainer}>
                    {(allUsersLoading || allUsersFetching || isSendGenerateNotificationLoading) ? (
                        <Loader />
                    ) : (
                        <div>
                            <div className={styles.inputSection}>
                                <Autocomplete
                                    value={null}
                                    options={allUsers}
                                    inputValue={inputValue}
                                    getOptionLabel={(option: any) => `${option.first_name} ${option.last_name} ${option.email_id}`}
                                    renderInput={(params) => (
                                        <TextField {...params} label="Select Users" />
                                    )}
                                    classes={{
                                        root: styles.autoCompleteSendNotifi,

                                    }}
                                    onInputChange={(e, value) => setInputValue(value)}
                                    onChange={(e, data: any) => {
                                        if (data) {
                                            setInputValue("");
                                            // prepend({ userId: data.id, firstName: data.first_name, lastName: data.last_name, email: data.email_id });
                                            setSelectedUsers((selectedUsers: any) => [...selectedUsers, { userId: data.id, firstName: data.first_name, lastName: data.last_name, email: data.email_id }])
                                        }
                                    }}
                                    renderOption={(props, option) => <span {...props} key={option.id}>{option.first_name} {option.last_name}<br />{option.email_id}</span>}
                                    filterOptions={getFilterOptions}
                                    getOptionDisabled={(option) => selectedUsers.some(user => user.userId === option.id)}
                                />
                            </div>
                            {selectedUsers?.length > 0 && <div className={styles.tblscroll}>
                                <table>
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>First Name</th>
                                            <th>Last Name</th>
                                            <th>Email</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {selectedUsers.map((user, i) => (
                                            <tr key={user.id}>
                                                <td className={styles.delRow} onClick={() => removeUser(i)}>x</td>
                                                <td>{user.firstName}</td>
                                                <td>{user.lastName}</td>
                                                <td>{user.email}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>}

                        </div>
                    )}
                </div>
                <div className={styles.fetchLogsBtn}>
                    <button disabled={allUsersLoading || allUsersFetching || selectedUsers.length <= 0} onClick={() => setShowConfirmationPopup(true)}>Fetch Log</button>
                </div>

            </div>
            <MatPopup
                className={styles.fetchLogsContinuePopup}
                open={!!showConfirmationPopup}
            >
                <div className={styles.fetchContinuePopup}>
                    <p className={styles.fetchContinuetext}>Do you want to continue ?</p>
                    <div className={styles.yesAndnoBtn}>
                        <button className={styles.okBtn} onClick={confirmationPopupYes}>
                            Yes
                        </button>
                        <button className={styles.okBtn} onClick={confirmationPopupClose}>
                            No
                        </button>
                    </div>
                </div>
            </MatPopup>
        </div>

    )
}

export default FetchUserLogs