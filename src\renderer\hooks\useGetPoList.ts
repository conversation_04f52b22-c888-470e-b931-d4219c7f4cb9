import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";
import { GetPOsDto } from "@bryzos/giss-common-lib";

const useGetPoList = () => {
  return useQuery([reactQueryKeys.getPos], async () => {
    try {
      const response: GetPOsDto = (await axios.get(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/cass/getPos`)).data;
      if (response.data) {
        return response.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default useGetPoList;
