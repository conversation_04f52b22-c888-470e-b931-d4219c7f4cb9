    div.selectDropdown {
        background-color: #fff;
        width: 100%;
        padding: 8px 0px;
        border-radius: 5px;
        border: solid 1px #626b84;
        background-color: #fff;
        font-size: 14px;
        line-height:normal;
        text-align: left;
        color: #525f7f;
        border: none;
      
        input {
            height: 100%;
        }
      
        // &.Mui-focused {
        //     fieldset.MuiOutlinedInput-notchedOutline {
        //         border-color: #397aff;
        //         border-width: 1px;
        //     }
        // }
      
        .MuiSelect-select {
            padding: 0px;
            padding-right: 15px;
        }
        &.Mui-focused{
            border: 1px solid #000;
        }
        &.MuiInputBase-root {
            .MuiSelect-select {
              border: 1px solid transparent;
            
            }
            fieldset {
              border-color: #626b84;
            }
            &:hover {
              .MuiSelect-select {
                border: 1px solid transparent;
              }
            }
        
            &.Mui-focused {
              .MuiSelect-icon {
                transform: unset;
              }
        
              fieldset {
                border-color: #397aff;
                border-width: 1px;
              }
            }
          }
        
      }

      .MuiInputBase-root{
        &:focus{
          fieldset{
            border: 1px solid #397aff;
          }
        }
      }