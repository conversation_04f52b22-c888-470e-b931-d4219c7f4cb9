//@ts-nocheck
import React, { useEffect, useRef, useState } from 'react';
import styles from './VideoPlayer.module.scss';
import { ReactComponent as VideoPlayIcon } from '../../../assests/images/VideoPlay-Icon.svg';
import { ReactComponent as VideoPlayControlIcon } from '../../../assests/images/Play.svg';
import { ReactComponent as VideoPauseIcon } from '../../../assests/images/Pause.svg';
import { ReactComponent as VolumeIcon } from '../../../assests/images/UnMute.svg';
import { ReactComponent as VolumeMutedIcon } from '../../../assests/images/Mute.svg';
import { ReactComponent as FullScreenIcon } from '../../../assests/images/Fullscreen.svg';
import { ReactComponent as PlayNext } from '../../../assests/images/Skip.svg';
import Poster from "../../../assests/images/black.png";
import clsx from 'clsx';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { ReactComponent as ExitFullScreenIcon } from '../../../assests/images/ExitFullScreen.svg';
import { CircularProgress } from '@mui/material';
import { useLoadSubtitle } from '../../hooks/useLoadSubtitle';
 

const VideoPlayer = ({ url, width, height, autoPlay, id, videoRef, crossOrigin, playNextVideo , disabled, onPlay , isPlayingPrev = true, captionUrl , captionBlobUrl , showFullScreen = true, setIsPageLoading , fromSafeIntro = false}: any) => {
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const containerRef = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const {emitAppCloseEvent, setEmitAppCloseEvent}: any = useGlobalStore();
  const [isBuffering, setIsBuffering] = useState(false);
  const [subtitleUrl, setSubtitleUrl] = useState<string | null>(null);
  const { data: subtitle, error, isLoading: isSubtitleLoading } = useLoadSubtitle(captionUrl);

  useEffect(() => {
    if (!isSubtitleLoading && subtitle && !captionBlobUrl) {
      const subtitleBlob = new Blob([subtitle], { type: 'text/vtt' });
      const objectUrl = URL.createObjectURL(subtitleBlob);
      setSubtitleUrl(objectUrl);
  
      return () => {
        // Clean up the object URL when the component unmounts or subtitle changes
        URL.revokeObjectURL(objectUrl);
      };
    }
  }, [isSubtitleLoading, subtitle]);
  
  useEffect(()=>{
      setSubtitleUrl(captionBlobUrl);
  },[captionBlobUrl]);
  
  useEffect( () => {
    if(!captionBlobUrl && !captionUrl) {
      setSubtitleUrl(captionBlobUrl);
    }
  })

  const togglePlay = () => {
    if (videoRef.current.paused) {
      videoRef.current.play();
      setIsBuffering(false);
      setIsPlaying(true);
    } else {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };

  const updateTime = () => {
    setCurrentTime(videoRef.current.currentTime);
    setDuration(videoRef.current.duration);
  };

  const handleSeek = (e) => {
    const seekTime = (e.target.value / 100) * duration;
    videoRef.current.currentTime = seekTime;
    setCurrentTime(seekTime);
  };

  const handleVolumeChange = (e) => {
    const vol = parseFloat(e.target.value);
    setVolume(vol);
    videoRef.current.volume = vol;
    if (vol === 0) {
      setIsMuted(true);
    } else {
      videoRef.current.muted = false;
      setIsMuted(false);
    }
  };

  useEffect(() => {
    const handleFullScreenChange = () => {
      if (!document.fullscreenElement) {
        console.log('Exited full-screen mode');
        setIsFullscreen(false);
      }
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
    };
  }, []);

  useEffect( () => {
     if(!isPlayingPrev) {
       videoRef.current.pause();
       setIsPlaying(false);
     }
  })

  const toggleMute = () => {
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);
    videoRef.current.muted = newMutedState;
    if (newMutedState) {
      setVolume(0);
    } else {
      setVolume(1);
      videoRef.current.volume = 1;
    }
  };

  useEffect( () => {
    if(fromSafeIntro) {  
        setIsMuted(true);
        setVolume(0);
         videoRef.current.volume = 0;
      }    
   } , [])

  
  const formatTime = (time) => {
    if (isNaN(time)) return '00:00';

    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  };

  const toggleFullscreen = () => {
    const container = containerRef.current;
    if (!isFullscreen) {
      if (container.requestFullscreen) {
        container.requestFullscreen();
      } else if (container.mozRequestFullScreen) {
        container.mozRequestFullScreen();
      } else if (container.webkitRequestFullscreen) {
        container.webkitRequestFullscreen();
      } else if (container.msRequestFullscreen) {
        container.msRequestFullscreen();
      }
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
      setIsFullscreen(false);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    if(setIsPageLoading) setIsPageLoading(true) 
  };

  useEffect(()=>{
    if(emitAppCloseEvent && videoRef?.current){
      if(!videoRef?.current.paused){
        videoRef.current.pause();
        setIsPlaying(false);
        setEmitAppCloseEvent(false);
      } 
    }
  }, [emitAppCloseEvent])

  useEffect(() => {
    if(url){
      setIsPlaying(autoPlay)
      setIsBuffering(autoPlay)
    }
  }, [url,autoPlay]);

  const progress = (currentTime / duration) * 100 || 0;

  const handleOnPlaying = () => {
    setIsBuffering(false)
  };
  
  const handleOnWaiting = () => {
    setIsBuffering(true) 
  };
  

  return (
    <div className={clsx(styles['videoPlayerMain'],'videoPlayerMain','subTitleStyle')} ref={containerRef}>
      <div className={clsx(styles['custom-video-player'],'custom-video-player', isFullscreen && styles['fullScreenHeight'])}>
      {isBuffering && <div className={styles.videoLoading}>
           <CircularProgress classes={{
            circle:styles.colorLoader
           }} />
        </div>}        
        <video
          ref={videoRef}
          controls={false}
          onTimeUpdate={updateTime}
          onLoadedMetadata={updateTime}
          className={styles.video}
          autoPlay={autoPlay}
          onEnded={handleEnded}
          width={width}
          height={height}
          src={url}
          id={id}
          crossOrigin={crossOrigin}
          poster={Poster}
          onPlay={onPlay}
          onWaiting={handleOnWaiting}
          onPlaying={handleOnPlaying}          
        >
            {(subtitleUrl) &&
            <track 
            kind="subtitles" 
            src={subtitleUrl} 
            srcLang="en" 
            label="English" 
            default />
          }
          Your browser does not support the video tag.
        </video>
        <div className={clsx(styles['overlay'], {[styles['noOverlay']] : isPlaying })} onClick={togglePlay}>
            <span className={clsx(styles['VideoPlayIcon'],'playNextBtn')}>
              {!isPlaying && <VideoPlayIcon />}
            </span>
          </div>
        { (
          <div className={clsx(styles['controls'],'controls')}>
            <div className={styles['seek-container']}>
              <input
                type="range"
                min="0"
                max="100"
                value={progress}
                className={styles['seek-bar']}
                onChange={handleSeek}
                style={{ '--progress': `${progress}%` }}
              />
            </div>
            <div className={styles['action-container']}>
            <div className={clsx(styles['leftbar-action'],'leftbar-action')}>
              <button onClick={togglePlay}>
                {isPlaying ? <VideoPauseIcon /> : <VideoPlayControlIcon />}
              </button>
              {playNextVideo && 
                <button className={styles.playNextBtn} onClick={playNextVideo} disabled={disabled}>
                  <PlayNext  />
                </button>
              }
              <div className={clsx(styles['time-display'],'time-display')}>
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
              
              <button className={styles['volume-icon']} onClick={toggleMute}>
                {isMuted ? ( <VolumeMutedIcon />) : <VolumeIcon />}
              </button>
              <input
                type="range" 
                min="0"
                max="1"
                step="0.01"
                value={volume}
                onChange={handleVolumeChange}
                className={styles['volume-bar']}
                style={{
                  background: `linear-gradient(to right, #fff ${volume * 100}%, rgba(156, 163, 175, 0.6) ${volume * 100}%)`
                }}
              /> 

            </div>
            { (showFullScreen) &&  <button className="fullscreen-button" onClick={toggleFullscreen}>
            {!isFullscreen ? <FullScreenIcon/> : <ExitFullScreenIcon/> }  
            </button>}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;
