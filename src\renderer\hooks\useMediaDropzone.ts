import { useCallback, useContext, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { CommonCtx } from '../pages/AppContainer';

const useMediaDropzone = (requiredWidth: number, requiredHeight: number, type = 'image' ) => {
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const [media, setMedia] = useState<string | ArrayBuffer | null>(null);
    const [error, setError] = useState('');
    const onDrop = useCallback((acceptedFiles: any[]) => {
        if (acceptedFiles.length) {
            const file = acceptedFiles[0];
            const ext = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();


            if (type === 'image') {

                const allowedExtensions = ['.png', '.jpeg', '.jpg', '.webp', '.heif'];
                if (allowedExtensions.indexOf(ext) === -1) {
                    showPopupFormAnyComponent(`${file.name} is not supported.`)
                    return;
                }

                const img = new Image();

                img.onload = () => {
                    const reader = new FileReader();
                    reader.readAsDataURL(acceptedFiles[0]);
                    reader.onload = (e) => {
                        if (e?.target)
                            setMedia(e.target.result);
                    };
                  };
                  img.src = URL.createObjectURL(file);
            }
        }
    }, [requiredWidth, requiredHeight, type]);

    const dropzoneProps = useDropzone({
        onDrop,
        noClick: true
    });

    return { dropzoneProps, media, error, setError, setMedia };
};

export default useMediaDropzone;
