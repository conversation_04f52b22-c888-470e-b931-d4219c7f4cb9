.cassRow {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px
}

.cassCollg3,
.cassColmd6,
.cassColsm12 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px
}


@media (min-width:576px) {
    .cassColsm12 {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        flex-grow: 1;
    }
}

@media (min-width:768px) {
    .cassColmd6 {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        flex-grow: 1;
    }
}

@media (min-width:992px) {
    .cassCollg3 {
        -ms-flex: 1 0 20%;
        flex: 1 0 20%;
        flex-grow: 1;
        max-width: 36%;
    }
}

.colum1.colum1 {
        table {
            border-spacing: 0px;
            border-collapse: collapse;

            tr {

                &:nth-child(2) td,
                &:nth-child(4) td,
                &:nth-child(6) td,
                &:nth-child(8) td {
                    background-color: #fff1eb;
                }

                &:nth-child(4) td,
                &:nth-child(6) td {
                    border-top: 1px solid #000;
                    border-bottom: 1px solid #000;
                }

                &:nth-child(7) td {
                    height: 48px;
                }
            }
        }
}

.colum2.colum2 {
        table {
            border-spacing: 0px;
            border-collapse: collapse;

            tr {

                &:nth-child(2) td,
                &:nth-child(4) td,
                &:nth-child(6) td {
                    background-color: #edeff2;
                }

                &:nth-child(4) td,
                &:nth-child(6) td {
                    border-top: 1px solid #000;
                    border-bottom: 1px solid #000;
                }

                &:nth-child(7) td {
                    height: 48px;
                }
            }
        }
}

.colum3.colum3 {
        table {
            border-spacing: 0px;
            border-collapse: collapse;

            tr {

                &:nth-child(2) td,
                &:nth-child(4) td {
                    background-color: rgb(237 239 242 / 42%);
                }

                &:nth-child(4) td {
                    border-top: 1px solid #000;
                    border-bottom: 1px solid #000;
                }

                &:nth-child(6) td {
                    height: 48px;
                }
            }
        }
}

.colum4.colum4 {
        table {
            border-spacing: 0px;
            border-collapse: collapse;

            tr {

                &:nth-child(2) td,
                &:nth-child(4) td,
                &:nth-child(6) td {
                    background-color: rgb(139 195 74 / 40%);
                }

                &:nth-child(4) td,
                &:nth-child(6) td {
                    border-top: 1px solid #000;
                    border-bottom: 1px solid #000;
                }

                &:nth-child(7) td {
                    height: 48px;
                }
            }
        }
}

.colum5.colum5 {
   
        table {
            border-spacing: 0px;
            border-collapse: collapse;

            tr {

                &:nth-child(2) td,
                &:nth-child(4) td {
                    background-color: rgb(158 158 158 / 40%);
                }

                &:nth-child(4) td,
                &:nth-child(6) td {
                    border-top: 1px solid #000;
                    border-bottom: 1px solid #000;
                }

                &:nth-child(7) td {
                    height: 48px;
                }
            }
        }
}

.colum1,
.colum2,
.colum3,
.colum4,
.colum5 {
    padding-left: 10px;
    padding-right: 10px;
    position: relative;
    margin-bottom: 20px;

    .overlay-payment {
        position: absolute;
        background-color: rgba(0, 0, 0, 0.4);
        width: calc(100% - 30px);
        height: 100%;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        border: 2px solid #000;
        z-index: 99;

        .title-1,
        .title-2 {
            font-size: 26px;
            color: #000;
            margin-bottom: 20px;
        }

        button {
            width: 130px;
            height: 48px;
            border: 0px;
            border-radius: 4px;
            font-size: 18px;
            font-weight: 500;
        }
    }
}


.colum3 {
    table {
        position: relative;
        z-index: 1;
    }
}

.paymentHeader {
    height: 55px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 5px 16px;
    font-size: 20px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: normal;
    justify-content: center;
    color: #fff;
    white-space: break-spaces;
    text-align: center;

    @media (max-width:1600px) {
        font-size: 13px;
        padding: 0px 8px;
    }

    span {
        font-size: 0.8em;
    }
}

.paymentHeader1 {
    background-color: #fe753e;
    height: auto;
    min-height: 55px;
}

.paymentHeader2 {
    background-color: #626b84;
}

.paymentHeader3 {
    background-color: #b22234;
}

.paymentHeader4 {
    background-color: #8bc34a;
}

.paymentHeader5 {
    background-color: #9E9E9E;
}

.cassMainTable {
    table {
        width: 100%;

        tr {
            td {
                font-size: 14px;
                font-weight: normal;
                line-height: 1.57;
                letter-spacing: normal;
                color: #626b84;
                padding: 0px 10px;
                height: 48px;

                &.accountNumberTD{
                    padding-left: 28px;
                }

                &:nth-child(1) {
                    width: 50%;
                    padding-right: 0px;
                    font-weight: 700;
                }

                &:nth-child(2) {
                    width: 50%;
                }

                @media (max-width:1600px) {
                    font-size: 12px;
                }

                @media (max-width:1300px) {
                    font-size: 10px;
                    padding: 0px 8px;
                }

                @media (max-width:767px) {
                    font-size: 12px;
                    padding: 0px 10px;
                }
            }

            td.input-pad {
                padding: 0px 10px;

                @media (max-width:1300px) {
                    padding: 0px 8px;
                }
            }

            td.noTaxesDueTitle {
                font-size: 36px;
                color: #b22234;
                text-align: center !important;
                font-weight: 600;
                height: 95px;

                @media (max-width:1600px) {
                    font-size: 30px;
                }

                @media (max-width:1300px) {
                    font-size: 26px;
                }
            }


            textarea,
            input {
                width: 100%;
                border-radius: 4px;
                border: solid 1px #c4c8d1;
                font-size: 14px;
                font-weight: normal;
                line-height: 1.57;
                letter-spacing: normal;
                text-align: left;
                color: #3b4665;
                height: 34px;
                padding: 0px 10px;

                &[type='checkbox']{
                    height: 15px;
                    width: 15px;
                    margin-right: 6px;
                    margin-bottom: 0px;
                    vertical-align: middle;
                    margin-top: -2px;
                }

                .adHocBryzosBuyerLbl{
                    line-height: 1;
                    
                }
                

                @media (max-width:1600px) {
                    font-size: 12px;
                }

                @media (max-width:1300px) {
                    font-size: 10px;
                    padding: 0px 8px;
                }

                @media (max-width:767px) {
                    font-size: 12px;
                    padding: 0px 12px;
                }

                &:focus {
                    outline: none;
                }
            }

            input {
                margin: 6px 0px;
            }

            textarea {
                height: 80px;
                padding: 16px;
                resize: none;

                @media (max-width:1600px) {
                    padding: 10px;
                }

                @media (max-width:1300px) {
                    padding: 8px;
                }

                @media (max-width:767px) {
                    padding: 12px;
                }
            }

            .cassInput {
                height: 80px;
                padding: 16px;
                resize: none;

                @media (max-width:1600px) {
                    padding: 10px;
                }

                @media (max-width:1300px) {
                    padding: 8px;
                }

                @media (max-width:767px) {
                    padding: 12px;
                }
            }
        }
    }

    .paymentAmountInput {
        margin: 0px 10px;

        @media (max-width:1600px) {
            font-size: 14px;
            padding: 0px 12px;
        }

        @media (max-width:1300px) {
            font-size: 10px;
            padding: 0px 4px;
            margin: 0px 4px;
        }

        @media (max-width:767px) {
            font-size: 12px;
            padding: 0px 4px;
            margin: 0px 5px;
        }
    }

    .negValue {
        color: red;
    }
}

.selectDropdown {
    width: 100%;
    max-width: 350px;
    height: 48px;
}

.dFlex {
    display: flex;
}

.aligncenter {
    align-items: center;
}

.vLine {
    border: 2px solid #f0f0f0;
    height: 110%;
    width: 4px;
    position: absolute;
    left: -2px;
    top: -40px;
}

.bgImg {
    opacity: 0.4;
    position: absolute;
    z-index: -1;
    height: 70%;
    width: 100%;
    object-fit: cover;
}

.submitBtn {
    border: 0px;
    width: 160px;
    height: 45px;
    font-size: 16px;
    text-align: center;
    color: #3b4665;
    font-weight: 500;
    margin-top: 10px;
    margin-bottom: 10px;
    background-color: #eee;
    cursor: pointer;

    &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }

    @media (max-width:1500px) {
        width: 140px;
        height: 40px;
        font-size: 14px;
    }
}

.autocompleteDescPanel {
    border-radius: 4px;
    width: 100%;
    max-width: 350px;
    height: 48px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    background-color: #fff;
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    padding-right: 4px;
    border-radius: 0px 0px 4px 4px;
}

.listAutoComletePanel.listAutoComletePanel {
    width: 100%;
    max-height: 316px;
    padding: 6px 4px 6px 10px;
    margin-top: 4px;
    li{
        ul{
            padding: 0px;
        }
    }
   

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }


    span {
        font-family: Noto Sans;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        box-shadow: none;
        padding: 4px 8px;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 4px;

        &:hover {
            border-radius: 2px;
            background-color: #fff;
            color: #000;
        }

        &[aria-selected="true"] {
            // background-color: #EBF2FF;
            // color: #397aff;
            background-color: #fff;
            color: #000;
        }
    }
}
.listAutoComletePanel1.listAutoComletePanel1 {
    width: 100%;
    max-height: 316px;
    padding: 6px 4px 6px 10px;
    margin-top: 4px;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

}

.headingErrors {
    color: red;
}

.loaderImg {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 200px;
}

.disableBryzosHolding {
    cursor: not-allowed;
}

.disableSelect {
    -webkit-user-select: none;
    /* Safari */
    -ms-user-select: none;
    /* IE 10 and IE 11 */
    user-select: none;
    /* Standard syntax */
}
.textareaAdHoc.textareaAdHoc{
    textarea {
        height: 80px;
        padding: 16px;
        resize: none;
        width: 100%;
    
        @media (max-width:1600px) {
            padding: 10px;
        }
    
        @media (max-width:1300px) {
            padding: 8px;
        }
    
        @media (max-width:767px) {
            padding: 12px;
        }
    }
}

.getDataBtn{
    text-align: center;
    padding: 6px 2px;
    height: 40px;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    background-color: #343a40;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    margin-top: 8px;
    margin-bottom: 8px;
    width: 30%;
    
    &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }
}

.createNewAccount{
    text-align: center;
    height: 40px;
    padding: 6px 8px;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    background-color: #343a40;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    margin-top: 10px;
    margin-bottom: 45px;
    margin-left: 8px;
    @media (max-width:767px) {
        margin-bottom: 0px;
    }

    
    &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }
}

.tblscrollSelectUser.tblscrollSelectUser {
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 0px;
    max-height: 700px;
    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }
    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }
    table {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        border-collapse: collapse;
        border-spacing: 0;
        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }
        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }
        thead {
            th {
                line-height: 1.2;
                font-weight: 600;
                font-size: 14px;
                margin: 0;
                text-align: left;
                padding: 6px 12px;
                color: #fff;
                height: 35px;
                position: sticky;
                top: 0;
                background: #676f7c;
                color: #fff;
            }
        }
        tbody {
            background-color: #fff;
            tr {
                margin: 0;
                &:nth-child(even) {
                    background-color: #f2f2f2;
                }
                td {
                    color: #343a40;
                    font-size: 14px;
                    margin: 0;
                    padding: 4px 8px;
                    height: 42px;
                   
                }
            }
        }
    }
}

.createNewAccountMain{
    .crossBtn{
        background-color: transparent;
        padding: 0px;
        position: absolute;
        right: 12px;
        top: 10px;
        cursor: pointer;
        border: 0px;
        font-size: 20px;
    }
}

.adhocMainTable{
    tr {
        td {
            font-size: 14px;
            font-weight: normal;
            line-height: 1.57;
            letter-spacing: normal;
            color: #626b84;
            padding: 0px 10px;
            height: 38px;

            &:nth-child(1) {
                width: 50%;
                padding-right: 0px;
            }

            &:nth-child(2) {
                width: 50%;
            }

            @media (max-width:1600px) {
                font-size: 12px;
            }

            @media (max-width:1300px) {
                font-size: 10px;
                padding: 0px 8px;
            }

            @media (max-width:767px) {
                font-size: 12px;
                padding: 0px 10px;
            }
        }


        textarea,
        input {
            width: 100%;
            border-radius: 4px;
            border: solid 1px #c4c8d1;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.57;
            letter-spacing: normal;
            text-align: left;
            color: #3b4665;
            height: 34px;
            padding: 0px 10px;

            &[type='checkbox']{
                height: 15px;
                width: 15px;
                margin-left: 6px;
                margin-bottom: 0px;
                vertical-align: sub;
            }
            

            @media (max-width:1600px) {
                font-size: 12px;
            }

            @media (max-width:1300px) {
                font-size: 10px;
                padding: 0px 8px;
            }

            @media (max-width:767px) {
                font-size: 12px;
                padding: 0px 12px;
            }

            &:focus {
                outline: none;
            }
        }

        input {
            margin: 6px 0px;
        }
    }
    .paymentAmountInput {
        margin: 0px 10px;
    
        @media (max-width:1600px) {
            font-size: 14px;
            padding: 0px 12px;
        }
    
        @media (max-width:1300px) {
            font-size: 10px;
            padding: 0px 4px;
            margin: 0px 4px;
        }
    
        @media (max-width:767px) {
            font-size: 12px;
            padding: 0px 4px;
            margin: 0px 5px;
        }
    }
}
.getUserRow{
    display: flex;
    padding-top: 5px;
    height: 96px;
    align-items: center;
    gap: 10px;
}


.errorStyle.errorStyle {
    .tooltip.tooltip {
      background-color: #ff5d47;
      font-size: 11px;
      line-height: 1.6;
      text-align: left;
      color: #fff;
      text-transform: capitalize;
      margin-bottom: 5px;
    }
  
  }