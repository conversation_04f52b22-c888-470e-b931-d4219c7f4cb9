import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostCompleteMultiPartUpload = () => {
  
  return useMutation(async (data: any) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/videos/complete-multipart-upload`,{
            data:   {
                bucket_name: import.meta.env.VITE_S3_UPLOAD_VIDEO_THUMBNAIL_BUCKET_NAME,
                parts: data.parts, 
                upload_id: data.upload_id, 
                file_name: data.file_name,
                expire_time: 300
            }
        }
      );
      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostCompleteMultiPartUpload;
