import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

const useOnboardUser = () => {
  const queryClient = useQueryClient();
  return useMutation(async (data: any) => {
    try {
      const url = `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/user/on-board`;
      const response = await axios.post(url, data);
      queryClient.invalidateQueries([reactQueryKeys.getAllPendingOnBoardUsers]);
      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          const errorMessage = JSON.stringify(response.data.data)
          throw new Error(errorMessage);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message ?? "");
    }
  });
};

export default useOnboardUser;
