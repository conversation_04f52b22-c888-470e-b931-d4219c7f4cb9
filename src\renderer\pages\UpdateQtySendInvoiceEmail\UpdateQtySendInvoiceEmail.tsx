import { Select, MenuItem, Popper, Box, Tooltip } from "@mui/material";
import React, { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import Loader from "../../components/common/Loader";
import MatPopup from "../../components/common/MatPopup";
import useGetOrderToUpdateQtySendInvoiceEmail from "../../hooks/useGetOrderToUpdateQtySendInvoiceEmail";
import useSendEmail from "../../hooks/useSendEmail";
import { format2DecimalPlaces } from "../../utils/helper";
import styles from "./UpdateQtySendInvoiceEmail.module.scss";
import clsx from "clsx";
import EditLinesPopup from "./EditLinesPopup/EditLinesPopup";
import useGetAdminReferenceData from "../../hooks/useGetAdminReferenceData";
import { useDebouncedValue, useTimeout } from "@mantine/hooks";
import { ReactComponent as SendEmailICon } from '../../../assests/images/Email.svg';
import { ReactComponent as AddICon } from '../../../assests/images/add-row.svg';
import { ReactComponent as EditICon } from '../../../assests/images/Edit.svg';
import { ReactComponent as SplitIcon } from '../../../assests/images/Split.svg';
import SplitPO from "../../components/SplitPO/SplitPO";
import useGetAllReferenceDataProductV2 from "../../hooks/useGetAllReferenceDataProductV2";

const UpdateQtySendInvoiceEmail = () => {
  const [apiResponseMessage, setApiResponseMessage] = useState("");
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue, 1000);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const [filteredOrders, setFilteredOrders] = useImmer<any[]>([]);
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [emailData, setEmailData] = useImmer<any>(null);
  const [seditSelectedLine, setEditSelectedLine] = useImmer<any>(null);
  const [addSelectedLine, setAddSelectedLine] = useImmer<any>(null);
  const [showLoader, setShowLoader] = useImmer(false);
  const [invoiceEmailRecipients, setInvoiceEmailRecipients] = useImmer<any>({});
  const [meta, setMeta] = useImmer<any>(null);
  const [showSplitInvoicePopup, setShowSplitInvoicePopup] = useState(false);
  const [splitInvoiceData, setSplitInvoiceData] = useState<any>(null);

  const { data: orders, isLoading: isordersLoading, isFetching: isOrdersFetching } = useGetOrderToUpdateQtySendInvoiceEmail(itemsPerPage, currentPage, debouncedInputSearchValue);
  const { isLoading: isAllReferenceProductDataLoading, data: referenceProducts, isFetching: isAllReferenceProductDataFetching } = useGetAllReferenceDataProductV2();
  const { data: adminReferenceData, isLoading: adminReferenceDataLoading } = useGetAdminReferenceData();

  const [hoveredRow, setHoveredRow] = useState<number | null>(null); 

  const {
    mutate: sendEmail,
    data: sendEmailData,
    isLoading: isSendEmailLoading,
  } = useSendEmail();


  useEffect(() => {
    if (orders?.meta) {
      setMeta(orders.meta);
    }

    if (orders?.data?.length >= 0) {
      setFilteredOrders(orders.data);
    }else{
      setFilteredOrders([])
    }
  }, [orders, isordersLoading, isOrdersFetching]);

  useEffect(() => {
    if (sendEmailData) {
      setApiResponseMessage(sendEmailData);
    }
  }, [sendEmailData]);

  useEffect(() => {
    if(adminReferenceData?.email_extra_recipients?.WIDGET_SERVICE_BUYER_INVOICE){
      setInvoiceEmailRecipients(adminReferenceData.email_extra_recipients.WIDGET_SERVICE_BUYER_INVOICE);
    }
  },[adminReferenceData])

  const search = (searchValue: string) => {
    setCurrentPage(1);
    setInputSearchValue(searchValue);
  };

  const sendEmailClickHandler = (order: any) => {
    setShowConfirmationPopup(true);
    setEmailData(order);
  };


  const handlePageClick = (event: any) => {
    setCurrentPage(event.selected + 1);
  };

  const confirmationPopupYes = () => {
    if (emailData) {
      sendEmail({ data: { po_number: emailData.buyer_po_number } });
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setEmailData(null);
  };

  const handleOpenEditLinesPopup = (order: any) => {
    setEditSelectedLine(order);
  }
  const handleCloseEditLinesPopup = () => {
    setEditSelectedLine(null)
  }

  const handleOpenAddLinesPopup = (order: any) => {
    setAddSelectedLine(order);
  }
  const handleCloseAddLinesPopup = () => {
    setAddSelectedLine(null)
  }

  const handleMouseEnter = (index: number, event: React.MouseEvent<HTMLElement>) => {
    setHoveredRow(index);
  }

  const handleMouseLeave = () => {
    setHoveredRow(null);
  }

  const handleSplitInvoice = (order: any) => {
      console.log(order);
      setShowSplitInvoicePopup(true);
      setSplitInvoiceData(order);
      setHoveredRow(null);
  }


  return (
    <div className="contentMain">
      {showSplitInvoicePopup ? 
        <SplitPO order={splitInvoiceData} onClose={() => setShowSplitInvoicePopup(false)} referenceProducts={referenceProducts} invoiceEmailRecipients={invoiceEmailRecipients} /> 
        : 
        <div>
          <div className={styles.searchBox}>
            <Select className="editLinesDropdown" MenuProps={{
                    classes: {
                        paper: styles.Dropdownpaper,
                        list: styles.muiMenuList,
                    }
                }}
              value={itemsPerPage}
              onChange={(event) => {
                setItemsPerPage(+event.target.value);
              }}
            >
              {perPageEntriesOptions.map((item, index) => (
                <MenuItem key={index} value={item}>
                  <span>{item}</span>
                </MenuItem>
              ))}
            </Select>
            <input
              className={styles.searchInput}
              type="text"
              onChange={(e)=>{search(e.target.value)}}
              placeholder="Search"
              value={inputSearchValue}
            />
          </div>
          <div className={styles.tblscroll}>
            <table>
              <thead>
                <tr>
                  <th>PO Number</th>
                  <th>Buyer Internal PO</th>
                  <th>Items Count</th>
                  <th>Buyer Email</th>
                  <th>Claimed By</th>
                  <th>Buyer PO Price</th>
                  <th>Sales Tax</th>
                  <th>Total Weight</th>
                  <th>Seller PO Price</th>
                  <th>Last Invoice Sent</th>
                  <th></th>
                </tr>
              </thead>
            <tbody>
              {(isordersLoading || isOrdersFetching || isSendEmailLoading || showLoader || isAllReferenceProductDataLoading || isAllReferenceProductDataFetching) ?
                <tr><td colSpan={11}><span className="loaderImg"><Loader /></span></td></tr>
                : filteredOrders?.length ? (
                  filteredOrders
                    .map((order: any, index) => {
                      const orderCount = order.purchase_order_lines.reduce((acc: number, line: any) => { if (!line.is_canceled) acc += 1; return acc; }, 0);
                      return (
                          <tr key={index}
                            className={hoveredRow === index ? styles.activeRow : ""}
                            onMouseEnter={(event) => handleMouseEnter(index, event)}
                            onMouseLeave={handleMouseLeave}
                          >
                            <td id={`row-${index}`}>{order.buyer_po_number}</td>
                            <td>{order.buyer_internal_po}</td>
                            <td>{orderCount}</td>
                            <td>{order.buyer_email}</td>
                            <td>{order.claimed_by}</td>
                            <td>{format2DecimalPlaces(order.buyer_po_price)}</td>
                            <td>{format2DecimalPlaces(order.sales_tax)}</td>
                            <td>{format2DecimalPlaces(order.total_weight)}</td>
                            <td>{format2DecimalPlaces(order.seller_po_price)}</td>
                            <td>{order.last_invoice_sent}</td>
                            <td>
                              <Popper className="sendEmailPopper" placement="left-start" open={hoveredRow === index} anchorEl={hoveredRow === index ? document.querySelector(`#row-${index}`) : null}>
                                <Box>
                                  <Tooltip title={'Split Invoice'}><button className={clsx(styles.sendEmailIconBtn)} onClick={() => handleSplitInvoice(order)} ><SplitIcon /></button></Tooltip>
                                   <Tooltip title={'Send Email'}>
                                     <button className={clsx(styles.sendEmailIconBtn)} onClick={() => { sendEmailClickHandler(order); }}><SendEmailICon /></button>
                                    </Tooltip>
                                  {order.show_add_edit_button === 1 && <>
                                    <Tooltip title={'Edit Line'}><button className={clsx(styles.sendEmailIconBtn)} onClick={() => handleOpenEditLinesPopup(order)}><EditICon /></button></Tooltip>
                                    <Tooltip title={'Add Line'}><button className={clsx(styles.sendEmailIconBtn)} onClick={() => handleOpenAddLinesPopup(order)}><AddICon /></button></Tooltip>
                                  </>}
                                </Box>
                              </Popper>

                            </td>
                          </tr>
                      )
                    })
                ) : (
                  <tr>
                    <td colSpan={11} className={"noDataFoundTd"}>No data found</td>
                  </tr>
                )}
            </tbody>
            </table>
          </div>
          <div className={"PaginationNumber"}>
            {meta && <ReactPaginate
              breakLabel="..."
              nextLabel=">"
              onPageChange={handlePageClick}
              pageRangeDisplayed={5}
              pageCount={meta.totalPages}
              previousLabel="<"
              renderOnZeroPageCount={(props) =>
                props.pageCount > 0 ? undefined : null
              }
              forcePage={meta.currentPage > 0 ? meta.currentPage - 1 : undefined}
            />}
          </div>
        </div>
        }

      {!!seditSelectedLine && <EditLinesPopup open={!!seditSelectedLine} onClose={handleCloseEditLinesPopup} orderData={seditSelectedLine} setShowLoader={setShowLoader} referenceProducts={referenceProducts} isEdit={true}/>}
      {!!addSelectedLine && <EditLinesPopup open={!!addSelectedLine} onClose={handleCloseAddLinesPopup} orderData={addSelectedLine} setShowLoader={setShowLoader} referenceProducts={referenceProducts} isEdit={false}/>}

      <MatPopup
        className={styles.orderContinuePopup}
        open={showConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>Email will be sent - </p>
          <div className={styles.sendEmailGrid}>
            <span>To: </span>
            <Tooltip title={emailData?.send_invoice_to ? emailData.send_invoice_to : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={emailData?.send_invoice_to} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <div className={styles.sendEmailGrid}>
            <span>Cc: </span>
            <Tooltip title={invoiceEmailRecipients?.cc_email ? invoiceEmailRecipients.cc_email : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={invoiceEmailRecipients?.cc_email ? invoiceEmailRecipients.cc_email : 'NA'} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <div className={styles.sendEmailGrid}>
            <span>Bcc: </span>
            <Tooltip title={invoiceEmailRecipients?.bcc_email ? invoiceEmailRecipients.bcc_email : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={invoiceEmailRecipients?.bcc_email ? invoiceEmailRecipients.bcc_email : 'NA'} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <p className={styles.continuetext}>Do you want to continue? (you can use Generate Email feature to send invoice to desired recipients)</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div
            className={styles.successfullytext}
            dangerouslySetInnerHTML={{ __html: apiResponseMessage }}
          ></div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
    </div>
  );
};

export default UpdateQtySendInvoiceEmail;
