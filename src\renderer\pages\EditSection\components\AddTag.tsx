import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import styles from "../EditSection.module.scss";
import {
  AddTagSchema,
  AddTagSchemaType,
} from "../../../models/videotags.model";
function AddTag({ confirmationPopupClose, addTag, setShowLoader }: any) {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { isValid },
  } = useForm<AddTagSchemaType>({
    defaultValues: {
      name: "",
      display_title: "",
      display_subtitle: "",
      query_param: "",
      show_on_app: false,
      show_on_safe: false,
      add_at_top : false,
      shuffle_sequence : false
    },
    resolver: yupResolver(AddTagSchema),
    mode: "onSubmit",
  });

  const clickOnSubmitBtn = () => {
    confirmationPopupClose();
    setShowLoader(true);
    submitData(watch());
    setShow<PERSON>oader(false);
  };

  const submitData = (data: {
    name: string;
    display_title: string;
    display_subtitle: string;
    query_param: string;
    show_on_app: boolean | number;
    show_on_safe: boolean | number;
    add_at_top : boolean | number;
    shuffle_sequence : boolean | number;
  }) => {
    const payload = {
      data: {
        name: data.name,
        display_title: data.display_title,
        display_subtitle: data.display_subtitle,
        query_param: data.query_param,
        show_on_app: data.show_on_app,
        show_on_safe: data.show_on_safe,
        add_at_top : Number(data.add_at_top),
        shuffle_sequence : Number(data.shuffle_sequence)
      },
    };
    addTag(payload);
  };

  return (
    <div className={styles.continuePopup}>
      <div className={styles.overFlowForPop}>
        <div className={styles.col}>
          <div className={styles.inputField}>
            <span className={styles.lblInput}>Name</span>
            <input
              type="text"
              className={styles.inputBox}
              {...register("name")}
            />
          </div>
          <div className={styles.inputField}>
            <span className={styles.lblInput}>Display Title</span>
            <input
              type="text"
              className={styles.inputBox}
              {...register("display_title")}
            />
          </div>
        </div>
        <div className={styles.col}>
          <div className={styles.inputField}>
            <span className={styles.lblInput}>Display Subtitle</span>
            <input
              type="text"
              className={styles.inputBox}
              {...register("display_subtitle")}
            />
          </div>
          <div className={styles.inputField}>
            <span className={styles.lblInput}>Query Param</span>
            <input
              type="text"
              className={styles.inputBox}
              {...register("query_param")}
            />
          </div>
        </div>
        <div className={styles.col}>
          <div className={styles.inputField}>
            <span className={styles.lblInput}>Show on app</span>
            <input type="checkbox" {...register("show_on_app")} />
          </div>
          <div className={styles.inputField}>
            <span className={styles.lblInput}>Show on safe</span>
            <input type="checkbox" {...register("show_on_safe")} />
          </div>
        </div>
        <div className={styles.col}>
          <div className={styles.inputField}>
            <span className={styles.lblInput}>Add at top</span>
            <input type="checkbox" {...register("add_at_top")} />
          </div>
          <div className={styles.inputField}>
            <span className={styles.lblInput}>Shuffle sequence</span>
            <input type="checkbox" {...register("shuffle_sequence")} />
          </div>
        </div>
      </div>
      
      <div className={styles.yesAndnoBtn}>
        <button
          className={styles.okBtn}
          disabled={!isValid}
          onClick={handleSubmit(clickOnSubmitBtn)}
        >
          Submit
        </button>
        <button
          className={styles.okBtn}
          onClick={() => {
            confirmationPopupClose();
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

export default AddTag;
