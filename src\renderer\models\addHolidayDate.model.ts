import * as yup from "yup";

export const addHolidayDatesFormSchema = yup.object().shape({
    data: yup.array().of(yup.object().shape({
        holiday_date: yup
            .string()
            .required('Holiday Date is Required'),
        description: yup.string().nullable(),
        daysToSkip: yup.number().nullable(),
        daysToSkipBefore: yup.number(),
        daysToSkipAfter: yup.number(),
        isThanksgiving: yup.boolean(),
        holidayStartTime: yup.string().required("Holiday Start Time is Required"),
        dayBeforeHolidayStartTime: yup.string().required("Holiday Start Time is Required"),
        dayAfterHolidayStartTime: yup.string().required("Holiday Start Time is Required"),
        removeDayBeforeHoliday: yup.boolean().default(false),
        removeDayAfterHoliday: yup.boolean().default(false),
        holidayStatus: yup.array().default([0,1,2])
    })),
    
});

export type addHolidayDatesFormSchemaType = yup.InferType<
    typeof addHolidayDatesFormSchema
>;
