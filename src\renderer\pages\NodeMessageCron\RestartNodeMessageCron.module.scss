.settingPage {

    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.0509803922);
    padding: 15px;


    .orderReminderBox {
        max-width: 600px;
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border: 1px solid transparent;
        border-radius: 4px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.0509803922);
        border-color: #ddd;

        p {
            color: var(--primaryColor);
            font-size: 14px;
            font-weight: 500;
        }

        .orderReminderInput {
            display: block;
            width: 100%;
            height: 34px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            margin-bottom: 10px;
        }
    }

    .submitBtn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
        background-color: var(--primaryColor);
        color: #fff;
        border: 1px solid transparent;
        padding: 6px 12px;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 5px;
        cursor: pointer;

    }

    .referenceText {
        font-size: 18px;
        font-weight: 600;
    }

    .referenceDataText {
        font-size: 16px;
        font-weight: 600;
        max-width: 400px;
        margin-bottom: 15px;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        padding: 10px;
        border: 1px solid transparent;
        border-radius: 4px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.0509803922);
        border-color: #ddd;


        .referenceLabelText {
            // margin-right: 26px;
            padding: 10px;
        }

    }
}

.approveRejectPopup {

    h2 {
        display: none;
    }

    .successfullyUpdated {
        padding: 20px;
        text-align: center;
        width: 300px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }

        .successfullytext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);

            @media screen and (max-width: 768px) and (min-width: 320px) {
                font-size: 18px;
            }

        }

        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
        }
    }



}

.orderContinuePopup {
    h2 {
        display: none;
    }

    .continuePopup {
        padding: 20px;
        text-align: center;
        width: 450px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }

        .continuetext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
        }

        .yesAndnoBtn {
            display: flex;
            gap: 10px;

            .okBtn {
                width: 100%;
                height: 45px;
                border-radius: 6px;
                text-decoration: none;
                gap: 8px;
                border: none;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--primaryColor);
                color: #fff;
            }

        }
    }

    .continuePopup1 {
        padding: 20px;
        text-align: center;
        width: 370px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 290px;
        }

        .continuetext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
        }

        .yesAndnoBtn {
            display: flex;
            gap: 10px;
            margin-top: 30px;

            .okBtn {
                width: 100%;
                height: 45px;
                border-radius: 6px;
                text-decoration: none;
                gap: 8px;
                border: none;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--primaryColor);
                color: #fff;

                &:disabled {
                    opacity: 0.7;
                    cursor: not-allowed;
                }
            }

        }
    }
}