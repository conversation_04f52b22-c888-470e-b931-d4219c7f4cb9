import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import svgrPlugin from 'vite-plugin-svgr';

// https://vitejs.dev/config/
export default defineConfig({
  ...(process.env.NODE_ENV === 'development' ? { define: { global: {} } } : {}),
  plugins: [
    react(),
    svgrPlugin(),
  ],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'build',
    sourcemap: false,
  },

})
