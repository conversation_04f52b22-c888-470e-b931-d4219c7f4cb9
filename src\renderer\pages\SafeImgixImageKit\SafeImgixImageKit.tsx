import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import useGetSafeImgixImageKit from "../../hooks/useGetSafeImgixImageKit";
import useSaveSafeImgixImageKit from "../../hooks/useSaveSafeImgixImageKit";
import { useContext, useEffect } from "react";
import { CommonCtx } from "../AppContainer";
import { USE_IMAGEKIT, USE_IMGIX } from "../../utils/constant";
import Loader from "../../components/common/Loader";

const schema = yup.object().shape({
    useImgix: yup.boolean().default(false).oneOf([true, false]),
    useImageKit: yup.boolean().default(false).oneOf([false, true]),
});
type SchemaType = yup.InferType<typeof schema>;

const defaultValues = {
    useImgix: false,
    useImageKit: false,
};

export const SafeImgixImageKit = () => {
    const showPopupFormAnyComponent = useContext(CommonCtx);

    const {
        data: safeImgixImageKitData,
        isLoading: isSafeImgixImageKitLoading,
        isFetching: isSafeImgixImageKitFetching,
    } = useGetSafeImgixImageKit();

    const {
        mutate: saveSafeImgixImageKit,
        data: saveSafeImgixImageKitData,
        isLoading: isSaveSafeImgixImageKitLoading,
    } = useSaveSafeImgixImageKit();

    const { register, handleSubmit, setValue } = useForm<SchemaType>({
        defaultValues,
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (!isSafeImgixImageKitLoading && !isSafeImgixImageKitFetching && safeImgixImageKitData) {
            const useImgixObj = safeImgixImageKitData.find((obj: any) => obj.config_key === USE_IMGIX);
            const useImageKitObj = safeImgixImageKitData.find((obj: any) => obj.config_key === USE_IMAGEKIT);
            if (useImgixObj) {
                setValue("useImgix", !!useImgixObj.config_value);
            }
            if (useImageKitObj) {
                setValue("useImageKit", !!useImageKitObj.config_value);
            }
        }
    }, [safeImgixImageKitData, isSafeImgixImageKitLoading, isSafeImgixImageKitFetching,]);

    useEffect(() => {
        if (saveSafeImgixImageKitData && !isSaveSafeImgixImageKitLoading) {
            showPopupFormAnyComponent(saveSafeImgixImageKitData);
        }
    }, [saveSafeImgixImageKitData, isSaveSafeImgixImageKitLoading]);

    const handleFormSubmit = (data: SchemaType) => {
        const payload: any = [];

        const useImgixObj = safeImgixImageKitData.find((obj: any) => obj.config_key === USE_IMGIX);
        const useImageKitObj = safeImgixImageKitData.find((obj: any) => obj.config_key === USE_IMAGEKIT);
        if (useImgixObj) {
            payload.push({ id: useImgixObj.id, value: data.useImgix });
        }
        if (useImageKitObj) {
            payload.push({ id: useImageKitObj.id, value: data.useImageKit });
        }

        saveSafeImgixImageKit({ data: payload, });
    };

    return (
        <div className="contentMain">
            {isSafeImgixImageKitLoading || isSafeImgixImageKitFetching || isSaveSafeImgixImageKitLoading ? (
                 <div className="loaderImg">
                   <Loader />
                 </div>
            ) : (
                <>
                <div className='safeImageMain'>
                    <div className='title'>Turn on/off Imgix and Imagekit</div>
                    <div className='imageKit'>
                    <label>
                        Use Imgix
                        <input type="checkbox" {...register("useImgix")} />
                    </label>
                    <label>
                        Use ImageKit
                        <input type="checkbox" {...register("useImageKit")} />
                    </label>
                    <button className="saveBtn" onClick={handleSubmit(handleFormSubmit)}>Save</button>
                </div>
                </div>
                
                </>
            )}
        </div>
    );
};
