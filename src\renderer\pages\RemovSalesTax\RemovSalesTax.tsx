import { useForm } from "react-hook-form";
import Loader from "../../components/common/Loader";
import useRemoveSalesTax from "../../hooks/useRemoveSalesTax";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import InputField from "../../components/common/InputField";
import { useEffect, useState } from "react";
import MatPopup from "../../components/common/MatPopup";
import styles from "./RemovSalesTax.module.scss";

const schema = yup.object({
  poNumber: yup.string().required(),
});

type FormData = {
  poNumber: string;
};

const RemovSalesTax = () => {
  const [apiResponseMessage, setApiResponseMessage] = useState<string>("");

  const {
    mutate: removeSalesTax,
    data: removeSalesTaxData,
    isLoading: isRemoveSalesTaxLoading,
  } = useRemoveSalesTax();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({ resolver: yupResolver(schema) });

  useEffect(() => {
    if (!isRemoveSalesTaxLoading && removeSalesTaxData) {
      setApiResponseMessage(removeSalesTaxData);
    }
  }, [removeSalesTaxData, isRemoveSalesTaxLoading]);

  const formSubmitHandler = (data: FormData) => {
    removeSalesTax({ data: { po_number: data.poNumber } });
  };

  const apiResponsePopupCloseHandler = () => {
    setApiResponseMessage("");
  };

  return (
    <div>
      {isRemoveSalesTaxLoading ? (
        <Loader />
      ) : (
        <div className={styles.salesTaxRemov}>
          <form onSubmit={handleSubmit(formSubmitHandler)}>
            <label>
              <InputField
                className={styles.poNumber}
                control={control}
                fieldName="poNumber"
                placeholder="Enter PO number to remove sales tax"
              />
              <button className={styles.loginBtn} type="submit">Submit</button>
            </label>

          </form>
        </div>
      )}
      <MatPopup open={!!apiResponseMessage}>
        <div>
          <p>{apiResponseMessage}</p>
          <button type="button" onClick={apiResponsePopupCloseHandler}>
            Ok
          </button>
        </div>
      </MatPopup>
    </div>
  );
};

export default RemovSalesTax;
