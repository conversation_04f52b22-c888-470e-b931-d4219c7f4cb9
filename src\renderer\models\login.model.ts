import * as yup from "yup";

export const loginFromSchema = yup.object().shape({
  email: yup
    .string()
    .default("")
    .required("Email is required")
    .email("Please enter valid email")
    .matches(/^$|^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "Please enter valid email"),
  password: yup
    .string()
    .default("")
    .required("Password is required")
    .min(6, "Min lenght is 6"),
});

export type LoginFromSchemaType = yup.InferType<typeof loginFromSchema>;
