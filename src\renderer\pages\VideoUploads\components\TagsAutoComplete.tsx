import { Autocomplete, Box, TextField, Tooltip } from "@mui/material";
import clsx from "clsx";
import styles from "../VideoUploads.module.scss";
import { useEffect, useState } from "react";
import { useImmer } from "use-immer";
const TagsAutoComplete = ({setVideoTags, videoTags, setValue, valueName, tagsData}: any) => {
    const [inputValue, setInputValue] = useState("");
    const [videoTagList, setVideoTagList] = useImmer<any>([]);
    const [open, setOpen] = useState(false);
    const [clickCheck, setClickCheck] = useState(false);
    const [disableClick,setDisableClick] = useState(-1);


    useEffect(() => {
        if (tagsData?.length) {
            const _videoTags:any[] = []
            tagsData.forEach((videoTagData: { isChecked: boolean; })=>{
                const _videoTag = {...videoTagData}
                _videoTag.isChecked = false;
                _videoTags.push(_videoTag)
            })
            setVideoTagList(_videoTags);
        }
    }, [tagsData])

    useEffect(() => {
        if (disableClick >= 0) {
            const videoTagListData = [...videoTagList];
            videoTagListData[disableClick].isChecked = !videoTagListData[disableClick].isChecked;
            handleVideoTagSelection(videoTagListData[disableClick]);
            setInputValue('');
            setVideoTagList(videoTagListData);
        }
    }, [disableClick])

    useEffect(()=>{
        setClickCheck(false);
        setDisableClick(-1);
    },[videoTagList])

    const handleVideoTagSelection = (data: any) => {
        const videoTagMapData = {...videoTags};
        data.isChecked = !data.isChecked;
        if(data.isChecked){
            videoTagMapData[data.id] = data;
        }else{
            videoTagMapData[data.id] = undefined;
        }
        const tagList = Object.values(videoTagMapData).filter(Boolean).map((videoTagValue: any) => videoTagValue.query_param);
        setValue(valueName, tagList, { shouldValidate: true });
        setVideoTags(videoTagMapData);
    };

    const checkBoxChangeHandler = (option: any) => {
        if(clickCheck){
            return;
        }
        option.isChecked = !option.isChecked;
        const i = videoTagList.findIndex((obj: any) => obj.query_param === option.query_param);
        setClickCheck(true);
        setDisableClick(i);
    }

    const handleClick = () => {
        setOpen(true);
    }

    const handleClickAway = () => {
        setOpen(false);
    }

    return (
            <Box sx={{ position: "relative", width:"100%", marginLeft:"auto", flex:"1" }}>
                {!open ? (
                    <button className={clsx(styles.cassMapptingDropdownBtn, styles.minWidth2)} type="button" onClick={handleClick}>
                        <span className={Object.keys(videoTags)?.length ? styles.dataSelected : styles.placeholder}>
                            {Object.values(videoTags).filter(Boolean)?.length ?
                                Object.keys(videoTags).reduce((acc: any, val: any) => {
                                    return videoTags[val]?.display_title ?
                                        (acc ? acc + " | " + videoTags[val]?.display_title : videoTags[val]?.display_title)
                                        : acc;
                                }, "")
                                : "Choose"}
                        </span>
                        <span className={styles.arrowIcon}>
                            <svg focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDropDownIcon">
                                <path d="M7 10l5 5 5-5z"></path></svg>
                            <span></span>
                        </span>
                    </button>
                ) : (
                    open && (
                        <>
                            <Autocomplete
                                open={open}
                                className={clsx(styles.selectDropdown, 'cassMappingDropdown')}
                                options={videoTagList}
                                getOptionLabel={(option: any) => option.display_title}
                                onBlur={handleClickAway}
                                renderOption={(props, option: any, {index}) => (
                                    <div key={index}>
                                    <span {...props} key={option.id} onClick={() => {checkBoxChangeHandler(option) }}>
                                        <span>
                                            <input type="checkbox" checked={!!option.isChecked} onChange={(e) => {  }} />
                                        </span>
                                        <span>
                                            <div className="compnyName">{option.display_title}</div>
                                        </span>
                                    </span>
                                    </div>
                                )}
                                inputValue={inputValue ? inputValue : ''}
                                renderInput={(params) => (
                                    <Tooltip title={videoTags.length ? `${videoTags.join(', ')}` : ''}
                                        placement='bottom-start'
                                        classes={{
                                            popper: styles.cassAutocompleteTooltipStyle,
                                            tooltip: styles.tooltip,
                                        }}
                                    >
                                        <TextField {...params} autoFocus={true} placeholder="Choose One" />
                                    </Tooltip>
                                )}
                                onChange={(e, data: any) => {
                                    handleVideoTagSelection(data)
                                    setInputValue('');
                                }}
                                onInputChange={(e, data) => {
                                    if (data !== 'undefined') {
                                        setInputValue((prev: any) => {
                                            prev = data;
                                            return prev;
                                        });
                                    }
                                }}
                                classes={{
                                    root: styles.autoCompleteDesc,
                                    popper: styles.autocompleteDescPanel,
                                    paper: styles.autocompleteDescInnerPanel,
                                    listbox: clsx(styles.listAutoComletePanel, 'cassMappinglist'),
                                }}
                                isOptionEqualToValue={(option, value) => {
                                    return !value ? false : `${option.display_title}` === value;
                                }}
                            />
                        </>
                    )
                )}
            </Box>
    );
}
export default TagsAutoComplete;