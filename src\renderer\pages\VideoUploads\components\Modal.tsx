import React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import { useState, useEffect, useRef } from "react";
import styles from "../VideoUploads.module.scss";
import { useContext } from "react";
import { CommonCtx } from "../../AppContainer";
import Preview from "./Preview";
import SafePreview from "./SafePreview";
import { cloneDeep } from "lodash-es";
import { partialForm } from "../../../utils/constant";
import { invalidSequenceNumber } from "../../../utils/constant";
import useGetAllVideoLibraryTags from "../../../hooks/useGetAllVideoLibraryTags";
import { useGetVideoByTag } from "@bryzos/giss-ui-library";
import { previewRadioButtonText } from "../../../utils/constant";

export default function Modal({
  open,
  handleClose,
  extraTag,
  extraThumbnail,
  extraVideo,
  extraTitle,
  videoId,
  extraSequence,
  extraShow,
  extraAssociatedTags,
  extraSelectedTags,
  extraViews,
  extraDescription,
  extraSubtitle,
  havingBlob,
  share_video_url
}: any) {
  const [selectedOption, setSelectedOption] = useState(previewRadioButtonText.desktop);
  const { data: tags, isLoading: tagsLoading } = useGetAllVideoLibraryTags();
  const {
    data: videoData,
    isLoading,
    mutateAsync: fetchVideos,
  } = useGetVideoByTag();
  const [queryData, setQueryData] = useState({tag: [], videoData: {}});
  const [previewOpen, setPreviewOpen] = useState(false);
  const [newVideoData, setnewVideoData] = useState({});
  const showPopupFormAnyComponent = useContext(CommonCtx);
  const [associatedTags, setAssociatedTags] = useState([]);

  useEffect(() => {
    if (tags && videoData && !tagsLoading && !isLoading) {
      setQueryData({
        tag: tags,
        videoData: videoData?.data?.data,
      });
    }
  }, [videoData]);  

  useEffect(() => {
    if (tags && tags.length > 0 && !tagsLoading) {
      let queryParams: any[] = [];
      tags.map((item: any) => {
        queryParams.push(item.query_param);
      });

      fetchVideos(queryParams);
    }
  }, [tags, tagsLoading]);

  useEffect(() => {
    if (extraAssociatedTags) {
      setAssociatedTags(extraAssociatedTags);
    }
  }, [extraAssociatedTags]);

  const commonStateUpdatePart = (originalVideoData: any) => {
    setnewVideoData(originalVideoData);
    setPreviewOpen(true);
  };

  const checkedForPartialUpload = () => {
    return !(
      extraTag?.length &&
      Object.keys(extraThumbnail)?.length &&
      extraTitle
    );
  };

  const checkedForPartialEdit = () => {
    return !(
      extraTag?.length &&
      Object.keys(extraThumbnail)?.length &&
      extraTitle &&
      extraSequence
    );
  };

  const checkEditState = (originalVideoData: any) => {
    let state = 0;

    originalVideoData[extraTag]?.forEach((video: any) => {
      if (
        videoId === video.video_id &&
        parseFloat(extraSequence) === parseFloat(video.sequence)
      ) {
        state = 1;
      }

      if (
        videoId !== video.video_id &&
        parseFloat(extraSequence) === parseFloat(video.sequence)
      ) {
        state = -1;
      }
    });

    return state;
  };

  const isVideoChenged = (originalVideoData: any) => {
    let isChanged = false;

    originalVideoData[extraTag]?.forEach((video: any) => {
      if (videoId === video.video_id) {
        if (extraVideo !== video.video_s3_url) {
          isChanged = true;
        }
      }
    });

    return isChanged;
  };

  const positonNotChanged = (originalVideoData: any) => {
    const updatedVideoListofCurrentTag: any = [];

    const isChanged = isVideoChenged(originalVideoData);

    const viewsCount = extraViews;

    const userVideoData = {
      thumbnail_s3_url_map: extraThumbnail,
      video_s3_url: extraVideo,
      title: extraTitle,
      show_on_ui: extraShow,
      view_count: viewsCount,
      description:extraDescription,
      subtitle_s3_url: extraSubtitle,
      isNew : havingBlob,
      share_video_url:share_video_url
    };

    originalVideoData[extraTag]?.forEach((video: any) => {
      if (videoId === video.video_id) {
        updatedVideoListofCurrentTag.push(userVideoData);
      } else {
        updatedVideoListofCurrentTag.push(video);
      }
    });

    originalVideoData[extraTag] = updatedVideoListofCurrentTag;

    extraSelectedTags?.forEach((currSelectedTag: string) => {
      if ((associatedTags?.data) && !associatedTags?.data?.includes(currSelectedTag)) {
        if (!originalVideoData[currSelectedTag]) {
          originalVideoData[currSelectedTag] = [];
        }

        originalVideoData[currSelectedTag]?.push({
          ...userVideoData,
          show_on_ui: true,
        });
      } else if (!(associatedTags?.data)){
        if(extraVideo) {
             if(extraTag !== currSelectedTag) {
                if (!originalVideoData[currSelectedTag]) {
                  originalVideoData[currSelectedTag] = [];
                }
       
                originalVideoData[currSelectedTag]?.push({
                 ...userVideoData,
                 show_on_ui: true,
                });                
             }            
        }
     }
    });
  };

  const filterBasedonShowUi = (originalVideoData: any) => {
    for (const tags in originalVideoData) {
      originalVideoData[tags] = originalVideoData[tags].filter(
        (video: any) => video.show_on_ui
      );
    }
  };

  const positionChanged = (originalVideoData: any) => {
    const updatedVideoListofCurrentTag = [];
    let prev = -1;

    const isChanged = isVideoChenged(originalVideoData);

    const viewsCount =  extraViews;

    const userVideoData = {
      thumbnail_s3_url_map: extraThumbnail,
      video_s3_url: extraVideo,
      title: extraTitle,
      show_on_ui: extraShow,
      view_count: viewsCount,
      description:extraDescription,
      subtitle_s3_url: extraSubtitle,
      isNew : havingBlob,
      share_video_url:share_video_url
    };

    let videoplaced = false

    originalVideoData[extraTag]?.forEach((video: any, i: Number) => {
      if (video.video_id === videoId) {
        return;
      }
      if (
        parseFloat(video.sequence) > parseFloat(extraSequence) &&
        prev < parseFloat(extraSequence) 
      ) {
        videoplaced = true
        updatedVideoListofCurrentTag.push(userVideoData);
      }

      updatedVideoListofCurrentTag.push(video);

      if (
        prev < parseFloat(extraSequence) &&
        parseFloat(video.sequence) < parseFloat(extraSequence) &&
        i == originalVideoData[extraTag].length - 1
      ) {
        videoplaced = true
        updatedVideoListofCurrentTag.push(userVideoData);
      }

      prev = parseFloat(video.sequence);
    });

    if (!updatedVideoListofCurrentTag.length  || !videoplaced) {
      updatedVideoListofCurrentTag.push(userVideoData);
    }

    originalVideoData[extraTag] = updatedVideoListofCurrentTag;

    extraSelectedTags?.forEach((currSelectedTag: string) => {
      if ((associatedTags?.data) && !associatedTags?.data?.includes(currSelectedTag)) {
        if (!originalVideoData[currSelectedTag]) {
          originalVideoData[currSelectedTag] = [];
        }

        originalVideoData[currSelectedTag]?.push({
          ...userVideoData,
          show_on_ui: true,
        });
      } else if (!(associatedTags?.data)){
        if(extraVideo) {
             if(extraTag !== currSelectedTag) {
                if (!originalVideoData[currSelectedTag]) {
                  originalVideoData[currSelectedTag] = [];
                }
       
                originalVideoData[currSelectedTag]?.push({
                 ...userVideoData,
                 show_on_ui: true,
                });                
             }            
        }
     }
    });
  };

  useEffect(() => {
    if (open && !isLoading && !tagsLoading && queryData && videoData) {
      const videoData = queryData?.videoData;

      let originalVideoData = cloneDeep(videoData);

      if (!videoId) {
        // Upload part
        if (checkedForPartialUpload()) {
          showPopupFormAnyComponent(partialForm, "Ok");
        } else {
          const userVideoData = {
            thumbnail_s3_url_map: extraThumbnail,
            video_s3_url: extraVideo,
            title: extraTitle,
            view_count: 0,
            show_on_ui: 1,
            description:extraDescription,
            subtitle_s3_url: extraSubtitle,
            isNew : true,
            share_video_url:share_video_url
          };

          const arrangeElemnentaccordingtoAddonTop = (originalVideoData : any , currTag : any , userVideoData : any) => {
               const tag = tags.filter( (curr : any) => {
                   return curr.query_param === currTag
               })

               if(tag?.length && tag[0]?.add_at_top) {
                  originalVideoData[currTag]?.unshift(userVideoData);
               } else {
                  originalVideoData[currTag]?.push(userVideoData);
               }
          }

          extraTag.forEach((currTag: any) => {
            originalVideoData
              ? originalVideoData[currTag]?.length
                ? (arrangeElemnentaccordingtoAddonTop(originalVideoData , currTag , userVideoData))
                : (originalVideoData[currTag] = [userVideoData])
              : ((originalVideoData = {}),
                (originalVideoData[currTag] = [userVideoData]));
          });
        }

        commonStateUpdatePart(originalVideoData);
      } else {
        // Edit part

        if (checkedForPartialEdit()) {
          showPopupFormAnyComponent(partialForm, "Ok");
        } else {
          const state = checkEditState(originalVideoData);

          if (state === 1) {
            positonNotChanged(originalVideoData);
          } else if (state === -1) {
            showPopupFormAnyComponent(invalidSequenceNumber, "Ok");
          } else {
            positionChanged(originalVideoData);
          }
        }

        filterBasedonShowUi(originalVideoData);

        commonStateUpdatePart(originalVideoData);
      }
    }
  }, [open, isLoading, videoData, tagsLoading]);

  const passingData = { tag: queryData?.tag, videoData: newVideoData };

  const handleOptionChange = (event: any) => {
    setSelectedOption(event.target.value);
  };

  return previewOpen ? (
    <React.Fragment>
      <Dialog maxWidth={false} open={open} onClose={handleClose}  >
        <div className={styles.radioContainer}>
          <label>
            <input
              type="radio"
              value={previewRadioButtonText.desktop}
              checked={selectedOption === previewRadioButtonText.desktop}
              onChange={handleOptionChange}
            />
            Desktop
          </label>
          <br />
          <label>
            <input
              type="radio"
              value={previewRadioButtonText.mobile}
              checked={selectedOption === previewRadioButtonText.mobile}
              onChange={handleOptionChange}
            />
            Mobile
          </label>
          <br />
          <br />
          <label>
            <input
              type="radio"
              value={previewRadioButtonText.safe}
              checked={selectedOption === previewRadioButtonText.safe}
              onChange={handleOptionChange}
            />
            Safe
          </label>
          <br />
        </div>
        <DialogContent
          className={styles.dialogContent}
          style={{ padding: "30px 10px", paddingBottom: "0px" }}
        >
          <div className={styles.modalContent}>
            <div className={styles.previewContainer}>
              {selectedOption === "safe" && !isLoading ? (
                <SafePreview queryData={passingData} />
              ) : (
                <Preview device={selectedOption} queryData={passingData} />
              )}
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  ) : (
    <></>
  );
}
