import React, { useEffect, useState } from 'react';
import styles from './AttachmentBubble.module.scss';
import clsx from 'clsx';
import { ReactComponent as ExcelIcon } from '../../../../assests/images/excel-table.svg';
import { ReactComponent as ImgIcon } from '../../../../assests/images/upload-photo.svg';
import { ReactComponent as FileIcon } from '../../../../assests/images/icon-file.svg';
import { Tooltip, Fade } from '@mui/material';

interface AttachmentBubbleProps {
  name: string;
  extension: string;
  url: string;
  isMyMessage: boolean;
  isImgix: boolean;
}

const AttachmentBubble: React.FC<AttachmentBubbleProps> = ({
  name,
  extension,
  url,
  isMyMessage,
  isImgix,
}) => {
  const [showPreview, setShowPreview] = useState(false);
  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(extension.toLowerCase());
  const [imgixUrl, setImgixUrl] = useState('');

  useEffect(() => {
    if (isImgix && url) {
      const path = url.split('.com')[1];
      setImgixUrl(import.meta.env.VITE_CHAT_IMGIX_URL + path + import.meta.env.VITE_IMGIX_SUFFIX);
    }
  }, [isImgix, url]);

  // Function to determine file type icon based on extension
  const getFileTypeIcon = (extension: string) => {
    const ext = extension.toLowerCase();

    // Common document types
    if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.documentIcon}>
            <span className={styles.fileTypeText}><FileIcon/></span>
          </div>
        </div>
      );
    }

    // Image types
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.imageIcon}>
            <ImgIcon/>
          </div>
        </div>
      );
    }

    // Spreadsheet types
    if (['xls', 'xlsx', 'csv'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.spreadsheetIcon}>
            <ExcelIcon/>
          </div>
        </div>
      );
    }

    // Presentation types
    if (['ppt', 'pptx'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.presentationIcon}>
            <span className={styles.fileTypeText}>PPT</span>
          </div>
        </div>
      );
    }

    // Archive types
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
      return (
        <div className={styles.fileIconContainer}>
          <div className={styles.archiveIcon}>
            <span className={styles.fileTypeText}>ZIP</span>
          </div>
        </div>
      );
    }

    // Default icon for unknown types
    return (
      <div className={styles.fileIconContainer}>
        <div className={styles.unknownIcon}>
          <span className={styles.fileTypeText}>FILE</span>
        </div>
      </div>
    );
  };

  // Image preview content for tooltip
  const ImagePreviewContent = () => (
    <div className={styles.imagePreviewContainer}>
      <img 
        src={isImgix?imgixUrl:url} 
        alt={name} 
        className={styles.imagePreview} 
      />
      <div className={styles.imagePreviewInfo}>
        <div className={styles.imagePreviewName}>{name}</div>
        <div className={styles.imagePreviewType}>{extension.toUpperCase()} Image</div>
      </div>
    </div>
  );

  return (
    <div
      className={clsx(
        styles.attachmentBubble,
        !isMyMessage && styles.othersMessage
      )}
    >
      <div className={styles.attachmentCard}>
        {isImage ? (
         
            <div className={styles.attachmentContent}>
              {getFileTypeIcon(extension)}
              <div className={styles.attachmentInfo}>
                <div className={styles.attachmentName}>{name}</div>
                <div className={styles.attachmentMeta}>
                  <span className={styles.attachmentExtension}>{extension}</span>
                </div>
              </div>
            </div>
        ) : (
          <div className={styles.attachmentContent}>
            {getFileTypeIcon(extension)}
            <div className={styles.attachmentInfo}>
              <div className={styles.attachmentName}>{name}</div>
              <div className={styles.attachmentMeta}>
                <span className={styles.attachmentExtension}>{extension}</span>
              </div>
            </div>
          </div>
        )}

        <div className={styles.attachmentActions}>
           <Tooltip
            title={<ImagePreviewContent />}
            placement="top-start"
            TransitionComponent={Fade}
            TransitionProps={{ timeout: 200 }}
            classes={{
              tooltip: styles.imageTooltip,
            }}
            onOpen={() => setShowPreview(true)}
            onClose={() => setShowPreview(false)}
          >
            <span className={styles.previewButton}>Preview</span>
          </Tooltip>
          <a
            href={url}
            title='Download'
            target="_blank"
            rel="noopener noreferrer"
            className={styles.downloadButton}
          >
            <div className={styles.downloadIcon}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 15L7 10H10V3H14V10H17L12 15Z" fill="currentColor"/>
                <path d="M20 18H4V20H20V18Z" fill="currentColor"/>
              </svg>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
};

export default AttachmentBubble;
