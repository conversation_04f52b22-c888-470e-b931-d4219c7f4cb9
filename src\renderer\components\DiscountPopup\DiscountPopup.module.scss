h2 {
    display: none;
}

.discountPopup {
    padding: 24px;
    text-align: center;
    width: 600px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        width: 100%;
    }

    .continuetext {
        text-align: center;
        font-size: 24px;
        margin-bottom: 24px;
        color: var(--primaryColor);
        font-weight: bold;
        text-align: left;
        margin-top: 0px;
    }

    .note {
        text-align: center;
        font-size: 16px;
        margin-bottom: 15px;
        color: #ff5d47;
        font-weight: bold;
        text-align: left;
        margin-top: 0px;
        height: 19px;
    }

    .yesAndnoBtn {
        display: flex;
        gap: 10px;
        margin-top: 30px;

        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            gap: 8px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;

            &:disabled {
                opacity: 0.7;
                cursor: not-allowed;
            }
        }

    }
}

.sectionHeader {
    text-align: left;
    border-top: 1px solid #eee;
    padding-top: 12px;
    margin-bottom: 8px;
}

.bottomAction {
    margin-top: 20px;
    input[type=checkbox]{
        margin-top: 1px;
    }
}

// discount popup 
.discountDiv {
    display: flex;
    padding-bottom: 12px;

    .checkBoxDiv {
        padding-left: 6px;
    }

    span {
        width: 50%;
        text-align: left;
        display: flex;
        align-items: center;
        line-height: 1;
    }

    .discountRightDiv {
        width: 50%;
        text-align: left;
        display: flex;
        align-items: center;
        flex-direction: column;

        &.discDiscountPeriod{
            flex-direction: unset;
        }

        &.discountSpreadinputMain {
            align-items: flex-start;
            gap: 8px;

            .markedUpText{
                font-size: 11px;
                font-weight: bold;
            }

            .discountSpreadinput {
                display: flex;
                justify-content: flex-start;
                width: 100%;
                position: relative;

                input {
                    width: calc(100% - 30px);
                }
            }

            .markedUpMain {
                display: flex;
                align-items: center;
                flex-direction: column;
                position: absolute;
                top: -8px;
                right: 0px;
                z-index: 9;
                margin-left: 5px;

                label {
                    display: flex;
                }
            }

            .enableBtn {
                cursor: pointer;
            }

            .disableBtn {
                cursor: default;
            }

            .markedUp {
                border: 1px solid #3F51B5;
                border-radius: 4px;
                width: 26px;
                height: 26px;
            }

            .notMarkedUp {
                border: 1px solid transparent;
                width: 26px;
                height: 26px;
                opacity: 0.6;
            }

        }

        fieldset {
            border: none;
        }


        .InputFieldcss {
            width: 100%;
            font-weight: normal;
            line-height: normal;
            color: #000;
            border-radius: 4px;
            font-size: 16px;
            outline: none;


            div {
                padding: 4px 12px;
                border: 1px solid rgba(0, 0, 0, 0.23);
                border-radius: 4px;
                box-sizing: inherit;
                height: 36px;
                color: #000;
                display: flex;
                align-items: center;
            }
        }

        input {
            width: 100%;
            height: 36px;
            padding: 4px 12px;
            font-size: 16px;
            border: 1px solid rgba(0, 0, 0, 0.23);
            border-radius: 4px;
            box-sizing: inherit;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 90px;
            height: 22px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .2s;
            transition: .2s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            -webkit-transition: .2s;
            transition: .2s;
        }

        input:checked+.slider {
            background-color: var(--primaryColor);
        }

        input:focus+.slider {
            box-shadow: 0 0 1px var(--primaryColor);
        }

        input:checked+.slider:before {
            -webkit-transform: translateX(22px);
            -ms-transform: translateX(22px);
            transform: translateX(22px);
        }

        /* Rounded sliders */
        .slider.round {
            border-radius: 34px;
        }

        .slider.round:before {
            border-radius: 50%;
        }
    }
}

.exclamationMark {
    border: 1px solid red;
    background: none;
    font-size: 10px;
    border-radius: 50%;
    cursor: pointer;
    height: 18px;
    width: 18px;
    justify-content: center;
    color: red;
    font-weight: 800;
    margin-left: 6px;

    span {
        line-height: 1.2;
    }
}

.approveRejectPopup {

    h2 {
        display: none;
    }

    .successfullyUpdated {
        padding: 20px;
        text-align: center;
        width: 300px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }

        .successfullytext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);

            @media screen and (max-width: 768px) and (min-width: 320px) {
                font-size: 18px;
            }

        }

        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
        }
    }



}

.errorStyle.errorStyle {
    .tooltip.tooltip {
        background-color: #ff5d47;
        font-size: 11px;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        text-transform: capitalize;
        margin-bottom: 5px;
    }
}

.orderContinuePopup {
    h2 {
        display: none;
    }
    .continuePopup {
        padding: 20px 30px;
        text-align: left;
        width: 500px;
        font-family: Noto Sans;

        .continuetext {
            text-align: left;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);
        }
        .popupDiv {
            margin-bottom: 20px;
        }
        .popupTitle{
            font-size: 14px;
            color:#333;
        }
        ul{
            padding-left: 20px;
            margin: 6px 0px 12px 0px;
            li{
                font-size: 14px;
                color: var(--primaryColor);
                font-weight: 500;
                margin-bottom: 4px;
            }
        }


            .popupDesc {
                font-size: 14px;
                color: var(--primaryColor);
                display: flex;
                margin-bottom: 12px;
                align-items: center;
                column-gap: 6px;
            }
      
        
        .processTxt{
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .yesAndnoBtn {
            display: flex;
            gap: 10px;
            .okBtn {
                width: 100%;
                height: 45px;
                border-radius: 6px;
                text-decoration: none;
                gap: 8px;
                border: none;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                background-color: var(--primaryColor);
                color: #fff;
            }
        }
    }
}