.tblscroll.tblscroll {
  overflow-x: auto;
  white-space: nowrap;
  // margin-bottom: 35px;
  max-height: 700px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }

  table {
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 35px;
    border-collapse: collapse;
    border-spacing: 0;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px #a8b2bb;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
    }

    thead {
      tr {
        th {
          line-height: 1.2;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
          text-align: left;
          padding: 6px 12px;
          color: #fff;
          height: 35px;
          position: sticky;
          z-index: 99;
          top: 0;
          background: #676f7c;
          color: #fff;

          &:nth-child(2) {
            width: 50%;
          }
        }

        td {
          line-height: 2.5;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
        }
      }
    }

    tbody {
      background-color: #fff;

      tr {
        margin: 0;


        &:nth-child(even) {
          background-color: #f2f2f2;
        }

        td {
          color: var(--primaryColor);
          font-size: 16px;
          margin: 0;
          padding: 6px 12px;
          height: 42px;
        }
      }
    }
  }
}

.oaderCenter {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  margin-top: 100px;
}

.addCompanyBtn {
  width: 140px;
  height: 37px;
  border-radius: 4px;
  text-decoration: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  background-color: var(--primaryColor);
  color: #fff;
  margin-right: 16px;

  @media (max-width:767px) {

    margin-top: 16px;
    margin-left: 0px;
  }
}

.searchBox {
  margin-bottom: 15px;
  display: flex;

  @media (max-width:767px) {
    flex-direction: column;
  }

  .showdropdwn {
    width: 82px;
    height: 38px;
    padding: 4px;
  }

  .searchInput {
    box-shadow: none;
    outline: none;
    height: 38px;
    padding: 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    width: 196px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 100%;
    }
  }
}

.PaginationNumber {
  ul {
    list-style: none;
    display: flex;
    padding: 0px;


    li {
      position: relative;
      display: block;
      padding: 10px;
      margin-left: -1px;
      line-height: 1.25;
      color: var(--primaryColor);
      background-color: #fff;
      border: 1px solid #dee2e6;
      cursor: pointer;


      &:active {
        color: #fff;
        background-color: var(--primaryColor);
        border-color: var(--primaryColor);
      }
    }
  }

}

.loaderImg {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  margin-top: 200px;
}

.approveRejectPopup {
  h2 {
    display: none;
  }

  .successfullyUpdated {
    padding: 20px;
    text-align: center;
    width: 300px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 240px;
    }

    .successfullytext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);

      @media screen and (max-width: 768px) and (min-width: 320px) {
        font-size: 18px;
      }
    }

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
    }
  }

}

.orderContinuePopup {
  h2 {
    display: none;
  }

  .continuePopup {
    padding: 20px;
    text-align: center;
    width: 400px;

    .companyNameInput {
      box-shadow: none;
      outline: none;
      width: 100%;
      height: 42px;
      padding: 6px 12px;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.5;
      color: #495057;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
    }

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 310px;
    }

    .addCompanyTitle {
      font-size: 20px;
      color: #000;
      line-height: normal;
      margin-top: 0px;
      margin-top: 12px;
      font-weight: 600;
    }

    .continuetext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);
    }

    .yesAndnoBtn {
      display: flex;
      gap: 16px;
      margin-top: 16px;

      .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

    }
  }

}

.continuePopup1 {
  padding: 20px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 100%;
    padding: 15px;
  }

  .continuetext {
    text-align: center;
    font-size: 20px;
    margin-bottom: 24px;
    color: var(--primaryColor);
    margin-top: 0px;
  }

  .yesAndnoBtn {
    display: flex;
    gap: 10px;
    margin-top: 30px;

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      gap: 8px;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }

  }
}

.passCode {
  display: flex;
  gap: 15px;
  padding-left: 11px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    padding: 0px;
  }

  input {
    min-width: 41px;
    height: 34px;
    font-weight: 400;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 20px;
    border-radius: 7px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 100%;
      min-width: 12%;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    &::-webkit-inner-spin-button {
      display: none;
    }
  }
}

.resetPassBtn {
  width: 100px;
  height: 30px;
  border-radius: 4px;
  text-decoration: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  background-color: var(--primaryColor);
  color: #fff;
  padding: 0px 12px;
}

.inputFiledLoginPass {
  display: flex;
  width: 100%;
  align-items: center;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    display: block;
  }

  input {
    width: 300px;

  }

  .InputFieldcss {
    width: 320px;
    padding: 10px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      // display: block;
      width: 100%;
      margin-left: 0px;
    }
  }

  .emailText {
    width: 200px;
    font-size: 16px;
    font-weight: 600;
  }

  .loginBtn {
    width: 64px;
    height: 37px;
  }
}

.emailNotvalidPopup {
  h2 {
    display: none;
  }

  .emailNotvalidbox {
    padding: 20px;
    text-align: center;
    width: 300px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 240px;
    }

    .emailText {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);

      @media screen and (max-width: 768px) and (min-width: 320px) {
        font-size: 16px;
      }
    }

    .CompanyNametext {
      text-align: center;
      font-size: 16px;
      color: var(--primaryColor);
    }

    .yesAndnoBtn {
      // display: flex;
      // gap: 5px;

      .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
      }

      .editCompanyText {
        height: 48px;
        padding: 6px 12px;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        width: 100%;
      }

      .btnYesno {
        display: flex;
        margin-top: 10px;
        margin-bottom: 10px;
        gap: 5px;
      }
    }
  }
}

.yesAndnoBtnHeader {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-left: 24px;

  .okBtn {
    width: 140px;
    height: 37px;
    border-radius: 4px;
    text-decoration: none;
    gap: 8px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    background-color: var(--primaryColor);
    color: #fff;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

}

.allCompanyText {
  display: inline-flex;
  margin-right: auto;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    margin-right: 0px;
  }

  .selectDropdown.selectDropdown {
    width: 280px;
    height: 38px;
    padding: 0px;
    margin-right: 20px;
    margin-top: 0px;
    background-color: #fff;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      margin-top: 15px;
      margin-left: 0px;
      width: 100%;
      margin-right: 0px;
      max-width: 100%;
    }

    .MuiOutlinedInput-root {
      padding: 2px;
    }

    input {
      padding: 0px;
      margin-bottom: 3px;

    }

  }
}

.autocompleteDescPanel {
  border-radius: 4px;
  width: 100%;
  max-width: 350px;
  height: 48px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  padding-right: 4px;
  border-radius: 0px 0px 4px 4px;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 316px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }


  span {
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    box-shadow: none;
    padding: 4px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;

    &:hover {
      border-radius: 2px;
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      // background-color: #EBF2FF;
      // color: #397aff;
      background-color: #fff;
      color: #000;
    }
  }
}