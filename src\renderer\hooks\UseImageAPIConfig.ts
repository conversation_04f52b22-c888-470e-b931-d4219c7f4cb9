import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

// Define the fetch function
const fetchImageApiConfig = async () => {
  const { data } = await axios.get(`${import.meta.env.VITE_API_SERVICE}/reference-data/getHomepageSafeConfig`);
  return data;
};

// Create the custom hook
const UseImageAPIConfig = () => {
  return useQuery(['imageApiConfig'], fetchImageApiConfig);
};

export default UseImageAPIConfig;
