import * as React from 'react';
import DialogTitle from '@mui/material/DialogTitle';
import Dialog, { DialogProps } from '@mui/material/Dialog';

export type MatPropsType = { 
  dialogTitle?: string; 
}& DialogProps;

const MatPopup: React.FC<MatPropsType> = ({ 
  dialogTitle,
  children,
   ...rest
}) => {
  return (
    <Dialog {...rest}>
      <DialogTitle>{dialogTitle}</DialogTitle>
      {children}
    </Dialog>
  );
};

export default MatPopup;
