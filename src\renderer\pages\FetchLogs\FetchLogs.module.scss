.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 35px;
    max-height: 700px;
  
    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }
  
    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }
  
    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }
  
    table {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 10px;
        border-collapse: collapse;
        border-spacing: 0;
  
        thead {
            tr {
  
                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 6px 12px;
                    color: #fff;
                    height: 35px;
                    position: sticky;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
  
                }
  
                td {
                    line-height: 2.5;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
  
                }
            }
        }
  
        tbody {
            background-color: #fff;
            tr {
                margin: 0;
  
  
                &:nth-child(even) {
                    background-color: #f2f2f2;
                }
  
                td {
                    color: var(--primaryColor);
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;
                    &.delRow{
                      color: #ff0000;
                      font-size: 16px;
                      cursor: pointer;
                    }
                    
  
                }
            }
        }
    }
}

.userListContainer{
    margin-top: 10px;
}
.inputSection{
    margin-bottom: 10px;
}

.fetchLogsBtn {
    margin-top: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
    button {
      height: 38px;
      color: #fff;
      background-color: var(--primaryColor);
      border-radius: 4px;
      cursor: pointer;
      padding: 6px 18px;
      border: 0px;
      font-size: 16px;
      line-height: normal;
  
      @media (max-width:767px) {
        font-size: 14px;
      }
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .fetchLogsContinuePopup {
    z-index: 100000;
    h2 {
      display: none;
    }
  }


  .fetchContinuePopup {
    padding: 20px;
    text-align: center;
    width: 300px;
  
    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 240px;
    }
  
    .fetchContinuetext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);
    }
  
    .yesAndnoBtn {
      display: flex;
      gap: 10px;
  
      .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        gap: 8px;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;
      }
  
    }
  }
  

  .fetchUserLogsContainer{
    h3{
        font-family: Noto Sans;
        font-size: 24px;
        font-weight: 600;
        margin-top: 0px;
        margin-bottom: 8px;
    }
  }