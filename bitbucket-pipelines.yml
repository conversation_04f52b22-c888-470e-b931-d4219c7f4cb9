image: node:18

pipelines:
  custom:
    RollBack-2-Demo:
      - variables:
        - name: VERSION
      - step:
          name: RollBack Demo
          deployment: Demo
          script:
            - apt-get update && apt-get install -y awscli jq
            - S3_BUCKET_BACKUP="s3://deploy-bckups/demo/extended-widget-demo-admin"
            - S3_BUCKET="s3://extended-widget-demo-admin"
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_DEMO" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_DEMO" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
    Deployment-2-Demo:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"Demo\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload, Invalidate, Tag and cloudflare-clear
          deployment: Demo
          script:
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is available
            - npm install
            - npm run build:demo
            # Set necessary variables for Current Build
            - S3_BUCKET="s3://extended-widget-demo-admin"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Set necessary variables for Backup
            - S3_BUCKET="s3://extended-widget-demo-admin"
            - S3_BACKUP_BUCKET="s3://deploy-bckups/demo/extended-widget-demo-admin"            
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_DEMO" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_DEMO" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
             # Create tag
            - TAG_EXISTS=$(git tag -l "demo-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="demo-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "demo-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/demo-.*-\([0-9]*\)/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="demo-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully" 
            - |
              # Send the API request to Cloudflare to purge the entire cache
              curl -X DELETE "https://api.cloudflare.com/client/v4/zones/$DEMO_ZONE_ID/purge_cache" \
              -H "Authorization: Bearer $DEMO_CLOUD_FLARE_TOKEN" \
              -H "Content-Type: application/json" \
              --data '{"purge_everything":true}'
            - echo "Cloudflare DONE"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi  
    RollBack-3-Production:
      - variables:
        - name: VERSION
      - step:
          name: RollBack PROD
          deployment: Production
          script:
            - apt-get update && apt-get install -y awscli jq
            - S3_BUCKET_BACKUP="s3://deploy-bckups/prod/extended-widget-admin-prod"
            - S3_BUCKET="s3://extended-widgetadmin-prod"
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_PROD" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_PROD" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
    Deployment-2-Production:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"Production\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload, Invalidate, Tag and cloudflare-clear
          deployment: Production
          caches:
            - node
          script:
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is available
            - npm install
            - npm run build:production
            # Set necessary variables for Current Build
            - S3_BUCKET="s3://extended-widgetadmin-prod"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Set necessary variables for Backup
            - S3_BUCKET="s3://extended-widgetadmin-prod"
            - S3_BACKUP_BUCKET="s3://deploy-bckups/prod/extended-widget-admin-prod"            
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_PROD" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_PROD" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
             # Create tag
            - TAG_EXISTS=$(git tag -l "prod-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="prod-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "prod-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/prod-.*-\([0-9]*\)/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="prod-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully"
            - |
              # Send the API request to Cloudflare to purge the entire cache
              curl -X DELETE "https://api.cloudflare.com/client/v4/zones/$PROD_ZONE_ID/purge_cache" \
              -H "Authorization: Bearer $PROD_CLOUD_FLARE_TOKEN" \
              -H "Content-Type: application/json" \
              --data '{"purge_everything":true}'
            - echo "Cloudflare DONE"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi  
    RollBack-1-QA:
      - variables:
        - name: VERSION
      - step:
          name: RollBack QA
          deployment: qa
          script:
            - apt-get update && apt-get install -y awscli jq
            - S3_BUCKET_BACKUP="s3://deploy-bckups/qa/extended-widget-qa-admin"
            - S3_BUCKET="s3://extended-widget-qa-admin"
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_QA" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_QA" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
    Deployment-1-QA:
      - step:
          name: Build, Upload, Invalidate, Tag and cloudflare-clear
          deployment: qa
          caches:
            - node
          script:
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is available
             # start deplyoment notification
            - MY_ENV="$ENVIRONMENT"
            - echo $MY_ENV
            - echo "export MY_ENV=$MY_ENV" >> set_env.sh
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
             curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${MY_ENV}\",\"status\":\"${START}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
            # start installation and build
            - npm install
            - npm run build:qa
            # Set necessary variables for Current Build
            - S3_BUCKET="s3://extended-widget-qa-admin"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Set necessary variables for Backup
            - S3_BUCKET="s3://extended-widget-qa-admin"
            - S3_BACKUP_BUCKET="s3://deploy-bckups/qa/extended-widget-qa-admin"            
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_QA" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_QA" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
             # Create tag
            - TAG_EXISTS=$(git tag -l "qa-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="qa-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "qa-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/qa-.*-\([0-9]*\)/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="qa-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully" 
            - |
              # Send the API request to Cloudflare to purge the entire cache
              curl -X DELETE "https://api.cloudflare.com/client/v4/zones/$QA_ZONE_ID/purge_cache" \
              -H "Authorization: Bearer $QA_CLOUD_FLARE_TOKEN" \
              -H "Content-Type: application/json" \
              --data '{"purge_everything":true}'
            - echo "Cloudflare DONE"  
          after-script:
            - cat set_env.sh 
            - source set_env.sh
            - echo $MY_ENV
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${MY_ENV}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${MY_ENV}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi