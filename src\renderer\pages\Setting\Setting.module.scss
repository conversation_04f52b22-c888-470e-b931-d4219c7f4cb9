.settingPage {

    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.0509803922);
    padding: 15px;


    .orderReminderBox {
        max-width: 600px;
        margin-bottom: 20px;
        background-color: #fff;
        padding: 15px;
        border: 1px solid transparent;
        border-radius: 4px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.0509803922);
        border-color: #ddd;

        p {
            color: var(--primaryColor);
            font-size: 14px;
            font-weight: 500;
        }

        .orderReminderInput {
            display: block;
            width: 100%;
            height: 34px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            margin-bottom: 10px;
        }
    }

    .submitBtn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
        background-color: var(--primaryColor);
        color: #fff;
        border: 1px solid transparent;
        padding: 6px 12px;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 5px;

        &hover {
            color: #212529;
            text-decoration: none;

        }

    }
}
.loaderImg {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 200px;
}
