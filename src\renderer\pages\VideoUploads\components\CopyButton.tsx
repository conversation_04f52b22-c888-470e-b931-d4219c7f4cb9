import React, { useState } from 'react';
import styles from "../VideoUploads.module.scss"
const CopyButton = ({ url }) => {
  const [copied, setcopied] = useState(false);

  const copyToClipboard = (text:string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setcopied(true);
        setTimeout(() => setcopied(false), 1300);
      })
  };

  return (
    <button
      onClick={() => copyToClipboard(url)}
      className={styles.copyBtn}
    >
    {!copied ? "Copy" : "Copied"}
    </button>
  );
};

export default CopyButton;