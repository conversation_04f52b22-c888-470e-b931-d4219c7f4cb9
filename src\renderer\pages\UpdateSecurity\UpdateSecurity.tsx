import { useEffect } from "react";
import Loader from "../../components/common/Loader";
import styles from "./UpdateSecurity.module.scss";
import MatPopup from "../../components/common/MatPopup";
import { useImmer } from "use-immer";
import InputField from "../../components/common/InputField";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import useUpdateSecurityKey from "../../hooks/useUpdateSecurityKey";

const schema = yup.object({
    security_key: yup.string().trim().required("Required"),
});

type FormData = {
    security_key: string;
};


const UpdateSecurity = () => {

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<FormData>({
        defaultValues: {
            security_key: ''
        }, 
        resolver: yupResolver(schema) });

    const [apiResponseMessage, setApiResponseMessage] = useImmer("");
    const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
    const [confirmationPopupData, setConfirmationPopupData] = useImmer<any>(null);

    const {
        mutate: updateSecurityKey,
        data: updateSecurityKeyData,
        isLoading: isUpdateSecurityKeyLoading,
    } = useUpdateSecurityKey();


    useEffect(() => {
        if (updateSecurityKeyData) {
            setApiResponseMessage(updateSecurityKeyData);
            reset();
        }
    }, [updateSecurityKeyData]);

    const confirmationPopupYes = () => {
        if (confirmationPopupData) {
            updateSecurityKey(confirmationPopupData);
        }

        confirmationPopupClose();
    };

    const confirmationPopupClose = () => {
        setShowConfirmationPopup(false);
        setConfirmationPopupData(null);
    };


    const formSubmitHandler = (data: FormData) => {
        setShowConfirmationPopup(true);
        setConfirmationPopupData(data);
    };

    const handlePopupOkBtn = () => {
        setApiResponseMessage("");
    }

    return (
        <div>
            {isUpdateSecurityKeyLoading ? (
                <Loader />
            ) : (
                <div className={styles.settingPage}>
                    <p className={styles.referenceText}>Update Security Token</p>
                    <div className={styles.referenceDataText}>
                        <form onSubmit={handleSubmit(formSubmitHandler)}>
                            <InputField
                                className={styles.referenceLabelText}
                                control={control}
                                fieldName="security_key"
                                placeholder="Enter Security Token"
                            />
                            <button className={styles.submitBtn} type="submit">Update</button>
                        </form>
                    </div>
                </div>
            )}

            <MatPopup
                className={styles.approveRejectPopup}
                open={!!apiResponseMessage}
            >
                <div className={styles.successfullyUpdated}>
                    <div className={styles.successfullytext}>{apiResponseMessage}</div>
                    <button
                        className={styles.okBtn}
                        onClick={() => handlePopupOkBtn()}
                    >
                        Ok
                    </button>
                </div>
            </MatPopup>
            <MatPopup
                className={styles.orderContinuePopup}
                open={showConfirmationPopup}
            >
                <div className={styles.continuePopup}>
                    <p className={styles.continuetext}>Do you want to continue ?</p>
                    <div className={styles.yesAndnoBtn}>
                        <button className={styles.okBtn} onClick={confirmationPopupYes}>
                            Yes
                        </button>
                        <button className={styles.okBtn} onClick={confirmationPopupClose}>
                            No
                        </button>
                    </div>
                </div>
            </MatPopup>
        </div>
    );
};

export default UpdateSecurity;
