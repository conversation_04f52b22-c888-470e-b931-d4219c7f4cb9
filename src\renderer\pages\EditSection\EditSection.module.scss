.tblscroll.tblscroll {
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 20px;
  max-height: 670px;
  margin-top: 20px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }

  table {
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 35px;
    border-collapse: collapse;
    border-spacing: 0;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px #a8b2bb;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
    }

    thead {
      tr {
        border: 1px solid gray;

        th {
          line-height: 1.2;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
          text-align: left;
          padding: 6px 12px;
          color: #fff;
          height: 35px;
          position: sticky;
          top: 0;
          background: #676f7c;
          color: #fff;
          z-index: 9;
        }

        td {
          line-height: 2.5;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
          &:nth-child(even) {
            background-color: #f2f2f2;
          }
        }
      }
    }

    tbody {
      background-color: #fff;

      tr {
        margin: 0;
        &:nth-child(even) {
          background-color: #f2f2f2;
        }

        td {
          color: #343a40;
          font-size: 16px;
          margin: 0;
          padding: 6px 12px;
          height: 42px;
          white-space: pre-wrap;

          &:first-child {
            width: 100px;
          }

          &:last-child{
            min-width: 150px
          }

          img {
            object-fit: contain;
            max-width: 130px;
          }

          .messageText {
            box-shadow: none;
            outline: none;
            height: 38px;
            width: 180px;
            padding: 6px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out,
              box-shadow 0.15s ease-in-out;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .videoThumbBox {
            width: 240px;
            height: 150px;
            .custom-video-player {
              height: 100%;
            }
          }
        }
      }
    }
  }
}

.searchBox {
  margin-right: 20px;
  display: flex;
  justify-content: flex-end;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    display: flex;
    flex-direction: column;
  }

  .showdropdwn {
    width: 82px;
    height: 38px;
    padding: 4px;
  }

  .searchInput {
    box-shadow: none;
    outline: none;
    height: 38px;
    padding: 6px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }
}

.loaderImg {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  margin-top: 400px;
}

.saveButton {
  width: 70px;
  height: 38px;
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    margin-left: 0px;
    margin-top: 10px;
  }
}

.saveBtnf {
  width: 75px;
  height: 38px;
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;
  margin-right: 12px;
  border: 0px;
  @media screen and (max-width: 768px) and (min-width: 320px) {
    margin-top: 10px;
  }
}

.addBtnf {
  width: 75px;
  height: 38px;
  color: #fff;
  background-color: var(--primaryColor);
  border-color: #122b40;
  border-radius: 5px;
  cursor: pointer;
  margin-right: 12px;
  border: 0px;
  @media screen and (max-width: 768px) and (min-width: 320px) {
    margin-top: 10px;
  }
}

.noteText {
  font-size: 18px;
  line-height: normal;
  margin-bottom: 20px;
  font-weight: 600;
  color: var(--primaryColor);
  text-align: center;
  color: red;
}

.showCommentsBtn {
  width: 120px;
  height: 30px;
  border-radius: 4px;
  text-decoration: none;
  border: none;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  background-color: var(--primaryColor);
  color: #fff;
  padding: 0px 12px;
}

.showCommentPopup {
  overflow: auto;
  h2 {
    display: none;
  }

  .continuePopup {
    padding: 20px;
    text-align: center;

    @media screen and (max-width: 767px) and (min-width: 320px) {
      width: 240px;
    }

    .continuetext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);
    }

    .yesAndnoBtn {
      display: flex;
      gap: 10px;
      padding-top: 20px;

      .okBtn {
        width: 100%;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        gap: 8px;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}

.commentListPopup {
  width: 550px;
  .tblscrollPop {
    .continuetext {
      margin-top: 0px;
      margin-bottom: 12px;
      font-weight: 600;
      text-align: left;
      font-size: 20px;
    }
    .tblscroll {
      margin-bottom: 0px;
      max-height: 450px;
      table {
        margin-bottom: 12px;
        tr {
          th {
            font-size: 14px;
            padding: 6px;
            &:first-child {
              white-space: pre-line;
            }
          }
          td {
            font-size: 14px;
            padding: 6px;
            text-align: left;
            input {
              box-shadow: none;
              outline: none;
              height: 38px;
              padding: 6px;
              font-size: 14px;
              font-weight: 400;
              line-height: 1.5;
              color: #495057;
              background-color: #fff;
              background-clip: padding-box;
              border: 1px solid #ced4da;
              border-radius: 0.25rem;
              margin: 0px;
            }
          }
        }
      }
    }
  }
}

.showCommentPopupLoader {
  .commentListPopup {
    height: 300px;
  }
}

.errorStyle.errorStyle {
  .tooltip.tooltip {
    background-color: #ff5d47;
    color: #fff;
    font-family: Noto Sans;
    font-size: 10px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    margin-bottom: 5px;
  }
}

.reactionDiv {
  display: flex;
  flex-direction: row;
  align-items: center;
  column-gap: 10px;

  .reactionSpan {
    display: inline-flex;
    align-items: center;
  }
  .reactionIcon {
    display: flex;
    margin-right: 2px;
  }
  .reactionText {
    vertical-align: middle;
  }
}

.showVideoPopup.showVideoPopup {
  max-width: 600px;
  width: 100%;
  padding: 45px 24px 24px 24px;
 .closeBtn{
  position: absolute;
  right: 12px;
  top:12px;
  z-index: 9;
  button{
      background-color: transparent;
      border: 0px;
      padding: 0px;
      cursor: pointer;
  }
 }
}

.uploadVideoPopup.uploadVideoPopup {
  max-width: 850px;
  width: 100%;
  padding: 24px;

  .uploadVideoTitle{
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 24px;
  }

  .cassMapptingDropdownBtn{
      font-family: Noto Sans;
      width: 100%;
      max-width: 300px;
      height: 40px;
      font-weight: 400;
      line-height: 1.4375em;
      letter-spacing: 0.00938em;
      text-align: left;
      padding:9px ;
      border-radius: 4px;
      color: rgba(0, 0, 0, 0.87);
      border: solid 1px rgba(98, 107, 132,0.42);
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #fff;
      margin-left: auto;
      .placeholder{
          opacity: 0.42;
      }
      .dataSelected{
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 75%;
      }
      .arrowIcon{
          padding: 0px;
          border: 0px;
          display: flex;
          background-color: transparent;
          svg{
              width: 22px;
              height: 22px;
              path{
                  fill{
                      color: currentColor;
                  }
              }
          }
      }
  }

  .col{
      display: flex;
      column-gap: 12px;
      margin-bottom: 12px;
  }

  .inputField {
      display: flex;
      flex: 1;

      .lblInput {
          flex: 1;
          max-width: 100px;
          display: flex;
          align-items: center;
      }
      .checkbox{
          margin: 11px;
          margin-left: 0;
          width: 23px;
      }

      .lbl {
          flex: 1;
          display: flex;
          align-items: center;
          font-weight: bold;
      }

      .inputBox {
          flex: 1;
          max-width: 300px;
          height: 38px;
          padding: 6px;
          font-size: 14px;
          font-weight: 400;
          line-height: 1.5;
          color: #495057;
          background-color: #fff;
          background-clip: padding-box;
          border: 1px solid #ced4da;
          border-radius: 0.25rem;
      }
  }

  .uploadVideoSection {
      display: flex;
      column-gap: 12px;
      border-top: 1px solid #eee;
      border-bottom: 1px solid #eee;
      padding: 12px 0px;

      span {
          font-size: 20px;
          font-weight: 600;
      }

      .uploadVideo {
          flex-grow: 1;
          max-width: 50%;
      }

      .uploadHeading {
          font-size: 14px;
          font-weight: bold;
          line-height: 1.6;
          text-align: left;
          color: #0f0f14;
          text-transform: uppercase;
      }

      .uploadLabel {
          font-size: 14px;
          font-weight: bold;
          line-height: 1.6;
          color: #393e47;
          text-transform: uppercase;
          margin-top: 12px;

          @media (max-width: 767px) {
              font-size: 12px;
          }

          input {
              outline: none;
              padding-left: 8px;
              height: 30px;
              width: 100%;
              border-radius: 4px;
              border: solid 1px #9b9eac;
              background-color: #fff;
              height: 40px;
              font-size: 14px;
              font-weight: bold;
              line-height: 1.4;
              text-align: left;
              color: #0f0f14;

              &:focus {
                  font-weight: normal;
              }
          }
      }

      .uploadBox {
          height: 320px;
          margin-top: 8px;
          border-radius: 4px;
          border: dashed 1px #1c40e7;
          background-color: #e9f3ff;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          .uploadText1 {
              font-size: 12px;
              line-height: 1.6;
              text-align: center;
              color: #71737f;
              margin-top: 0px;
          }

          input {
              display: none;
          }
      }

      .continuePopBtn {
          .continueBtn {
              padding: 8px 30px;
              border-radius: 6px;
              background-color: #16b9ff;
              font-size: 14px;
              font-weight: bold;
              line-height: 1.6;
              text-align: center;
              color: #fff;
              text-transform: uppercase;
              width: 100%;
              cursor: pointer;
              border: 0px;

              &:disabled {
                  opacity: 0.5;
                  cursor: not-allowed;
              }

              @media (max-width: 767px) {
                  padding: 8px 20px;
                  font-size: 10px;
              }
          }
      }

      .captureThumbnail{
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 6px;
          span{
              font-size: 16px;
              line-height: 1.6;
              text-align: center;
              color: #71737f;
              margin-top: 12px;
              display: block;
          }
          button{
              font-family: Inter;
              padding: 6px 12px;
              font-size: 16px;
              cursor: pointer;
              color: var(--primaryColor);
              border-radius: 6px;
          }
      }

   
  }

  .yesAndnoBtn {
      display: flex;
      gap: 10px;
      margin-top: 24px;
      .okBtn {
          width: 100%;
          height: 40px;
          border-radius: 6px;
          text-decoration: none;
          gap: 8px;
          border: none;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          background-color: var(--primaryColor);
          color: #fff;
          &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
          }
      }
  }

  &.addTagPopup{
    .inputField{
      .lblInput{
        max-width: 140px;
      }
      input[type=checkbox]{
        margin-left: 0px;
      }
    }
  }


}

.highlightInput {
  border: 2px solid red !important; 
  box-shadow: 0 0 5px rgba(255, 165, 0, 0.5);
}