import React, { useEffect, useState } from "react";
import styles from "../BroadcastNotifications.module.scss";
import { sendToOptions } from "../../../utils/constant";
import {
  Control,
  FieldArrayWithId,
  UseFieldArrayPrepend,
  UseFieldArrayRemove,
  UseFormRegister,
  UseFormWatch,
} from "react-hook-form";
import { SchemaType } from "../BroadcastNotifications";
import { Autocomplete, FilterOptionsState, TextField } from "@mui/material";
import useGetAllUser from "../../../hooks/useGetAllUser";
import Loader from "../../../components/common/Loader";
import clsx from "clsx";

type Props = {
  register: UseFormRegister<SchemaType>;
  watch: UseFormWatch<SchemaType>;
  control: Control<SchemaType>;
  fields: FieldArrayWithId<SchemaType, "sendToUsers", "id">[];
  prepend: UseFieldArrayPrepend<SchemaType, "sendToUsers">;
  remove: UseFieldArrayRemove;
};
const SendTo: React.FC<Props> = ({ register, watch, fields, prepend, remove }) => {
  const [inputValue, setInputValue] = useState("");

  const sentTo = watch("sendTo");

  const {
    data: allUsers,
    isLoading: allUsersLoading,
    isFetching: allUsersFetching,
  } = useGetAllUser();

  useEffect(() => {
    if (sentTo !== sendToOptions.users) {
      fields?.forEach((filed, i) => {
        remove(i);
      });
    }
  }, [sentTo]);

  const getFilterOptions = (options: any[], state: FilterOptionsState<any>) => {
    return options.filter(option => {
      const keyword = state.inputValue?.toLocaleLowerCase();
      const firstName = option.first_name?.toLocaleLowerCase();
      const lastName = option.last_name?.toLocaleLowerCase();
      const email = option.email_id?.toLocaleLowerCase();

      return firstName?.includes(keyword) || lastName?.includes(keyword) || email?.includes(keyword);
    });
  };

  return (
    <>
      <div>
        <div className={clsx(styles.inputSection, styles.sendToOptions)}>
          <div>
            <p className={styles.lblTitle}>Send To</p>
          </div>
          <div className={styles.rightGrid}>
            <input
              className={styles.radioInput}
              {...register("sendTo")}
              type="radio"
              value={sendToOptions.all}
            />
            <label className={styles.lblRadio}>All</label>

            <input
              className={styles.radioInput}
              {...register("sendTo")}
              type="radio"
              value={sendToOptions.buyers}
            />
            <label className={styles.lblRadio}>Buyers</label>

            <input
              className={styles.radioInput}
              {...register("sendTo")}
              type="radio"
              value={sendToOptions.sellers}
            />
            <label className={styles.lblRadio}>Sellers</label>

            
            <input
              className={styles.radioInput}
              {...register("sendTo")}
              type="radio"
              value={sendToOptions.users}
            />
            <label className={styles.lblRadio}>Users</label>
          </div>
        </div>
        {watch("sendTo") === sendToOptions.users && (
          <>
            {allUsersLoading || allUsersFetching ? (
              <Loader />
            ) : (
              <div>
                <div className={styles.inputSection}>
                  <Autocomplete
                    value={null}
                    options={allUsers}
                    inputValue={inputValue}
                    getOptionLabel={(option: any) => `${option.first_name} ${option.last_name} ${option.email_id}`}
                    renderInput={(params) => (
                      <TextField {...params} label="Select Users" />
                    )}
                    classes={{
                      root: styles.autoCompleteSendNotifi,

                    }}
                    onInputChange={(e, value) => setInputValue(value)}
                    onChange={(e, data: any) => {
                      if (data) {
                        setInputValue("");
                        prepend({ userId: data.id, firstName: data.first_name, lastName: data.last_name, email: data.email_id });
                      }
                    }}
                    renderOption={(props, option) => <span {...props} key={option.id}>{option.first_name} {option.last_name}<br />{option.email_id}</span>}
                      filterOptions={getFilterOptions}
                    getOptionDisabled={(option) => fields.some(filed => filed.userId === option.id)}
                  />
                </div>
                {fields?.length > 0 && <div className={styles.tblscroll}>
                  <table>
                    <thead>
                      <tr>
                        <th></th>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Email</th>
                      </tr>
                    </thead>
                    <tbody>
                      {fields.map((field, i) => (
                        <tr key={field.id}>
                          <td className={styles.delRow} onClick={() => remove(i)}>x</td>
                          <td>{field.firstName}</td>
                          <td>{field.lastName}</td>
                          <td>{field.email}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>}

              </div>
            )}
          </>
        )}
      </div>
    </>
  );
};
export default SendTo;
