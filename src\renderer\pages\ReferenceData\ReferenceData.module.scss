.tblscroll.tblscroll {
    overflow-x: auto;
    white-space: nowrap;
    margin-bottom: 35px;
    max-height: 700px;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

    table {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 35px;
        border-collapse: collapse;
        border-spacing: 0;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }


        thead {
            tr {
                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 6px 12px;
                    color: #fff;
                    height: 35px;
                    position: sticky;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
                }

                td {
                    line-height: 2.5;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                }
            }
        }

        tbody {
            background-color: #fff;

            tr {
                margin: 0;
                height: 45px;
                cursor: pointer;

                &:hover {
                    border: 1px solid #000;
                }

                &:nth-child(even) {
                    background-color: #f2f2f2;
                }

                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;

                    .approvalBtn {
                        display: inline-block;
                        text-align: center;
                        padding: 6px 11px;
                        border: 1px solid transparent;
                        border-radius: 0.25rem;
                        background-color: #343a40;
                        font-size: 15px;
                        color: #fff;
                        cursor: pointer;
                    }

                    .rejectBtn {
                        display: inline-block;
                        text-align: center;
                        padding: 6px 23px;
                        border: 1px solid transparent;
                        border-radius: 0.25rem;
                        background-color: #343a40;
                        font-size: 15px;
                        color: #fff;
                        cursor: pointer;
                    }

                }
            }
        }
    }
}

.editRefPop {
    padding: 8px;

}

.editRefDate {
    display: flex;
    padding: 25px;
    height: 395px;
    overflow-y: scroll;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

    .titleFiled {
        padding-right: 25px;

        div {
            font-size: 15px;
            display: flex;
            align-items: center;
            height: 50px;

            @media screen and (max-width: 768px) and (min-width: 320px) {
                font-size: 11px;
            }

            span {
                margin-left: auto;
            }
        }
    }

    .inputFiled {
        div {
            font-size: 20px;
            display: flex;
            align-items: center;
            height: 50px;

            input {
                height: 25px;
                padding-left: 15px;
                font-size: 15px;

                @media screen and (max-width: 768px) and (min-width: 320px) {
                    width: 100%;
                }
            }
        }

    }

}

.btnFiled {
    display: flex;
    gap: 25px;
    padding: 20px;

    button {
        border: 1px solid var(--primaryColor);
        background-color: var(--primaryColor);
        border-radius: 4px;
        padding: 10px 20px 10px 20px;
        font-size: 15px;
        color: #fff;
        cursor: pointer;

        &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

    }
}

.prodUploadSearchSection {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .currentVesionText {
        font-size: 16px;
        color: #000000;
        margin-left: auto;
        font-weight: 500;

        @media (max-width:767px) {
            font-size: 14px;
        }
    }
}

.searchInput {
    box-shadow: none;
    outline: none;
    padding: 6px 10px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    &.errorInputVersion{
       border: 1px solid #ff0000;
    }
}

.DownloadAnduploadbox {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    @media (max-width:767px) {
        flex-direction: column;
    }

    .uploadFiletxt {
        font-size: 18px;
        align-items: center;
        color: #343a40;

        @media (max-width:1024px) {
            display: none;
        }

        .uploadFileInput {
            background-color: transparent;
            border: 1px dashed #000000;
            padding: 6px 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            border-radius: 4px;
            color: #fff;
            width: 200px;
            height: 38px;
            margin-left: 10px;
            color: #343a40;
        }

    }

    .exportExcelbtn {
        background-color: var(--primaryColor);
        border: 0px;
        padding: 10px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        border-radius: 4px;
        color: #fff;
        height: 38px;
        cursor: pointer;
        
        &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
    }
}

.loaderImg {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 200px;

    .loaderText {
        font-size: 24px;
    }

}

.popuptbl.popuptbl {
    overflow-x: auto;
    white-space: nowrap;
    max-height: 700px;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

    table {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 35px;
        border-collapse: collapse;
        border-spacing: 0;

        thead {
            tr {
                th {
                    line-height: 1.2;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                    padding: 6px 12px;
                    color: #fff;
                    height: 35px;
                    position: sticky;
                    top: 0;
                    background: #676f7c;
                    color: #fff;
                }

                td {
                    line-height: 2.5;
                    font-weight: 600;
                    font-size: 16px;
                    margin: 0;
                }
            }
        }

        tbody {
            tr {
                margin: 0;
                height: 45px;

                &:nth-child(even) {
                    background-color: #f2f2f2;
                }

                td {
                    color: #343a40;
                    font-size: 16px;
                    margin: 0;
                    padding: 6px 12px;
                    height: 42px;
                }
            }
        }
    }
}

.refDataPop {
    padding: 15px;
    width: 100%;
}

.errorStyle{
    .tooltip{
        background-color: #ff0000;
        font-size: 12px;
        .arrowTooltip{
            color: #ff0000;
        }
    }
}

.errorResponseMessagePopup {
    .errorResponseMessage {
        padding: 20px;
        text-align: center;
        width: 450px;

        h2 {
            font-size: 20px;
            color: #000;
            margin-top: 0;
            text-align: left;
        }

        .errorResponseMessageText {
            font-size: 16px;
            line-height: normal;
            color: #000;
            text-align: left;
            margin-bottom: 20px;
        }

        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
        }
    }
}

.approveRejectPopup {
    h2 {
        display: none;
    }

    .successfullyUpdated {
        padding: 20px;
        text-align: center;
        width: 300px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }

        .successfullytext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);

            @media screen and (max-width: 768px) and (min-width: 320px) {
                font-size: 18px;
            }
        }

        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
        }
    }
}

.uploadReferenceErrorPopup {
    .MuiPaper-root {
        width: 600px;
    }

    h2 {
        display: none;
    }

    .successfullyUpdated {
        padding: 20px;
        text-align: center;
        width: 100%;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            width: 240px;
        }

        .successfullytext {
            text-align: center;
            font-size: 20px;
            margin-bottom: 24px;
            color: var(--primaryColor);

            @media screen and (max-width: 768px) and (min-width: 320px) {
                font-size: 18px;
            }
        }

        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
        }
    }
}

.loaderClass {
    width: 200px;
    height: 100px;
    text-align: center;
}

.uiDescription {
    resize: none;
    overflow-y: scroll;
    width: 100%;
    height: 50px;
    padding: 5px;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px #a8b2bb;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a8b2bb;
        border-radius: 4px;
    }

}

.errorData {
    padding-left: 20px;

    li {
        text-align: left;
        font-size: 18px;
        padding-bottom: 2px;

        @media screen and (max-width: 768px) and (min-width: 320px) {
            font-size: 16px;
        }
    }
}

.headingPop {
    font-weight: 500;
    padding-bottom: 20px;
    margin: 0;
}

.uploadRefernceData {
    border: 1px solid #495057;
    border-radius: 4px;
    padding: 16px 20px;
    margin-bottom: 30px;

    .title {
        font-size: 24px;
        color: #000000;
        margin-bottom: 20px;
        font-weight: 600;
        text-align: center;
    }

    .uploadRefernceDataHeader {
        .dragBtnGrid {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;

            .dragBtn {
                padding: 4px 8px;
                display: inline-flex;
                align-items: center;
                background-color: var(--primaryColor);
                border: dashed 1px #fff;
                justify-content: center;
                color: #fff;
                min-width: 100px;
                min-height: 28px;
                opacity: 0.5;
                cursor: unset;

                &[draggable="true"] {
                    cursor: move;
                    opacity: unset;
                }
            }
        }

    }

    .dragColumn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;

        .dragColumnlbl {
            font-size: 13px;
            padding: 4px 6px;
            display: inline-flex;
            align-items: center;
            background-color: #2d3f55;
            border: dashed 1px #fff;
            justify-content: flex-start;
            color: #fff;
            min-width: 180px;
            min-height: 28px;
        }

        button {
            font-size: 13px;
            padding: 4px 6px;
            display: inline-flex;
            align-items: center;
            background-color: #284d75;
            border: dashed 1px #fff;
            justify-content: flex-start;
            color: #fff;
            min-width: 180px;
            min-height: 28px;
            opacity: 0.5;
            cursor: unset;

            &[draggable="true"] {
                cursor: move;
                opacity: unset;
            }
        }
    }

    .yesAndnoBtn {
        display: flex;
        gap: 10px;
        padding-top: 20px;
        justify-content: center;

        .okBtn {
            width: 180px;
            height: 45px;
            border-radius: 4px;
            text-decoration: none;
            gap: 8px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }

    }
}

.orderContinuePopup {
    h2 {
        display: none;
    }
}

.continuePopup {
    padding: 20px;
    text-align: center;
    width: 400px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
        width: 240px;
    }

    .continuetext {
        text-align: center;
        font-size: 20px;
        margin-bottom: 24px;
        color: var(--primaryColor);
    }

    .yesAndnoBtn {
        display: flex;
        gap: 10px;

        .okBtn {
            width: 100%;
            height: 45px;
            border-radius: 6px;
            text-decoration: none;
            gap: 8px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--primaryColor);
            color: #fff;
        }

    }
}
.productDataV{
    margin-bottom: 10px;
}
.highlightedCellList.highlightedCellList{
    padding: 0px;
    
  }

  .markHighlighted{
    background-color: #fffbc0;
    padding: 0px 12px 0px 5px;
    overflow: hidden;
    overflow: hidden;
    text-overflow: ellipsis;
  }