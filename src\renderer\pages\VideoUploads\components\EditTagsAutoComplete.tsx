import { Autocomplete, Box, TextField, Tooltip } from "@mui/material";
import clsx from "clsx";
import styles from "../VideoUploads.module.scss";
import { useEffect, useState } from "react";
import { useImmer } from "use-immer";
const EditTagsAutoComplete = ({setValue, disabledTags, setHasFieldValuesChanged, disableTagSelect, defaultInternalTags, valueName, tagsData}: any) => {
    const [inputValue, setInputValue] = useState("");
    const [videoTagList, setVideoTagList] = useImmer<any[]>([]);
    const [open, setOpen] = useState(false);
    const [clickCheck, setClickCheck] = useState(false);
    const [disableClick,setDisableClick] = useState(-1);
    const [videoTags , setVideoTags] =  useState({});

    useEffect(() => {
        if (tagsData?.length) {
            const arrQeryParam:string[] = [];
            const videoTagMapData:any = {};
            const _videoTags: any[] = []
            tagsData.forEach((videoTagData: { query_param:string, isChecked: boolean, disabled:boolean, id:number })=>{
                    const _videoTag = {...videoTagData};
                    const internalTagList = defaultInternalTags ? defaultInternalTags.trim().split(',') : [];
                    if(disabledTags && disabledTags.indexOf(videoTagData.query_param) != -1){
                        _videoTag.isChecked = true;
                        _videoTag.disabled = true;
                        videoTagMapData[videoTagData.id] = _videoTag;
                        arrQeryParam.push(videoTagData.query_param);
                    }else if (internalTagList?.length && internalTagList.indexOf(videoTagData.query_param) != -1) {
                        _videoTag.isChecked = true;
                        videoTagMapData[videoTagData.id] = _videoTag;
                        arrQeryParam.push(videoTagData.query_param);
                    } else{
                        _videoTag.disabled = false;
                        _videoTag.isChecked = false;
                    }   
                _videoTags.push(_videoTag);
            })
            setVideoTagList(_videoTags);
            setVideoTags(videoTagMapData);
            setValue(valueName, arrQeryParam, { shouldValidate: true });
        }
    }, [tagsData])

    useEffect(() => {
        if (disableClick >= 0) {
            const videoTagListData = [...videoTagList];
            videoTagListData[disableClick].isChecked = !videoTagListData[disableClick].isChecked;
            handleVideoTagSelection(videoTagListData[disableClick]);
            setInputValue('');
            setVideoTagList(videoTagListData);
        }
    }, [disableClick])

    useEffect(()=>{
        setClickCheck(false);
        setDisableClick(-1);
    },[videoTagList])

    const isEnabled = (id:string)=>{
        return true;
    }

    const handleVideoTagSelection = (data: any) => {
        const videoTagMapData = {...videoTags};
        data.isChecked = !data.isChecked;
        if(data.isChecked){
            videoTagMapData[data.id] = data;
        }else{
            videoTagMapData[data.id] = undefined;
        }
        const tagList = Object.values(videoTagMapData).filter(Boolean).map((videoTagValue: any) => videoTagValue.query_param);
        setValue(valueName, tagList, { shouldValidate: true, shouldDirty:true });
        setVideoTags(videoTagMapData);
    };

    const checkBoxChangeHandler = (option: any) => {
        if(option.disabled){
            return;
        }
        if(clickCheck){
            return;
        }
        option.isChecked = !option.isChecked;
        const i = videoTagList.findIndex((obj: any) => obj.query_param === option.query_param);
        setClickCheck(true);
        setDisableClick(i);
        setHasFieldValuesChanged(true);
    }

    const handleClick = () => {
        setOpen(true);
    }

    const handleClickAway = () => {
        setOpen(false);
    }

    return (
            <Box sx={{ position: "relative", width:"100%", marginLeft:"auto", flex:"1" }}>
                {!open ? (
                    <button className={clsx(styles.cassMapptingDropdownBtn, styles.minWidth2)} type="button" onClick={handleClick} disabled={disableTagSelect}>
                        <span className={Object.keys(videoTags)?.length ? styles.dataSelected : styles.placeholder}>
                            {Object.values(videoTags).filter(Boolean)?.length ?
                                Object.keys(videoTags).reduce((acc: any, val: any) => {
                                    return videoTags[val]?.display_title ?
                                        (acc ? acc + " | " + videoTags[val]?.display_title : videoTags[val]?.display_title)
                                        : acc;
                                }, "")
                                : "Choose"}
                        </span>
                        <span className={styles.arrowIcon}>
                            <svg focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDropDownIcon">
                                <path d="M7 10l5 5 5-5z"></path></svg>
                            <span></span>
                        </span>
                    </button>
                ) : (
                    open && (
                        <>
                            <Autocomplete
                                open={open}
                                className={clsx(styles.selectDropdown, 'cassMappingDropdown')}
                                options={videoTagList}
                                getOptionLabel={(option: any) => option.display_title}
                                onBlur={handleClickAway}
                                renderOption={(props, option: any, {index}) => (
                                    <div key={index}>
                                    <span {...props} key={option.id} onClick={() => {checkBoxChangeHandler(option) }}>
                                        <span>
                                            <input type="checkbox" checked={!!option.isChecked} disabled={option.disabled} onChange={(e) => {  }} />
                                        </span>
                                        <span>
                                            <div className="compnyName">{option.display_title}</div>
                                        </span>
                                    </span>
                                    </div>
                                )}
                                inputValue={inputValue ? inputValue : ''}
                                renderInput={(params) => (
                                    <Tooltip title={videoTags.length ? `${videoTags.join(', ')}` : ''}
                                        placement='bottom-start'
                                        classes={{
                                            popper: styles.cassAutocompleteTooltipStyle,
                                            tooltip: styles.tooltip,
                                        }}
                                    >
                                        <TextField {...params} autoFocus={true} placeholder="Choose One" />
                                    </Tooltip>
                                )}
                                onChange={(e, data: any) => {
                                    handleVideoTagSelection(data)
                                    setInputValue('');
                                    setHasFieldValuesChanged(true);
                                }}
                                onInputChange={(e, data) => {
                                    if (data !== 'undefined') {
                                        setInputValue((prev: any) => {
                                            prev = data;
                                            return prev;
                                        });
                                    }
                                }}
                                classes={{
                                    root: styles.autoCompleteDesc,
                                    popper: styles.autocompleteDescPanel,
                                    paper: styles.autocompleteDescInnerPanel,
                                    listbox: clsx(styles.listAutoComletePanel, 'cassMappinglist'),
                                }}
                                isOptionEqualToValue={(option, value) => {
                                    return !value ? false : `${option.display_title}` === value;
                                }}
                            />
                        </>
                    )
                )}
            </Box>
    );
}
export default EditTagsAutoComplete;