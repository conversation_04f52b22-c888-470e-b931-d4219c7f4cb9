
import { useEffect, useState } from "react";
import styles from "../VideoUploads.module.scss";
import MediaInputField from "../../../components/common/MediaInputField";

const MultipleFilesSelection = ({ confirmationPopupClose, setValue, watch, isFilledAllThumbnails, isEdit, getValues, defaultThumbnailFiles, setHasFieldValuesChanged, setIsFilledAllThumbnails}: any) => {

  const [resetData, setResetData] = useState<any>(null);

  useEffect(()=>{
    if(setIsFilledAllThumbnails){
      setIsFilledAllThumbnails(false);
    }
  },[])

  useEffect(()=>{
    if(isEdit){
      const defaultData = {...getValues('thumbnailFiles')};
      setResetData(defaultData)
    }
  },[isEdit])

  const clearSelectedThumbnail = (name:string) => {
    setValue(name, undefined, { shouldValidate: true });
  }

  const submitThumbnails = () => {
    
    confirmationPopupClose();
  }

  const handleCancelPopup = () => {
    if(isEdit){
      setValue('thumbnailFiles', resetData, { shouldValidate: true });
      setIsFilledAllThumbnails(true);
    }else{
      setValue('thumbnailFiles', defaultThumbnailFiles, { shouldValidate: true })
    }
    confirmationPopupClose()
  }

  return (
    <div className={styles.continuePopup}>
      <div className={styles.overFlowForPop}>
        <div className={styles.uploadThumbnailSection}>
          <div className={styles.uploadVideo}>
            <span className={styles.thumbTitle}>Resolution (140 x 120) <span className={styles.uploadVideoNote}>*</span></span>
            <div className={styles.thumbnailDescription}>
              Used at:
              <ul>
                <li>Mobile, Tablet & Desktop app video listing</li>
              </ul>
            </div>
            <MediaInputField width={140} height={120} type={'image'} setValue={setValue} name={'thumbnailFiles.thumbnail_app'} watch={watch} clearMediaData={clearSelectedThumbnail} setHasFieldValuesChanged={setHasFieldValuesChanged} />
          </div>
          <div className={styles.uploadVideo}>
            <span className={styles.thumbTitle}>Resolution (200 x 140) <span className={styles.uploadVideoNote}>*</span></span>
            <div className={styles.thumbnailDescription}>
              Used at:
              <ul>
                <li>Safe Page video listing</li>
              </ul>
            </div>
            <MediaInputField width={200} height={140} type={'image'}  setValue={setValue} name={'thumbnailFiles.thumbnail_safe'}  watch={watch} clearMediaData={clearSelectedThumbnail} setHasFieldValuesChanged={setHasFieldValuesChanged} /> 
          </div>
          <div className={styles.uploadVideo}>
            <span className={styles.thumbTitle}>Resolution (300 x 215) <span className={styles.uploadVideoNote}>*</span></span>
            <div className={styles.thumbnailDescription}>
              Used at:
              <ul>
                <li>Video player on mobile</li>
                <li>Safe Page Popup Player on mobile</li>
                <li>Safe Page Intro Player on mobile</li>
              </ul>
            </div>
            <MediaInputField width={300} height={215} type={'image'}  setValue={setValue} name={'thumbnailFiles.intro_mobile'}  watch={watch} clearMediaData={clearSelectedThumbnail} setHasFieldValuesChanged={setHasFieldValuesChanged} /> 
          </div>
          <div className={styles.uploadVideo}>
            <span className={styles.thumbTitle}>Resolution (400 x 340) <span className={styles.uploadVideoNote}>*</span></span>
            <div className={styles.thumbnailDescription}>
              Used at:
              <ul>
                <li>Video player on tablet</li>
                <li>Safe Page Popup Player on tablet</li>
                <li>Safe Page Intro Player on tablet</li>
              </ul>
            </div>
            <MediaInputField width={400} height={340} type={'image'}  setValue={setValue} name={'thumbnailFiles.intro_tablet'}  watch={watch} clearMediaData={clearSelectedThumbnail} setHasFieldValuesChanged={setHasFieldValuesChanged} />
          </div>
          <div className={styles.uploadVideo}>
            <span className={styles.thumbTitle}>Resolution (560 x 300) <span className={styles.uploadVideoNote}>*</span></span>
            <div className={styles.thumbnailDescription}>
              Used at:
              <ul>
                <li>Video player on desktop</li>
                <li>Safe Page Popup Player on desktop</li>
              </ul>
            </div>
            <MediaInputField width={560} height={300} type={'image'}  setValue={setValue} name={'thumbnailFiles.electron_player'}  watch={watch} clearMediaData={clearSelectedThumbnail} setHasFieldValuesChanged={setHasFieldValuesChanged} /> 
          </div>
          <div className={styles.uploadVideo}>
            <span className={styles.thumbTitle}>Resolution (2000 x 695) <span className={styles.uploadVideoNote}>*</span></span>
            <div className={styles.thumbnailDescription}>
              Used at:
              <ul>
                <li>Safe Page Intro Player on desktop</li>
              </ul>
            </div>
            <MediaInputField width={2000} height={695} type={'image'}  setValue={setValue} name={'thumbnailFiles.intro_desktop'} watch={watch} clearMediaData={clearSelectedThumbnail} setHasFieldValuesChanged={setHasFieldValuesChanged} /> 
          </div>
        </div>

      </div>

      <div className={styles.yesAndnoBtn}>
        <button className={styles.okBtn} onClick={submitThumbnails} disabled={!isFilledAllThumbnails} >
          Submit
        </button>
        <button
          className={styles.okBtn}
          onClick={() => { handleCancelPopup() }}
        >
          Cancel
        </button>
      </div>
    </div>
  )
}
export default MultipleFilesSelection;