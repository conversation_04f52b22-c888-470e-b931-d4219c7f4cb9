import { MenuItem, Select } from "@mui/material";
import styles from "./CassTransactionSubmissionStatus.module.scss";
import { useEffect, useState } from "react";
import Loader from "../../../components/common/Loader";
import { useDebouncedValue } from "@mantine/hooks";
import CustomDatePicker from "../../../components/common/CustomDatePicker";
import usePostGetCassTransactionSubmissionList from "../../../hooks/usePostGetCassTransactionSubmissionList";
import dayjs, { Dayjs } from "dayjs";
import clsx from "clsx";
import { AgGridReact } from "ag-grid-react";
import ReactPaginate from "react-paginate";
import { filterArrray } from "../../../utils/helper";
import { formatCurrency } from "@bryzos/giss-ui-library";
import { defaultTransactionsCountObj, TransactionsCount } from "../../../utils/constant";


const CassTransactionSubmissionStatus = () => {
    const {
        mutateAsync: getCassTransactionSubmissionList,
        data: getCassTransactionSubmissionListData,
        isLoading: isGetCassTransactionSubmissionListLoading,
    } = usePostGetCassTransactionSubmissionList();
    const [itemOffset, setItemOffset] = useState(0);
    const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(0);
    const [inputSearchValue, setInputSearchValue] = useState("");
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [rowData, setRowData] = useState([]);
    const [filteredRowData, setFilteredRowData] = useState<any[]>([]);
    const [selectedOption, setSelectedOption] = useState<string | number | null>("all");
    const [countObj, setCountObj] = useState<TransactionsCount>(defaultTransactionsCountObj);
    const [transactionAmountObj, setTransactionAmountObj] = useState<TransactionsCount>(defaultTransactionsCountObj);
    const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue?.trim(), 1000);
    const pageCount = Math.ceil(filteredRowData.length / itemsPerPage);

    const columnDefs = [
        {
            headerName: '#',
            valueGetter: (params: any) => params.node.rowIndex + itemOffset + 1,
            sortable: false,
            minWidth: 100,
            cellClass: 'holidayListCell',
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Transaction Status',
            field: 'is_transaction_success',
            sortable: false,
            minWidth: 271,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader',
            cellRenderer: (props: any) => {
                const classNameCondition = props?.data?.is_transaction_success === null ? styles.pendingStatus : props?.data?.is_transaction_success? styles.successStatus : styles.rejectStatus;
                return (
                    <button className={clsx(classNameCondition, styles.StatusBtn)}>
                        {props?.data?.is_transaction_success === null ?
                            (<>Pending</>)
                            :
                            props?.data?.is_transaction_success ?
                                (<>Success</>)
                                :
                                (<>Failed</>)
                        }
                    </button>
                );
            }
        },
        {
            headerName: 'Date Of Payment Submission',
            field: 'date_of_payment_submission',
            sortable: false,
            minWidth: 271,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'PO Number',
            field: 'po_number',
            sortable: false,
            minWidth: 271,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Seller Company Name',
            field: 'seller_company_name',
            sortable: false,
            minWidth: 271,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Transfer Type',
            field: 'transfer_type',
            sortable: false,
            minWidth: 271,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader'
        },
        {
            headerName: 'Paid Amount',
            field: 'amount_paid',
            sortable: false,
            minWidth: 271,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader',
            cellRenderer: (props: any) => {
                return (
                    <div>
                         {formatCurrency(props?.data?.amount_paid)}
                    </div>
                );
            }
        },
        {
            headerName: 'Unique Identifier',
            field: 'unique_identifier',
            sortable: false,
            minWidth: 271,
            flex: 1,
            cellClass: clsx('holidayListCell', styles.truncateText),
            headerClass: 'holidayListHeader'
        }
    ];

    const defaultColDef = {
        lockVisible: true
    };

    useEffect(() => {
        if (fromDate && toDate) {
            const payload = {
                "data": {
                    "from_date": dayjs(fromDate).format('YYYY-MM-DD'),
                    "to_date": dayjs(toDate).format('YYYY-MM-DD')
                }
            }
            getCassTransactionSubmissionList(payload)

        }
    }, [fromDate, toDate])

    useEffect(()=>{
        if(getCassTransactionSubmissionListData){
            const countObj = {...defaultTransactionsCountObj}
            const amountObj = {...defaultTransactionsCountObj}
            getCassTransactionSubmissionListData.forEach((obj: any)=>{
                const amountPaid = Number(obj.amount_paid);

                if(obj.is_transaction_success === 1){
                    countObj.success +=1 ;
                    amountObj.success += amountPaid;
                }else if(obj.is_transaction_success === 0){
                    countObj.failed +=1;
                    amountObj.failed += amountPaid;
                }else{
                    countObj.pending +=1;
                    amountObj.pending += amountPaid;
                }
                amountObj.all += amountPaid;
            });
            countObj.all = getCassTransactionSubmissionListData.length;
            setCountObj(countObj)
            setTransactionAmountObj(amountObj)
        }
    },[getCassTransactionSubmissionListData])

    useEffect(() => {
        if (getCassTransactionSubmissionListData) {
            let _filterData: any[] = [];
            _filterData =  search(debouncedInputSearchValue, getCassTransactionSubmissionListData);
            if(selectedOption !== 'all'){
                _filterData = handleFilterRowData(_filterData);
            }
            setFilteredRowData(_filterData);
            handlePageClick(0, _filterData.length)
            setRowData(getCassTransactionSubmissionListData);
        }
    }, [getCassTransactionSubmissionListData, isGetCassTransactionSubmissionListLoading, selectedOption, debouncedInputSearchValue])

    const handleFilterRowData = (data: any) => {
        return data.filter((obj: any) => obj?.is_transaction_success === selectedOption)
    }

    const handlePageClick = (currentPageNumber: number, filteredRowDataLength: number ) => {
        const newOffset = (currentPageNumber * itemsPerPage) % filteredRowDataLength;
        setCurrentPage(currentPageNumber);
        setItemOffset(newOffset);
    };
    
    const handleChange = (e: any) => {
        const value = e.target.value === "null" ? null : isNaN(e.target.value) ? e.target.value : Number(e.target.value);
        setSelectedOption(value);
        handlePageClick(0, filteredRowData.length);
    };

    
    const search = (searchString: string, data: any = rowData) => {
        setCurrentPage(0);
        setItemOffset(0);
        setInputSearchValue(searchString);
        if (searchString) {
            const _filterArrray = filterArrray(data, searchString, [
                "po_number",
                "seller_company_name",
                "amount_paid",
                "unique_identifier"
            ]);
            if (_filterArrray?.length) {
                return _filterArrray;
            } else {
                return [];
            }
        } else {
            return data;
        }
    };

    const disableFromDate = (date: Dayjs) => {
        // Disable dates after "toDate" if selected
        return toDate ? date.isAfter(toDate, "day") : false;
      };
    
      const disableToDate = (date: Dayjs) => {
        // Disable dates before "fromDate" if selected
        return fromDate ? date.isBefore(fromDate, "day") : false;
      };
    
    

    return (
        <div>
            {(isGetCassTransactionSubmissionListLoading) && (
                <>
                    <div className={styles.backDrop}></div>
                    <div className={styles.loaderImg}>
                        <Loader />
                    </div>
                </>

            )}
            <div className={styles.CassTransactionMain}>
                <div className={clsx(styles.searchBox,styles.selectDropdownCass)}>
                    <div className={styles.col1}>
                    <Select
                        className={styles.showdropdwn}
                        value={itemsPerPage}
                        onChange={(event) => {
                            setItemsPerPage(+event.target.value);
                            setCurrentPage(1);
                            handlePageClick(0, filteredRowData.length)
                        }}
                    >
                        {perPageEntriesOptions.map((item, index) => (
                            <MenuItem key={index} value={item}>
                                <span>{item}</span>
                            </MenuItem>
                        ))}
                    </Select>
                    </div>
                    <div className={styles.col1}>
                        
                   {!!getCassTransactionSubmissionListData &&  <div className={styles.totalSumListMain}>
                    <div className={styles.totalSummaryTitle}>Total Summary</div>
                   <ul className={styles.totalSumList}>
                        <li className={styles.totalSuccess}><b>Total Success</b><br/>{formatCurrency(transactionAmountObj.success)}</li>
                        <li className={styles.totalFailure}><b>Total Failure</b><br/>{formatCurrency(transactionAmountObj.failed)}</li>
                        <li className={styles.totalPending}><b>Total Pending</b><br/>{formatCurrency(transactionAmountObj.pending)}</li>
                        <li className={styles.totalSum}><b>Sum Total</b><br/>{formatCurrency(transactionAmountObj.all)}</li>
                    </ul>
                    </div>
                    }
                    </div>
           
                </div>
                <div className={clsx(styles.searchBox)}>
                    <div className={styles.leftColCassTransaction}>
                    <div>
                        <div className={styles.CassTransactionMainTitle}>
                            Select Date From And To
                        </div>
                        <div className={clsx(styles.searchBox,styles.selectDateColMain)}>
                            <div className={styles.selectDateCol}>
                                <label>From</label>
                                <CustomDatePicker value={fromDate} onChange={(newValue: any) => { setFromDate(newValue) }} disableDates={disableFromDate} format={'YYYY-MM-DD'} maxDate={dayjs()}  />
                            </div>
                            <div className={styles.selectDateCol}>
                                <label>To</label>
                                <CustomDatePicker value={toDate} onChange={(newValue: any) => { setToDate(newValue) }} disableDates={disableToDate} format={'YYYY-MM-DD'} maxDate={dayjs()} />
                            </div>
                        </div>
                    </div>
                    {!!getCassTransactionSubmissionListData && 
                    <div>
                        <div className={styles.CassTransactionMainTitle}>Transaction Status</div>
                        <div className={styles.CassTransactionChk}>
                            <label>
                                <input
                                    type="radio"
                                    value="all"
                                    checked={selectedOption === "all"}
                                    onChange={handleChange}
                                />
                                All ({countObj.all})
                            </label>
                            <label>
                                <input
                                    type="radio"
                                    value={'1'}
                                    checked={selectedOption === 1}
                                    onChange={handleChange}
                                />
                                Success ({countObj.success})
                            </label>
                            <label>
                                <input
                                    type="radio"
                                    value={'0'}
                                    checked={selectedOption === 0}
                                    onChange={handleChange}
                                />
                                Failed ({countObj.failed})
                            </label>
                            <label>
                                <input
                                    type="radio"
                                    value={"null"}
                                    checked={selectedOption === null}
                                    onChange={handleChange}
                                />
                                Pending ({countObj.pending})
                            </label>
                        </div>
                    </div>}
                    </div>
                    <div className={styles.rightEditSection}>
                        <input
                            className={styles.searchInput}
                            type="text"
                            onChange={(event) => setInputSearchValue(event.target.value)}
                            value={inputSearchValue}
                            placeholder="Search"
                        />
                    </div>
                </div>

                <div className={clsx(styles.ag_theme_quartz, styles.agGridAdmin)} style={{
                    height: "calc(100vh - 450px)",
                    width: "100%",
                    minHeight: "400px",
                    marginTop:"15px"
                }}>
                    <AgGridReact
                        rowData={filteredRowData?.slice(itemOffset, itemOffset + itemsPerPage)}
                        columnDefs={columnDefs}
                        rowHeight={50}
                        headerHeight={32}
                        defaultColDef={defaultColDef}
                        enableCellTextSelection={true}
                        suppressCellFocus={true}
                    />
                </div>
                <div className={"PaginationNumber"}>
                    <ReactPaginate
                        breakLabel="..."
                        nextLabel=">"
                        onPageChange={(e) => handlePageClick(e.selected, filteredRowData.length)}
                        pageRangeDisplayed={5}
                        pageCount={pageCount}
                        previousLabel="<"
                        renderOnZeroPageCount={(props) =>
                            props.pageCount > 0 ? undefined : null
                        }
                        forcePage={pageCount > 0 ? currentPage : -1}
                    />
                </div>
            </div>
        </div>
    );
}
export default CassTransactionSubmissionStatus;