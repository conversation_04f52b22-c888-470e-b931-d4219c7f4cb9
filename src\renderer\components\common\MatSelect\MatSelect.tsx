import { MenuItem, Select, SelectProps, Tooltip } from '@mui/material';
import clsx from 'clsx';
import { Controller, Control } from 'react-hook-form';
// import { ReactComponent as DownArrowIcon } from '../../../assets/DownArrowSelect.svg';
import styles from './MatSelect.module.scss';

export type MatSelectOptionsType = {
  title: string;
  value: string | number;
  disabled?: boolean;
}[];

type MatSelectProps = {
  fieldName: string;
  placeHolderText?: string;
  hideError?: boolean;
  control: Control<any>;
  tabIndex?: number | undefined;
  options: {
    title: string;
    value: string | number;
    disabled?: boolean;
  }[];
  onOptionChange?: (event: any) => void;
} & SelectProps;

const MatSelect: React.FC<MatSelectProps> = ({
  options,
  placeHolderText,
  hideError,
  fieldName,
  control,
  tabIndex,
  onOptionChange,
  ...rest
}) => {
  const _onOptionChange = (event: any) => {
    if (onOptionChange) {
      onOptionChange(event);
    }
  };
  return (
    <Controller
      name={fieldName}
      control={control}
      render={({
        field: { onChange, onBlur, value, name, ref },
        fieldState: { error },
      }) => (
        <Tooltip
          title={!hideError && error?.message}
          placement='top-end'
          classes={{
            popper: styles.errorStyle,
            tooltip: styles.tooltip,
          }}
        >
          <Select
            value={
              value !== null && value !== undefined && value !== ''
                ? value
                : placeHolderText
            }
            error={Boolean(error?.message)}
            onChange={(event) => {
              onChange(event);
              _onOptionChange(event);
            }}
            onBlur={onBlur}
            inputRef={ref}
            name={name}
            // IconComponent={DownArrowIcon}
            className='w-100'
            {...rest}
            classes={{
              select: clsx(styles.selectDropdown, rest.classes?.select, {
                [styles.errorInput]: error?.message,
              }),
              ...rest.classes,
            }}
            tabIndex={tabIndex}
          >
            <MenuItem key={placeHolderText} value={placeHolderText} disabled>
              {placeHolderText}
            </MenuItem>
            {options.map((item, index) => (
              <MenuItem
                key={item.title + index}
                value={item.value}
                disabled={!!item.disabled}
              >
                <span dangerouslySetInnerHTML={{ __html: item.title }}></span>
              </MenuItem>
            ))}
          </Select>
        </Tooltip>
      )}
    />
  );
};

export default MatSelect;
