.splitPoContainer {
  padding: 20px;
  max-width: 2100px;
  margin: 0 auto;
  background-color: #fff;
  height: calc(100vh - 170px);
  display: flex;
  flex-direction: column;
}

.backButtonContainer {
  margin-bottom: 40px;
  display: flex;
  align-items: center;
  margin-right: 20px;
  @media (max-width: 2560px) {
     margin-bottom: 20px;
  }

  .backButton {
    display: flex;
    align-items: center;
    gap: 6px;
    font-family: Noto Sans;
    font-size: 24px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #000;
    background: none;
    border: none;
    cursor: pointer;
  }
  
  .invoiceHistoryButton {
    display: flex;
    align-items: center;
    gap: 6px;
    font-family: Noto Sans;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    color: #fff;
    background-color: #343a40;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    margin-left: auto;
    padding: 8px 15px;
  }
}

.invoiceContainer {
  display: flex;
  gap: 10px;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.invoiceColumn {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 0;
}

.columnHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
  padding-bottom: 15px;
  margin-bottom: 0;

  h3 {
    font-family: Noto Sans;
    font-size: 24px;
    font-weight: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #000;
    margin: 0;
  }

  .emailButton {
    padding: 8px 12px;
    border-radius: 9.7px;
    background-color: #d9d9d9;
    border: none;
    cursor: pointer;
    font-family: Noto Sans;
    font-size: 18px;
    font-weight: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: center;
    color: #000;
   &[disabled]{
      opacity: 0.5;
      cursor: not-allowed;
    }

    &:hover {
      background-color: #e0e0e0;
    }
  }
}

.invoiceBoxMain {
  margin: 12px 0px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px;

  &.noScrollBar{
    &::-webkit-scrollbar {
     display: none;
    }
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }
}

.invoiceBox {
  flex-grow: 0;
  object-fit: contain;
  border: solid 1px #000;
  background-color: #fff;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
  min-height: 1450px;
  display: flex;
  flex-direction: column;
}

.invoiceHeader {
  display: flex;
  margin-bottom: 10px;
  column-gap: 16px;

  .logoSection {
    flex: 0 auto;

    .logo {
      margin-bottom: 10px;

      img {
        width: 100%;
        max-width: 379.7px;
        height: 81.7px;
        object-fit: contain;
        @media (max-width: 1920px) {
          max-width: 270px;
        }
      }
    }

    .paymentNote {
      margin: 5px 0;
      font-family: Inter;
      font-size: 12.2px;
      font-weight: 600;
      line-height: 1.3;
      text-align: left;
      color: #0f0f14;

    }
  }

  .invoiceDetailsSection {
    flex: 0 0 45%;
    border: solid 1.3px #71737f;
    max-width: 380px;
    margin-left: auto;

    .invoiceTitle {
      font-family: Syncopate;
      font-size: 23.7px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: center;
      color: #0f0f14;
      height: 48.5px;
      align-self: stretch;
      flex-grow: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      border-bottom: solid 1.3px #71737f;
    }

    .invoiceDetails {
      padding: 10px;
      display: flex;
      flex-direction: column;
      row-gap: 5px;

      .editInvoiceInput {
        margin-top: -5.6px;
        margin-left: auto;
      }

      .paymentDueInput {
        color: #1c40e7;
      }
    }
  }
}

.invoiceRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: Inter;
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #0f0f14;

  span:first-child {
    font-weight: 600;
  }

  span:last-child {
    font-weight: normal;
  }

  input {
    width: 89px;
    height: 28px;
    border-radius: 6px;
    border: solid 2px #16b9ff;
    font-family: Inter;
    font-size: 14.5px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: right;
    color: #0f0f14;

    &:focus {
      outline: none;
    }
  }
}

.paymentDueDate {
  color: #1c40e7;
  font-weight: 500;
}

.paymentDueDateValue {
  color: #1c40e7;
  font-weight: 500;
}

.billingSection {
  display: flex;
  margin-bottom: 16px;
  column-gap: 12px;

  .fulfilledByContainer{
    display: flex;
    gap: 12px;
    flex: 0 55%;

  }

  .billTo,
  .fulfilledBy {
    flex: 0 267px;

    h4 {
      font-family: Syncopate;
      font-size: 15.5px;
      font-weight: bold;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
      margin-bottom: 8px;
      margin-top: 0;
    }

    p {
      margin: 0 0 3px 0;
      font-family: Inter;
      font-size: 14.1px;
      font-weight: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
      word-break: break-all;
    }

    input {
      width: 100%;
      padding: 6px;
      margin-bottom: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;

      &:focus {
        outline: none;
        border-color: #0066CC;
        box-shadow: 0 0 3px rgba(0, 102, 204, 0.5);
      }
    }
  }

  .paymentInstructionsBox {
    margin-left: auto;
    flex: 0 0 45%;
    width: 100%;
    max-width: 380px;
    min-height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 8px;
    border-radius: 13.2px;
    border: solid 1.3px #71737f;

    h4 {
      font-family: Inter;
      font-size: 15.8px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
      margin: 0 0 2px 0;
      text-align: center;
    }

    .paymentMethodRow {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 5px;
      font-weight: bold;
      font-family: Inter;
      font-size: 15.8px;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
      position: relative;

      span {
        font-weight: normal;
      }

      .noChecks {
        color: #ea5b1f;
        font-weight: bold;
      }

      .greenArrow {
        display: inline-flex;
        align-items: center;
        vertical-align: middle;
        position: absolute;
        right: -85px;
        top: -20px;
      }
    }

    .paymentDetails {
      font-size: 12px;

      .paymentRow {
        display: flex;
        font-family: Inter;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 1px;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: left;
        color: #0f0f14;

        span:first-child {
          width: 137.1px;
        }

        span:last-child {
          flex: 1;
          word-wrap: break-word;
          margin-left: 10px;
        }
      }
    }
  }
}

.paymentInfo {

  h3 {
    font-family: Inter;
    font-size: 24.2px;
    font-weight: 600;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #0f0f14;
    margin: 0px 0px 6px 0px;
  }
}

.lineItems {

  h4 {
    font-family: Syncopate;
    font-size: 16.5px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #0f0f14;
    margin: 0px 0px 12px 0px;
  }
}

.itemTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;

  th,
  td {
    text-align: left;
    vertical-align: middle;
  }

  th {
    height: 35px;
    background-color: #f2f2f2;
    color: #333;
    font-weight: 600;
    padding: 0 10.5px;
    border-top: solid 1.3px #71737f;
    border-bottom: solid 1.3px #71737f;
    background-color: #eaecf0;
    font-family: Inter;
    font-size: 15.8px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #0f0f14;

    &:nth-child(1) {
      text-align: center;
      width: 90px;
    }

    &:nth-child(3) {
      width: 15%;
      text-align: center;
    }

    &:nth-child(4),
    &:nth-child(5) {
      text-align: right;
    }
  }

  tbody {
    tr {
      &:nth-child(odd) {
        background-color: #fff;
      }

      &:nth-child(even) {
        background-color: #f2f4f6;
      }

      td {
        font-family: Inter;
        font-size: 15.8px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.6;
        letter-spacing: normal;
        text-align: left;
        color: #0f0f14;
        padding: 6px 12px;

        p {
          margin: 0;
        }

        .domesticMaterialOnly {
          font-family: Inter;
          font-size: 15.8px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.4;
          letter-spacing: normal;
          text-align: left;
          color: #0f0f14;
        }

        .editInputTd{
          input{
             width: 70px;
             height: 35px;
             border-radius: 8px;
             border: solid 2px #16b9ff;
             background-color: transparent;
             &:focus{
              outline: none;
             }
          }
        }

        .priceUnitInputEditMain {
          display: flex;
          align-items: center;

          .priceUnitInputEdit {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 95px;
            height: 35px;
            border-radius: 8px;
            border: solid 2px #16b9ff;
            padding: 0px 6px;
           font-family: Inter;
            font-size: 15.8px;
            font-weight: normal;
            font-style: normal;
            line-height: 1.6;
            letter-spacing: normal;
            text-align: left;
            color: #0f0f14;
            margin-right: 2px;
            span{
              line-height: normal;
            }
            input {
              border: 0px;
              width: 75%;
              background-color: transparent;
              padding: 0px;
               &:focus {
                outline: none;
              }
            }
          }
          .priceUnitEdit{
            display: flex;
            width: 50px;
          }
        }
      

        &:nth-child(1) {
          width: 90px;
          text-align: center;
          font-size: 20px;
          font-weight: 600;
        }


        &:nth-child(3) {
          width: 15%;
          text-align: center;

          div {
            text-align: center;

          }
        }

        &:nth-child(4),
        &:nth-child(5) {
          width: 15%;
          text-align: right;
        }

        .redText {
          color: #ff0000;
          text-align: right;
        }

        &.addRowTd {
          text-align: left;
          padding-left: 35px;
        }
      }

      &:not(:last-child) {
        border-bottom: solid 1.3px #71737f;

      }
    }
  }



  .totalSection {

    td {
      padding: 8px;
      text-align: right;
      border-top: 1px solid #aaa;
      font-weight: 500;

      &:first-child {
        text-align: right;
      }

      &:last-child {
        font-weight: 600;
      }
    }
  }
}


.materialTotalTable {
  width: 100%;
  border-collapse: collapse;

  tr {
    border-top: solid 1.3px #71737f;
    border-bottom: solid 1.3px #71737f;
    background-color: #eaecf0;

    th {
      font-family: Inter;
      font-size: 18.5px;
      font-weight: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
      padding: 0px 12px;
      height: 40.5px;

      &:first-child,
      &:nth-child(2) {
        width: 25%;
      }

      &:nth-child(3) {
        width: 148px;
      }

      &:nth-child(4) {
        width: 50px;
        text-align: center;
        padding-right: 0;
        padding-left: 0;
      }

      &:nth-child(5) {
        padding-left: 0;
        width: 95px;
        text-align: right;
      }
    }
  }
}

.emptyRow {
  height: 100px;
  text-align: center !important;
  vertical-align: middle !important;
}

.addItemButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #1c40e7;
  cursor: pointer;
  font-size: 14px;

  span {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #1c40e7;
    color: white;
    margin-right: 5px;
    line-height: 20px;
  }
}

.invoiceFooter {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  position: relative;

  .note {
    flex: 0 0 50%;
    max-width: 432.4px;
    margin-bottom: 15px;
    padding-right: 0px;
    .paymentNote1{
      font-family: Inter;
      font-size: 18.5px;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.8;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
    }

    .paymentNote2{
      p{
        margin-bottom: 16px;
        &:nth-child(1){
          font-family: Inter;
          font-size: 12px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.8;
          letter-spacing: normal;
          text-align: left;
          color: #0f0f14;
        }
        &:nth-child(2){
          font-family: Inter;
          font-size: 12px;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.8;
          letter-spacing: normal;
          text-align: left;
           font-weight: normal;
           color: #0f0f14;
           span{
              color: #ec1717;
              font-weight: 600;
           }
        }
        &:nth-child(3){
            font-family: Inter;
            font-size: 14px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.6;
            letter-spacing: normal;
            text-align: left;
            color: #0f0f14;
        }
      }
    }
  }

  .totals {
    flex: 0 0 50%;
    // max-width: 350.7px;
    margin-bottom: 15px;

    table {
      width: 100%;
      border-collapse: collapse;
      font-family: Inter;
      font-size: 18.5px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;

      tr {
        &:first-child {
          background-color: #fff;
        }

        height: 40.5px;

        &:last-child {
          background-color: #eaecf0;
          font-weight: 600;
          height: 45px;
          font-size: 21.1px;
        }

        td {
          padding: 0px 12px;

          input {
            border: 0px;
            padding-right: 0px;

            &:focus {
              outline: none;
            }

          }

          &:nth-child(1) {
            width: 145px;
            padding-right: 0px;
          }

          &:nth-child(2) {
            width: 40px;
            text-align: center;
            padding-right: 0;
          }

          &:nth-child(3) {
            padding-left: 0;
            width: 100px;
            text-align: right;
          }
        }
      }
    }
  }

  .taxExemption {
    flex: 0 0 100%;

    p {
      font-family: Inter;
      font-size: 13.2px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
      padding-top: 10px;
      clear: both;
      margin-top: 0px;
    }

  }
}

.pageNumber {
  margin-top:auto;
   font-family: Inter;
  font-size: 15.8px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.6;
  letter-spacing: normal;
  text-align: right;
  color: #0f0f14;
}

.adjustments {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  align-items: center;
  position: sticky;
  bottom: 0;
  z-index: 10;

  span {
    font-family: Noto Sans;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #000;
    margin: 0 auto;
  }

  .adjustmentButton {
    height: 44.5px;
    border-radius: 9.7px;
    background-color: #d9d9d9;
    border: none;
    cursor: pointer;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #000;
    padding: 0px 6px;

    &:hover {
      background-color: #e0e0e0;
    }
  }
}

@media (max-width: 768px) {
  .billingSection {
    flex-direction: column;

    .billTo,
    .fulfilledBy,
    .paymentInstructionsBox {
      margin-bottom: 15px;
    }
  }

  .invoiceHeader {
    flex-direction: column;

    .logoSection,
    .invoiceDetailsSection {
      flex: 0 0 100%;
      margin-bottom: 15px;
    }
  }
}

.errorMessage {
  color: #ff0000;
  font-size: 12px;
  margin-top: 2px;
  display: block;
}

.submitButtonContainer {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.submitButton {
  background-color: #0066CC;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #0052a3;
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
}

.adjustmentInput {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: Inter;
  font-size: 18.5px;
  font-weight: normal;
  line-height: 1.6;
  letter-spacing: normal;
  color: #0f0f14;
  text-align: right;
  border: 2px solid #16b9ff;
}

.fulfilledByInput.fulfilledByInput {
  height: 145px;
  border-radius: 8px;
  border: solid 2px #16b9ff;
  padding: 8px 6px;

  input {
    border: none;
    outline: none;
    padding: 0;
    background: transparent;
    font-family: Inter;
    font-size: 14.1px;
    font-weight: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #0f0f14;
    margin-bottom: 1px;

    &:focus {
      outline: none;
      box-shadow: none;
    }
  }

  .coInput {
    display: flex;
    align-items: center;

    span {
      margin-right: 3px;
    }
  }
}

.addButton {
  background-color: transparent;
  border: 0px;
  padding: 0px;
  cursor: pointer;
}


.orderContinuePopup {
  h2 {
      display: none;
  }
  .continuePopup {
      padding: 20px;
      text-align: center;
      width: 380px;
      @media screen and (max-width: 768px) and (min-width: 320px) {
          width: 280px;
      }
      .continuetext {
          text-align: center;
          font-size: 20px;
          margin-bottom: 24px;
          color: var(--primaryColor);
      }
      .yesAndnoBtn {
          display: flex;
          gap: 10px;
          .okBtn {
              width: 100%;
              height: 45px;
              border-radius: 6px;
              text-decoration: none;
              gap: 8px;
              border: none;
              font-size: 16px;
              font-weight: 500;
              cursor: pointer;
              background-color: var(--primaryColor);
              color: #fff;
          }
      }
  }
}


.sendEmailGrid{
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;

  span{
      width: 30px;
      font-size: 14px;
      text-align: left;
  }

  .sendEmailinput {
      width: 120px;
      border-radius: 4px;
      border: solid 1px #c4c8d1;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.57;
      letter-spacing: normal;
      text-align: left;
      color: #3b4665;
      height: 34px;
      padding: 0px 10px;
      flex: 1;
      &:read-only{
          background-color: #e3e3e3;
      }

      &:focus{
          outline: none;
      }
  }
}

.loaderImg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 98;
}

.emailSentMain.emailSentMain {
  position: absolute;
  
  .emailSplitPopup {
    position: absolute;
    top: 50%;
    left: 75%;
    transform: translate(-50%, -50%);
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .emailInvoicePopup {
    position: absolute;
    top: 50%;
    left: 25%;
    transform: translate(-50%, -50%);
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.twoColumnLayout {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

}

.descriptionWithRemove {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  > span {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    flex: 1;
  }
}

.removeButton {
  position: absolute;
  left: 15px;
  background: none;
  border: none;
  border-radius: 50%;
  width: 26px;
  height: 26px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 10px;
  flex-shrink: 0;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #ffebeb;
    transform: scale(1.1);
  }
  
  svg {
    width: 16px;
    height: 16px;
    
    path {
      fill: #ff0000;
    }
  }
}

.invoiceHistoryPopup {
  .MuiPaper-root {
    border-radius: 8px;
    max-width: 700px;
    min-width: 300px;
    width: 90%;
    max-height: 90vh;
    min-height: 200px;
  }
}

.invoiceHistoryContainer {
  display: flex;
  flex-direction: column;
  min-height: 200px;
  
  .invoiceHistoryHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    
    h3 {
      margin: 0;
      font-family: Noto Sans;
      font-size: 18px;
      font-weight: normal;
      color: #000;
    }
    
    .closePopupButton {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      svg {
        width: 16px;
        height: 16px;
        
        path {
          fill: #666;
        }
      }
      
      &:hover svg path {
        fill: #000;
      }
    }
  }
  
  .invoiceHistoryContent {
    padding: 16px;
    max-height: calc(90vh - 120px);
    min-height: 140px;
    overflow-y: auto;
    flex: 1;
    
    .tblscroll {
      width: 100%;
      
      .invoiceHistoryTable {
        width: 100%;
        border-collapse: collapse;
        
        th, td {
          padding: 10px 12px;
          text-align: left;
          border-bottom: 1px solid #e0e0e0;
        }
        
        th {
          background-color: #f5f5f5;
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
        
        td {
          font-family: Noto Sans;
          font-size: 13px;
          color: #333;
          
          &:first-child {
            max-width: 400px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .downloadButton {
            padding: 5px 10px;
            background-color: #16b9ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            
            &:hover {
              background-color: #0ea5e6;
            }
          }
          
          &.noHistoryMessage {
            text-align: center;
            padding: 24px 12px;
            font-style: italic;
            color: #666;
          }
        }
        
        tr:hover {
          background-color: #f9f9f9;
        }
      }
    }
  }
}

.invoiceHistoryPopupPaper.invoiceHistoryPopupPaper{
  width: 100%;
  max-width: 1000px;
}