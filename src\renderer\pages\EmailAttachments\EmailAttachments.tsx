import { useContext, useEffect, useRef, useState } from "react";
import ReactPaginate from "react-paginate";
import { Select, MenuItem } from "@mui/material";
import { useImmer } from "use-immer";
import Loader from "../../components/common/Loader";
import styles from "./EmailAttachments.module.scss";
import useGetInvoiceEmailAttachments from "../../hooks/useGetInvoiceEmailAttachments";
import { useDebouncedValue, useSetState } from "@mantine/hooks";
import ExtractedFieldsData from "./components/extractedFieldsData";
import checkIcon from '../../../assests/images/check-icon.svg';
import notCheckIcon from '../../../assests/images/Close.svg';
import { AgGridReact } from "ag-grid-react";
import clsx from "clsx";
import "ag-grid-community/styles/ag-grid.css";
import { Tooltip } from "@mui/material";
import { convertUtcToCtTimeUsingDayjs, downloadFilesUsingFetch, fileType } from "@bryzos/giss-ui-library";
import MatPopup from "../../components/common/MatPopup";
import useGetAdminReferenceData from "../../hooks/useGetAdminReferenceData";
import useSendEmail from "../../hooks/useSendEmail";
import useHideUnhideSellerInvoice from "../../hooks/useHideUnhideSellerInvoice";
import { CommonCtx } from "../AppContainer";
import { ReactComponent as DownloadIcon } from '../../../assests/images/icondownload.svg';
import { ReactComponent as ClearIcon } from '../../../assests/images/Close.svg';
import emailIcon from '../../../assests/images/Email.svg';
import viewIcon from '../../../assests/images/view.svg';
import hideIcon from '../../../assests/images/hide.svg';
import { convertUtcTotoLocaleTimeUsingDayjs } from "../../utils/helper";
import { HEADER_BUYER_GENERATED_INVOICE, HEADER_DOWNLOAD_EXTRACTED_ATTACHMENT_LINK, HEADER_EXTRACTED_INVOICE_LINE_ITEMS, HEADER_SEND_BUYER_INVOICE_EMAIL , mimeTypeToExtension } from "../../utils/constant";
class CustomHeaderComponent {
  init(params) {
    this.eGui = document.createElement('div');
    this.eGui.className = styles.customHeader;
    this.eGui.innerHTML = `<div><b>${params.displayName}</b></div>`;
  }
  
  getGui() {
    return this.eGui;
  }
}

const EmailAttachments = () => {
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue, 1000);
  const [invoiceEmailAttachments, setInvoiceEmailAttachments] = useImmer<any[]>(
    []
  );
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [meta, setMeta] = useImmer<any>(null);
  const [rowData, setRowData] = useState([]);
  const [sortString, setSortString] = useState("");
  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);
  const [emailData, setEmailData] = useImmer<any>(null);
  const [invoiceEmailRecipients, setInvoiceEmailRecipients] = useImmer<any>({});
  const invoiceCompartor = () =>{}
  const [showAllRecords,setShowAllRecords] = useState(false)
  const [hideUnhidePopupModal, setHideUnhidePopupModal] = useState(false);
  const [hideUnhideSelectedRecord,setHideUnhideSelectedRecord] = useState({})
  const [emailSentOrNot,setEmailSentOrNot] = useState('all')
  const showPopupFormAnyComponent = useContext(CommonCtx);
  const [isColDefReady, setIsColDefReady] = useState(false);
  const tableRef = useRef(null);
  const [colDefs, setColDefs] = useState([
    {
      field: "po_number",
      headerName: "Matched PO Number",
      minWidth: 220,
      flex:1,
      pinned: 'left',
      cellClass:'poNOMatchCol',
      cellRenderer: (props:any) => {
        return (
        <div className={styles.poLineMatchMain}>
         {!!props?.data?.is_cancelled && <Tooltip title={'PO Cancelled'}>
            <span className={styles.poNumberExclamation}>!</span>
          </Tooltip> }
          {props?.data?.po_number}</div>
      );
      },
    },
    {
      field: "attachment_links",
      headerName: HEADER_DOWNLOAD_EXTRACTED_ATTACHMENT_LINK,
      headerComponent: CustomHeaderComponent,
      cellRenderer: (props) => (
        <CustomButtonComponent props={props} headerName = {HEADER_DOWNLOAD_EXTRACTED_ATTACHMENT_LINK} value={"seller_invoice_s3_url"} fileName={props.data.original_file_name} />
      ),
      sortable: false,
      minWidth: 180,
      maxWidth:180,
      flex:1,
      pinned: 'left',
      cellClass:"iconCenterCell",
      headerClass:"headerCenter"
    },
    {
      field: "extracted_fields",
      headerName: HEADER_EXTRACTED_INVOICE_LINE_ITEMS,
      headerComponent: CustomHeaderComponent,
      cellRenderer: (props) => {
        return (
          <>
            {props?.data?.invoice_line_items?.length > 0 &&  <ExtractedFieldsData attachmentData={props.data} headerName = {HEADER_EXTRACTED_INVOICE_LINE_ITEMS}/>}
          </>
        );
      },
      sortable: false,
      minWidth: 180,
      flex:1,
      cellClass:"iconCenterCell",
      headerClass:"headerCenter"
    },
    {
      field: "buyer_invoice",
      headerName: HEADER_BUYER_GENERATED_INVOICE,
      cellRenderer: (props) => (
        <CustomButtonComponent props={props} headerName = {HEADER_BUYER_GENERATED_INVOICE} value={"buyer_invoice_s3_url"} fileName={props.data.buyer_invoice_file_name}/>
      ),
      sortable: false,
      minWidth: 180,
      flex:1,
      cellClass:"iconCenterCell",
      headerClass:"headerCenter"
    },
    {
      field: "is_po_line_matched",
      headerName: ` Line Items Match \n(Matched PO vs Extracted PO)`,
      headerComponent: CustomHeaderComponent,
      cellRenderer: (props) => {
        return (
          <>
            {!!props?.data?.is_po_line_matched ? <span><img src={checkIcon}/></span> : <span><img src={notCheckIcon}/></span>}
          </>
        );
      },
      sortable: false,
      minWidth: 250,
      flex:1,
      headerClass:'poLineMatch',
      cellClass:'poLineMatchCol'
    },
    {
      field: "send_buyer_invoice",
      headerName: HEADER_SEND_BUYER_INVOICE_EMAIL,
      cellRenderer: (props) => (
        <SendEmailComponent props={props} headerName = {HEADER_SEND_BUYER_INVOICE_EMAIL} value={"buyer_invoice_s3_url"} />
      ),
      sortable: false,
      minWidth: 250,
      flex:1,
    },
    {
      field: "extracted_po_number",
      headerName: "Extracted PO Number",
      minWidth: 280,
      flex:1
    },
    {
      field: "vendor_name",
      headerName: "Extracted Vendor Name",
      minWidth: 300,
      flex:1,
      cellRenderer: (props:any) => {
        return (
        <div>
          <Tooltip title={props?.data?.vendor_name}>
              <p  className={styles.truncateText} >
                  {props?.data?.vendor_name}
              </p >
          </Tooltip>
        </div>
      );
      },
    },
    {
      field: "from_email",
      headerName: "From Email",
      minWidth: 250,
      flex:1,
      hide:true
    },
    {
      field: "invoice_total",
      headerName: "Extracted Invoice Total",
      minWidth: 180,
      flex:1

    },
    {
      field: "total_tax",
      headerName: "Extracted Invoice Tax",
      minWidth: 180,
      flex:1
    },
    {
      field: "item_count",
      headerName: "Extracted Item Count",
      minWidth: 150,
      flex:1

    },
    {
      field: "buyer_email",
      headerName: "Buyer Email Address",
      minWidth: 280,
      flex:2,
      cellRenderer: (props:any) => {
        return (
        <div>
          <Tooltip title={props?.data?.buyer_email}>
              <p  className={styles.truncateText} >
                  {props?.data?.buyer_email}
              </p >
          </Tooltip>
        </div>
      );
      },
    },
    {
      field: "is_hidden",
      headerName: "Hide Record",
      minWidth: 150,
      flex: 1,
      sortable: false,
      cellRenderer: (props: any) => {
        return (
          <Tooltip title = {props.data.is_hidden ? "Show Record" : "Hide Record"}>
            <button className={styles.hideUnhideBtn} onClick={() => {
              onHideUnhide(props.data)
            }}>{props.data.is_hidden ? <img alt = {"Show Record"} src = {viewIcon}></img> : <img alt = {"Hide Record"} src = {hideIcon}></img>}
            </button>
          </Tooltip>
        )
      }
    },
    {
      field: "created_date",
      headerName: "Invoice Received Date",
      cellRenderer: (props) => {
        return (
          <div>
            {convertUtcTotoLocaleTimeUsingDayjs(props?.data?.created_date)}
          </div>
        );
      },
      minWidth: 220,
      flex:1,
    },
  ]);

  
  const {
    data: invoiceEmailAttachmentsData,
    isLoading: isInvoiceEmailAttachmentsDataLoading,
  } = useGetInvoiceEmailAttachments(
    itemsPerPage,
    currentPage,
    debouncedInputSearchValue,sortString, showAllRecords, emailSentOrNot
  );
  const { data: adminReferenceData } = useGetAdminReferenceData();

  const {
    data: hideUnhideSellerInvoiceData,
    isLoading: hideUnhideSellerInvoiceLoading,
    mutateAsync: hideUnhideSellerInvoice,
  } = useHideUnhideSellerInvoice();

  useEffect(() => {
    if (isInvoiceEmailAttachmentsDataLoading) {
      return;
    }
    if (invoiceEmailAttachmentsData?.meta) {
      setMeta(invoiceEmailAttachmentsData.meta);
    }

    if (invoiceEmailAttachmentsData?.data?.length) {
      setInvoiceEmailAttachments(invoiceEmailAttachmentsData.data);
      let invoiceEmailAttachmentsRowData = []
      if(showAllRecords){
       invoiceEmailAttachmentsRowData = invoiceEmailAttachmentsData.data
      }else{
        invoiceEmailAttachmentsData.data.forEach((record : any)=>{
          if(!record.is_hidden){
            invoiceEmailAttachmentsRowData.push(record)
          }
        })
      }      
      setRowData(invoiceEmailAttachmentsRowData);
    } else {
      setInvoiceEmailAttachments([]);
      setRowData([])
    }
  }, [invoiceEmailAttachmentsData, isInvoiceEmailAttachmentsDataLoading]);

  useEffect(() => {
    if(adminReferenceData?.email_extra_recipients?.WIDGET_SERVICE_BUYER_INVOICE){
      setInvoiceEmailRecipients(adminReferenceData.email_extra_recipients.WIDGET_SERVICE_BUYER_INVOICE);
    }
  },[adminReferenceData])
  
  const {
    mutate: sendEmail,
    data: sendEmailData,
    isLoading: isSendEmailLoading,
  } = useSendEmail();
  useEffect(() => {
    if (!hideUnhideSellerInvoiceLoading) {
      if (hideUnhideSellerInvoiceData) {
        showPopupFormAnyComponent(hideUnhideSellerInvoiceData);
      }
    }
  }, [hideUnhideSellerInvoiceData, hideUnhideSellerInvoiceLoading]);

  
  useEffect(() => {
    updatePinnedData();
  }, [tableRef?.current]);
  
  const updatePinnedData = () => {
    if (tableRef?.current) {
      if(isColDefReady){
        return;
      }
      setIsColDefReady(true)
      const width = tableRef.current?.offsetWidth;
      const updateColDefs = [...colDefs];
      const filterData = updateColDefs.map(col => {
        if (width <= 740) {
          return { ...col, pinned: false }
        } else {
          if (col.field === 'po_number' || col.field === 'attachment_links') {
            return { ...col, pinned: 'left' };
          }
          return col;
        }
      })
      setColDefs(filterData)
    }
  };

  const handlePageClick = (event: any) => {
    setCurrentPage(event.selected + 1);
  };

  const search = (searchValue: string) => {
    setCurrentPage(1);
    setInputSearchValue(searchValue);
  };

  const CustomButtonComponent = ({ props, value , headerName ,  fileName = "" }: any) => {
    const downloadAttachments = () => {
      const extension = mimeTypeToExtension[fileType.pdf]
      const filenameConsistExt = fileName.includes(extension);
      const updateFileName = filenameConsistExt ? fileName : fileName + extension;
      downloadFilesUsingFetch(props.data[value], updateFileName , fileType.pdf )
    }
    if (props && props.data && props.data[value]) {
      return (
      <Tooltip title={headerName}>  
        <button className={styles.showBtn} onClick={()=> downloadAttachments()}>
                      <DownloadIcon/>
        </button>
      </Tooltip>  
      );
    } else {
      return null;
    }
  };

  const defaultColDef = {
    sortable: true,
    lockVisible: true,
    comparator: invoiceCompartor,
    unSortIcon: true,
    cellStyle:{flex : 1},
    headerClass: clsx(styles.header_initial),
    cellClass: styles.cell_default,
    wrapHeaderText: true,
    autoHeaderHeight: true,
    lockPinned:true
  };

  const updateColDef = (newColumnState)=>{
    const updatedColumnDefs = newColumnState.map(colState => ({
      ...colDefs.find(colDef => colDef.field === colState.colId),
      ...colState
    }));
    setColDefs(updatedColumnDefs)
  }

  const onSortChanged = (event) => {
    const newColumnState = event.columnApi.getColumnState();
    const sortedColumn = newColumnState.find((col) => Boolean(col.sort));
    if (sortedColumn && sortedColumn?.colId && sortedColumn?.sort) {
      const sortPayload = `${sortedColumn?.colId}:${sortedColumn?.sort}`;
      setSortString(sortPayload);
    } else {
      setSortString("");
    }
    updateColDef(newColumnState);
  };

  const onColumnMoved = (event) => {
    const newColumnState = event.columnApi.getColumnState();
    updateColDef(newColumnState);
  }

  const onGridReady  = (event) => {
    event.api.sizeColumnsToFit();
  }

  const gridOptions = {
    icons: {
      sortAscending: '<span class="custom-sort-asc sorted"></span>',
      sortDescending: '<span class="custom-sort-desc sorted"></span>',
      sortUnSort: '<span class="custom-sort-none"></span>',
    },
  };

  
  const SendEmailComponent = ({ props, value , headerName }: any) => {
    if (props && props?.data && (props?.data?.[value] || props.data?.last_invoice_sent)) {
      return (
        <Tooltip title = {headerName}>
        <div className={styles.sendEmailMain}>
          {props?.data?.[value] && <button className={styles.showBtn} onClick={()=>{sendEmailClickHandler(props.data)}}>
              <span><img alt = {"email icon"} src={emailIcon}/></span>
          </button>
          }
          {props.data?.last_invoice_sent && <span className={styles.emailSentTxt}>Email sent :<br/>{props.data.last_invoice_sent}</span>}
        </div>
        </Tooltip>
      );
    } else {
      return null;
    }
  };

  const sendEmailClickHandler = (order: any) => {
    setShowConfirmationPopup(true);
    setEmailData(order);
  };

  const confirmationPopupYes = () => {
    if (emailData) {
      sendEmail({ data: { po_number: emailData.po_number, source: 'SELLER_INVOICE_EXTRACT' } });
    }

    confirmationPopupClose();
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setEmailData(null);
  };
  
  useEffect(() => {
    if (sendEmailData) {
      showPopupFormAnyComponent(sendEmailData);
    }
  }, [sendEmailData]);

  const confirmationPopupNo = () => {
    setHideUnhidePopupModal(false);
  };

  const onSubmit = () =>{
    const payload = {
      data: hideUnhideSelectedRecord
    }
    hideUnhideSellerInvoice(payload);
    setHideUnhidePopupModal(false)
  }
   
  const onHideUnhide = (data: any) => {
    setHideUnhideSelectedRecord({
        id: data.id,
        is_hidden: !data.is_hidden
      });
    setHideUnhidePopupModal(true)
  }
  const handleEmailSentOrNotOnChange = (event) => {
    setEmailSentOrNot(event.target.value);
  };

  const clearInput = () => {
    setInputSearchValue('');
  };

  return (
    <div className={styles.emailscontainer}>
      <div className={styles.searchBox}>
        <div className={styles.sortDataSection}>
          <Select
            className="editLinesDropdown emailAttachDropdown"
            MenuProps={{
              classes: {
                paper: styles.Dropdownpaper,
                list: styles.muiMenuList,
              },
            }}
            value={itemsPerPage}
            onChange={(event) => {
              setItemsPerPage(+event.target.value);
            }}
          >
            {perPageEntriesOptions.map((item, index) => (
              <MenuItem key={index} value={item}>
                <span>{item}</span>
              </MenuItem>
            ))}
          </Select>
          <div className={styles.showAllRecordsChk}>
            <input
              type="checkbox"
              checked={showAllRecords}
              onChange={(event) => setShowAllRecords(event.target.checked)}
            ></input>
            <span className={styles.lblInput}>Show All Records</span>
          </div>
        </div>
        <div className={styles.SortRightSection}>
        <div className={styles.filterByEmailStatus}>
          <strong>Filter by Status:</strong>
          <div className={styles.filterByContainer}>
            <div className={styles.emailStatusOption}>
              <input
                type="radio"
                value={'all'}
                onChange={handleEmailSentOrNotOnChange}
                checked={emailSentOrNot === 'all'}
              ></input>
              <span className={styles.lblInput}>All</span>
            </div>
            <div className={styles.emailStatusOption}>
              <input
                type="radio"
                value={'true'}
                onChange={handleEmailSentOrNotOnChange}
                checked={emailSentOrNot === 'true'}
              ></input>
              <span className={styles.lblInput}>Emails Sent </span>
            </div>
            <div className={styles.emailStatusOption}>
              <input
                type="radio"
                value={'false'}
                onChange={handleEmailSentOrNotOnChange}
                checked={emailSentOrNot === 'false'}
              ></input>
              <span className={styles.lblInput}>Pending Emails </span>
            </div>
          </div>
        </div>
        <div className={styles.searchContainer}>
          <input
            className={styles.searchInput}
            type="text"
            onChange={(e) => search(e.target.value)}
            placeholder="Search"
            value={inputSearchValue}
          />
           {inputSearchValue && (
            <button className={styles.clearInputIcon} onClick={clearInput}>
              <ClearIcon/>
            </button>
          )}
        </div>
        </div>
      </div>
      {isInvoiceEmailAttachmentsDataLoading || hideUnhideSellerInvoiceLoading ||
      isSendEmailLoading ? (
        <div className={styles.noDataFound}>
            <Loader />
        </div>
      ) : (
        <div
          ref={tableRef}
          className={clsx(styles.ag_theme_quartz, styles.agGridAdmin)}
          style={{
            height: "calc(100vh - 250px)",
            width: "100%",
            minHeight: "400px",
          }}
        >
          {isColDefReady && <AgGridReact
            rowData={rowData}
            columnDefs={colDefs}
            sideBar={true}
            suppressCellFocus={true}
            rowHeight={70}
            headerHeight={32}
            enableCellTextSelection={true}
            ensureDomOrder={true}
            defaultColDef={defaultColDef}
            embedFullWidthRows={true}
            onGridReady={onGridReady}
            gridOptions={gridOptions}
            onSortChanged={onSortChanged}
            onColumnMoved={onColumnMoved}
          />}
        </div>
      )}
      <div className={"PaginationNumber"}>
        {meta && (
          <ReactPaginate
            breakLabel="..."
            nextLabel=">"
            onPageChange={handlePageClick}
            pageRangeDisplayed={5}
            pageCount={meta.totalPages}
            previousLabel="<"
            renderOnZeroPageCount={(props) =>
              props.pageCount > 0 ? undefined : null
            }
            forcePage={meta.currentPage > 0 ? meta.currentPage - 1 : undefined}
          />
        )}
      </div>
      <MatPopup
        className={styles.sendEmailPopup}
        open={showConfirmationPopup}
      >
        <div className={styles.sendEmailPopupDiv}>
          <p className={styles.sendEmailText}>Email will be sent - </p>
          <div className={styles.sendEmailGrid}>
            <span>To: </span>
            <Tooltip title={emailData?.buyer_email ? emailData.buyer_email : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={emailData?.buyer_email} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <div className={styles.sendEmailGrid}>
            <span>Cc: </span>
            <Tooltip title={invoiceEmailRecipients?.cc_email ? invoiceEmailRecipients.cc_email : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={invoiceEmailRecipients?.cc_email ? invoiceEmailRecipients.cc_email : 'NA'} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <div className={styles.sendEmailGrid}>
            <span>Bcc: </span>
            <Tooltip title={invoiceEmailRecipients?.bcc_email ? invoiceEmailRecipients.bcc_email : ''}
              placement='bottom-start'
              classes={{
                popper: styles.sendEmailPopupInputTooltip,
                tooltip: styles.tooltip,
              }}
            >
              <input className={styles.sendEmailinput} type="text" value={invoiceEmailRecipients?.bcc_email ? invoiceEmailRecipients.bcc_email : 'NA'} placeholder="To email (multiple separate with a semicolon)" readOnly />
            </Tooltip>
          </div>
          <p className={styles.sendEmailText}>Do you want to continue? (you can use Generate Email feature to send invoice to desired recipients)</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationPopupYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationPopupClose}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
      {
        hideUnhideSelectedRecord &&
            <MatPopup
            className={styles.hideUnhidePopup}
            open={hideUnhidePopupModal}
            >
            <div className={styles.hideUnhidePopup}>
              <p className={styles.hideUnhidePopuptext}>
                Do you want to {hideUnhideSelectedRecord?.is_hidden ? "hide" : "unhide"} this record?
              </p>
              <div className={styles.yesAndnoBtn}>
                <button className={styles.okBtn} onClick={onSubmit}>Yes</button>
                <button className={styles.okBtn}onClick={() => {confirmationPopupNo();}}>No</button> 
                </div>
            </div>
            </MatPopup>
      }
      
    </div>
  );
};

export default EmailAttachments;
