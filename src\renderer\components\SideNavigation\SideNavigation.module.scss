.sideNavigationbar {
    height: 100%;
    display: flex;
    flex-direction: column;
    .sideNavMain{
        overflow: auto;
        height: 100%;
        
        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }
    
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px #a8b2bb;
            border-radius: 4px;
        }
    
        &::-webkit-scrollbar-thumb {
            background: #a8b2bb;
            border-radius: 4px;
        }
    }
}

.sidebarTitle {
    display: flex;
    align-items: center;
}

.activeLink {
    font-weight: bold;
    border-left: 4px solid #007bff;
    padding-left: 10px;
  }

.searchText{
    box-shadow: none;
    outline: none;
    height: 38px;
    width: 250px;
    padding: 6px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.naviagtionTitle {
    width: 100%;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    padding: 10px 0;

    &:disabled{
        background-color: transparent;
        border: none;
        text-align: left;
        cursor: not-allowed;
        opacity: 0.5;
    }

}

.naviagtionTitleRef {
    width: 100%;
    color: #fff;
    font-size: 22px;
    font-weight: 600;
    padding: 10px 0;

  

    a {
        width: 100%;
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        text-decoration: none;
        margin-bottom: 16px;
        white-space: break-spaces;
    }
}

.mainMenu {
    color: #fff;
    font-size: 18px;
    padding: 10px 0;
    display: flex;
    align-items: center;

    .subMenuLink {
        text-decoration: none;
        padding-left: 15px;
        color: #fff;
        font-size: 16px;
    }

}

.redExlametry {
    color: red;
    font-size: 25px;
    margin-left: auto;
    margin-right: 5px;
}