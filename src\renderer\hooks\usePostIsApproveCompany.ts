import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

export type ApproveCompanyPayload = {
    id: string,
    company_name: string,
  }

  type Payload = {
    data: ApproveCompanyPayload[], 
    action: boolean
  }

const usePostIsApproveCompany = () => {
  const queryClient = useQueryClient();

  return useMutation(async (obj: Payload) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/user/approveCompany/${obj.action}`,
        obj.data
      );

      queryClient.invalidateQueries([reactQueryKeys.getAllPendingCompanies]);
      queryClient.invalidateQueries([reactQueryKeys.getAllCompany]);

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message ?? "");
    }
  });
};

export default usePostIsApproveCompany;
