.searchBox {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;

  input {
    padding: 0px 12px;
  }

  @media (max-width:767px) {
    flex-direction: column;
  }

  .sortDataSection {
    display: flex;
    align-items: center;

    .showAllRecordsChk {
      display: flex;
      flex: 1;
      margin-left: 16px;

      @media (max-width:767px) {
        margin-left: 8px;
      }

      .lblInput {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 16px;
        margin-left: 3px;

        @media (max-width:767px) {
          font-size: 14px;
        }
      }

      input[type=checkbox] {
        cursor: pointer;
      }

    }
  }

  .SortRightSection {
    display: flex;
    align-items: center;

    @media (min-width:1025px) {
      display: contents;
    }

    @media (max-width:767px) {
      align-items: flex-start;
      flex-direction: column;
    }

    .searchContainer {
      margin-left: 24px;

      input {
        height: 40px;
      }

      @media (max-width:1024px) {
        margin-left: auto;
      }

      @media (max-width:767px) {
        margin-left: unset;
        width: 100%;
        margin-top: 12px;
      }
    }
  }
}

.poLineMatchMain {
  display: flex;
  align-items: center;

  .poNumberExclamation {
    background: #ff0000;
    border-radius: 50%;
    color: #fff;
    cursor: pointer;
    display: flex;
    height: 20px;
    width: 20px;
    align-items: center;
    justify-content: center;
    line-height: 1;
    font-size: 16px;
    margin-right: 8px;
  }

}

.noDataFound {
  text-align: center;
}

.sendEmailMain {
  display: flex;
  align-items: center;

  .emailSentTxt {
    font-family: Noto Sans;
    margin-left: 10px;
    font-weight: 500;
    color: #0099ff;
    font-size: 15px;
    white-space: break-spaces;
  }
}

.showBtn {
  cursor: pointer;
  background-color: transparent;
  padding: 0;
  border: 0px;

  span {
    justify-content: center;
  }

  svg {
    width: 40px;
    height: 40px;

    path {
      fill: var(--primaryColor);
    }
  }

  a {
    padding: 0;
  }
}

.ag_theme_quartz {
  --ag-foreground-color: #676f7c;
  --ag-background-color: white;
  --ag-header-foreground-color: white;
  --ag-header-background-color: #676f7c;
  --ag-odd-row-background-color: #f2f2f2;
  --ag-header-column-resize-handle-color: rgb(126, 46, 132);
  --ag-font-size: 14px;
  --ag-font-family: monospace;
  --ag-icon-font-code-aggregation: "\f247";
  --ag-icon-font-color-group: red;
  --ag-icon-font-weight-group: normal;
  --ag-cell-horizontal-padding: 20px
}

.emailscontainer {
  overflow-y: hidden;

  @media (max-width: 475px) {
    overflow: auto;
  }
}

.agGridAdmin {
  --ag-icon-font-code-asc: '\25B2';
  --ag-icon-font-code-desc: '\25BC';
  --ag-icon-font-code-none: '\25B2\25BC';

  .ag-center-cols-viewport {
    min-height: 5000px !important;
  }

  .ag-icon-asc::before {
    content: var(--ag-icon-font-code-asc);
  }

  .ag-icon-none::before {
    content: var(--ag-icon-font-code-none);
    color: green;
    padding: 2px;
    margin-bottom: 5px;
    font-size: 20px !important;
  }

  .ag-root-wrapper {
    .ag-root-wrapper-body {
      .ag-body-horizontal-scroll-viewport {
        overflow-x: auto;

        &::-webkit-scrollbar {
          width: 8px;
          height: 6px;
        }

        &::-webkit-scrollbar-track {
          box-shadow: inset 0 0 6px #a8b2bb;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #a8b2bb;
          border-radius: 4px;
        }
      }

      .ag-header-row {
        .ag-header-cell {
          line-height: 1.2;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
          text-align: left;
          color: #fff;
          background: #676f7c;

          &:hover {
            color: #fff;
            background: #676f7c;
          }
        }

        .ag-header-cell:not(.ag-column-resizing)+.ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover {
          color: #fff;
          background: #676f7c;
        }
      }

      .ag-body-viewport-wrapper.ag-layout-normal {
        overflow-x: scroll;
        overflow-y: scroll;
      }

      ::-webkit-scrollbar {
        -webkit-appearance: none;
        width: 8px;
        height: 6px;
      }

      ::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background: #a8b2bb;
        box-shadow: inset 0 0 6px #a8b2bb;
      }

      .ag-body {
        .ag-body-viewport {
          .ag-center-cols-clipper {
            .ag-row-odd {
              background-color: #f2f2f2;
            }

            .ag-cell {
              cursor: pointer;
            }

            .red-border {
              border: 1px solid red;
            }
          }
        }
      }
    }
  }
}

.searchInput {
  margin-left: auto;

  @media (max-width: 767px) {
    width: 100%;
  }
}

.addEmailsBtn {
  font-family: Noto Sans;
  width: 140px;
  height: 40px;
  border-radius: 4px;
  text-decoration: none;
  border: none;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  background-color: var(--primaryColor);
  color: #fff;
  margin-right: 16px;

  @media (max-width:767px) {
    font-size: 14px;
    margin-right: 8px;
    margin-top: 0px;
    margin-left: 0px;
  }
}

.searchContainer {
  display: flex;
  position: relative;

  input {
    max-width: 230px;
    width: 100%;
    padding-right: 30px;
    font-family: Noto Sans;
    font-size: 14px;
    color: var(--primaryColor);
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
  }

  .clearInputIcon {
    position: absolute;
    top: 8px;
    right: 6px;
    cursor: pointer;
    z-index: 99;
    background-color: transparent;
    border: 0px;
    padding: 0px;
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;

      path {
        fill: var(--primaryColor);
      }
    }
  }
}

.customHeader {
  text-align: center;
  white-space: pre-wrap !important;
}

.addEmailPopup {
  .addEmailContent {
    width: 100%;
    max-width: 680px;

    h2 {
      display: none;
    }

    .addEmailPopupDiv {
      padding: 24px;

      .addEmailText {
        font-family: Noto Sans;
        text-align: left;
        font-weight: 600;
        font-size: 18px;
        margin-top: 0px;
        margin-bottom: 12px;
        color: var(--primaryColor);
      }

      textarea {
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5;
        border-radius: 4px;
        color: var(--primaryColor);
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        padding: 6px 12px;

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
  
        &::-webkit-scrollbar-thumb {
          background: #9da2b2;
          border-radius: 50px;
        }
  
        &::-webkit-scrollbar-track {
          background: transparent;
        }

      }
      .textareaError {
        border: 1px solid red;
        &:focus{
          outline: 0;
        }
      }

      .addEmailBtn {
        display: flex;
        gap: 10px;
        margin-top: 24px;

        .cancelBtn {
          font-family: Noto Sans;
          width: 100%;
          height: 45px;
          border-radius: 6px;
          text-decoration: none;
          gap: 8px;
          border: none;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          background-color: var(--primaryColor);
          color: #fff;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

.message {
  color: green;
  font-weight: 500;
  font-size: 14px;
  height: 18px;
}

.error {
  color: red;
  font-weight: 500;
  font-size: 14px;
  min-height: 18px;
}

.messageContainer {
  min-height: 18px;
}

.uploadRefernceData {
  border: 1px solid #495057;
  border-radius: 4px;
  padding: 16px 20px;
  margin-bottom: 30px;

  .title {
      font-size: 24px;
      color: #000000;
      margin-bottom: 20px;
      font-weight: 600;
      text-align: center;
  }

  .uploadRefernceDataHeader {
      .dragBtnGrid {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin-bottom: 20px;

          .dragBtn {
              padding: 4px 8px;
              display: inline-flex;
              align-items: center;
              background-color: var(--primaryColor);
              border: dashed 1px #fff;
              justify-content: center;
              color: #fff;
              min-width: 100px;
              min-height: 28px;
              opacity: 0.5;
              cursor: unset;

              &[draggable="true"] {
                  cursor: move;
                  opacity: unset;
              }
          }
      }

  }

  .dragColumn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;

      .dragColumnlbl {
          font-size: 13px;
          padding: 4px 6px;
          display: inline-flex;
          align-items: center;
          background-color: #2d3f55;
          border: dashed 1px #fff;
          justify-content: flex-start;
          color: #fff;
          min-width: 180px;
          min-height: 28px;
      }

      button {
          font-size: 13px;
          padding: 4px 6px;
          display: inline-flex;
          align-items: center;
          background-color: #284d75;
          border: dashed 1px #fff;
          justify-content: flex-start;
          color: #fff;
          min-width: 180px;
          min-height: 28px;
          opacity: 0.5;
          cursor: unset;

          &[draggable="true"] {
              cursor: move;
              opacity: unset;
          }
      }
  }

  .yesAndnoBtn {
      display: flex;
      gap: 10px;
      padding-top: 20px;
      justify-content: center;

      .okBtn {
          width: 180px;
          height: 45px;
          border-radius: 4px;
          text-decoration: none;
          gap: 8px;
          border: none;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          background-color: var(--primaryColor);
          color: #fff;

          &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
          }
      }

  }
}

.addCohortMain{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .addBtnSection{
    .addInputBtn{
      font-family: Noto Sans;
      width: 80px;
      height: 36px;
      border-radius: 4px;
      text-decoration: none;
      border: none;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
      margin-right: 12px;
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
     }
  }
  .checkboxMain{
    display: flex;
    align-items: center;
    .containerChk{
      font-size: 14px;
      .checkmark{
        top:-8px
      }   
    }
  }
}

.selectDropdown {
  width: 100%;
  max-width: 350px;
  height: 48px;

  @media screen and (max-width: 768px) and (min-width: 320px) {
    width: 100%;
  }
}

.autocompleteDescPanel {
  border-radius: 4px;
  width: 100%;
  max-width: 350px;
  height: 38px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  padding-right: 4px;
  border-radius: 0px 0px 4px 4px;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 316px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
  }

  &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px #a8b2bb;
      border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
      background: #a8b2bb;
      border-radius: 4px;
  }


  li {    
    font-size: 16px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #333;
    box-shadow: none;
    padding: 4px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;

    &:hover {
      border-radius: 2px;
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      background-color: #fff;
      color: #000;
    }
  }
}

.errorStyle.errorStyle{
  .tooltip.tooltip{
      background-color: #ff5d47;
      color:#fff;
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #fff;
      text-transform: capitalize;
      margin-bottom: 5px;
  }
}

.inputFieldContainer {
  margin: 10px 0px;
  max-height: 300px;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #a8b2bb;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a8b2bb;
    border-radius: 4px;
  }

  table {
    width: 100%;
    border-spacing: 0px;
    border-collapse: collapse;
    border: 1px solid #9E9E9E;

    tr {
      th {
        background-color: var(--primaryColor);
        color: #fff;
        width: 46%;
        position: sticky;
        top: 0px;
        z-index: 99;
        &:first-child{
          width: 150px;
          text-align: center;
        }
      }

      th,
      td {
        font-size: 14px;
        text-align: left;
        font-family: Noto Sans;
        padding: 6px 8px;

      }
    }

    tbody {
      tr {
        td {
          border-top: 1px solid #9E9E9E;
          border-bottom: 1px solid #9E9E9E;

          &:first-child, &:nth-child(2) {
            border-right: 1px solid #9E9E9E;
          }

          &:first-child{
            text-align: center;
            font-size: 16px;
            font-weight: bold;
          }

          &.inputBox {
            input {
              height: 38px;
              font-family: Noto Sans;
              font-size: 14px;
              color: var(--primaryColor);
              border: 1px solid #ced4da;
              border-radius: 0.25rem;
            }
          }

          button.clearIcon {
            background-color: transparent;
            border: 0px;
            padding: 0px;
            display: flex;
            cursor: pointer;

            svg {
              width: 24px;
              height: 24px;

              path {
                fill: #ff0000
              }
            }
          }

          &.noErrorTxt {
            text-align: center;
            font-size: 16px;
            height: 51px;
          }
        }
      }
    }

  }

}