import {
  useMutation,
  useQuery,
  useQueryClient
} from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

export type DiscountedUsers = {
  first_name: string,
  last_name: string,
  email_id: string,
  is_discounted: '0' | '1',
  discount_rate: string,
  initial_constant_discount_period: string | null,
  discount_compression_starting_date: string,
  discount_compression_period: string,
  discount_pricing_column: string,
  is_discount_overriden: '0' | '1',
  seller_spread_rate: string,
  discountPercentage: string,
  sellerSpreadPercentage: string,
}

type discountedUsersServerResponse = {
  data: DiscountedUsers[] | { error_message: string };
}

const useGetDiscountedUsers = () => {
  return useQuery([reactQueryKeys.getDiscountedUses], async () => {
    try {
      const response = (await axios.get<discountedUsersServerResponse>(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/user/getDiscountedUsers`)).data;

      if (response.data) {
        if (
          typeof response.data === "object" && "error_message" in response.data ) {
          throw new Error(response.data.error_message);
        } else {
          return response.data;
        }
      } else {
        return null;
      }
      
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default useGetDiscountedUsers;
