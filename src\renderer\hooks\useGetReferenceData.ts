import { useMutation, useQueries, useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

const useGetReferenceData = () => {
  return useQuery([reactQueryKeys.getAllReferenceDataProduct], async () => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_SERVICE}/reference-data`
      );
      if (response.data) {
        if (
          typeof response.data === "object" &&
          "error_message" in response.data
        ) {
          throw new Error(response.data.error_message);
        } else {
          return response.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default useGetReferenceData;
