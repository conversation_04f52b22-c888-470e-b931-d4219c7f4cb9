import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

if(!process.env.NODE_ENV)
throw new Error('NODE_ENV is required')

const readFile = path.join(__dirname, `../.env.${process.env.NODE_ENV}`);
const writeFile = path.join(__dirname, '../.env');

const envFileData = fs.readFileSync(readFile);

fs.writeFileSync(writeFile, envFileData)