import * as yup from "yup";

export const CreateUserFromSchema = yup.object().shape({
  firstName: yup.string().default("").trim().required("Required"),
  lastName: yup.string().default("").trim().required("Required"),
  emailAddress: yup
    .string()
    .trim()
    .email("Please enter valid email")
    .matches(/^$|^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "Please enter valid email")
    .default("")
    .required("Required"),
  password: yup.string().required("Required").default("").typeError("Required").min(6,"Min lenght is 6"),
  companyName: yup.string().default("").trim().required("Required")
  .test('containsValidCharacters', "Please enter valid company name", (value) => {
    const validCharactersRegex = /<|>/g;
    if(validCharactersRegex.test(value)){
      return false;
    }else{
      return true;
    }
  }),
  companyEntity: yup.string().default("").trim().required("Required")
  .test('containsValidCharacters', "Please enter valid company entity/location", (value) => {
    const validCharactersRegex = /<|>/g;
    if(validCharactersRegex.test(value)){
      return false;
    }else{
      return true;
    }
  }),
  zipCode: yup.string().trim().min(5,"Min lenght is 5"),
  type: yup.string().default("").required("Required"),
  onboardSource: yup.string().default("").required("Required"),
});

export const EditUserFromSchema = yup.object().shape({
  userId: yup.number(),
  firstName: yup.string().default("").trim().required("Required"),
  lastName: yup.string().default("").trim().required("Required"),
  emailAddress: yup
    .string()
    .trim()
    .email("Please enter valid email")
    .matches(/^$|^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "Please enter valid email")
    .default("")
    .required("Required"),
  companyName: yup.string().default("").trim().required("Required")
  .test('containsValidCharacters', "Please enter valid company name", (value) => {
    const validCharactersRegex = /<|>/g;
    if(validCharactersRegex.test(value)){
      return false;
    }else{
      return true;
    }
  }),
  companyEntity: yup.string().default("").trim().required("Required")
  .test('containsValidCharacters', "Please enter valid company entity/location", (value) => {
    const validCharactersRegex = /<|>/g;
    if(validCharactersRegex.test(value)){
      return false;
    }else{
      return true;
    }
  }),
  zipCode: yup.string().trim().min(5,"Min lenght is 5"),
  password: yup.string().default(""),
  type: yup.string().default("").required("Required"),
  onboardSource: yup.string().default("").required("Required"),
  isExternalApiAdmin: yup.boolean().default(false).required("Required")
});

export type CreateUserFromSchemaType = yup.InferType<
  typeof CreateUserFromSchema
>;
export type EditUserFromSchemaType = yup.InferType<
  typeof EditUserFromSchema
>;
