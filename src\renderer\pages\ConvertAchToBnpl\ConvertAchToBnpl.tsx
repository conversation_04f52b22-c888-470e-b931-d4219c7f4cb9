import { Autocomplete, TextField, Tooltip } from "@mui/material";
import styles from "./ConvertAchToBnpl.module.scss";
import { useContext, useEffect } from "react";
import { useImmer } from "use-immer";
import useGetOpenPoNumberData from "../../hooks/useGetOpenPoNumberData";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Loader from "../../components/common/Loader";
import useRemoveSalesTax from "../../hooks/useRemoveSalesTax";
import { CommonCtx } from "../AppContainer";
import { PAYMENT_METHOD_ACH_CREDIT } from "../../utils/constant";
import useConvertAchToBnpl from "../../hooks/useConvertAchToBnpl";

const schema = yup.object({
  poNumber: yup.string().required("PO number is required"),
});

type FormData = {
  poNumber: string;
};

const ConvertAchToBnpl = () => {
  const [poLists, setPoLists] = useImmer<any[]>([]);

  const showPopupFormAnyComponent = useContext(CommonCtx);

  const { data: openPoNumberData, isLoading: isOpenPoNumberDataLoading, isFetching: isOpenPoNumberDataFetching } =
    useGetOpenPoNumberData();

  const {
    mutate: convertAchToBnpl,
    error: convertAchToBnplError,
    data: convertAchToBnplData,
    isLoading: isConvertAchToBnplLoading,
  } = useConvertAchToBnpl();

  const { register, control, handleSubmit } = useForm<FormData>({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (!isOpenPoNumberDataLoading && openPoNumberData && !isOpenPoNumberDataFetching) {
      const _setPoLists: any[] = [];
      openPoNumberData.forEach((data: any, index: number) => {
        if (
          !data.payment_method ||
          data.payment_method === PAYMENT_METHOD_ACH_CREDIT
        ) {
          _setPoLists.push({
            id: index,
            title: data.buyer_po_number,
            value: data.buyer_po_number,
          });
        }
      });

      setPoLists(_setPoLists);
    }
  }, [isOpenPoNumberDataLoading, openPoNumberData, isOpenPoNumberDataFetching]);

  useEffect(() => {
    if (
      !isConvertAchToBnplLoading &&
      convertAchToBnplData &&
      !convertAchToBnplError
    ) {
      showPopupFormAnyComponent(convertAchToBnplData);
    }
  }, [convertAchToBnplData, isConvertAchToBnplLoading]);

  const formSubmitHandler = (data: FormData) => {
    convertAchToBnpl({ data: { po_number: data.poNumber } });
  };

  return (
    <div>
      {isConvertAchToBnplLoading || isOpenPoNumberDataLoading || isOpenPoNumberDataFetching ? (
        <div className={styles.loaderImg}>
          <Loader />
        </div>
      ) : (
        <div className={styles.sendNotificationMain}>
          <div className={styles.title}> Convert ACH Order To BNPL Order</div>
          <form onSubmit={handleSubmit(formSubmitHandler)}>
            <label>
              <Controller
                name={register("poNumber").name}
                control={control}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <Tooltip
                    title={error?.message}
                    placement="top-end"
                    classes={{
                      popper: styles.errorStyle,
                      tooltip: styles.tooltip,
                    }}
                  >
                    <Autocomplete
                      className={styles.selectDropdown}
                      options={poLists}
                      value={
                        poLists.find((obj: any) => obj.value === value) ?? null
                      }
                      getOptionLabel={(option: any) => option.title ?? ""}
                      renderInput={(params) => (
                        <TextField {...params} label="Choose PO#" />
                      )}
                      onChange={(event, data: any) => {
                        onChange(data ? data.value : null);
                      }}
                      classes={{
                        root: styles.autoCompleteDesc,
                        popper: styles.autocompleteDescPanel,
                        paper: styles.autocompleteDescInnerPanel,
                        listbox: styles.listAutoComletePanel,
                      }}
                    />
                  </Tooltip>
                )}
              />
            </label>
            <div className={styles.btnSendNotif}>
              <button type="submit">Submit</button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default ConvertAchToBnpl;
