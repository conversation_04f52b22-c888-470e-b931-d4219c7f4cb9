import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { API_KEY_ENVIRONMENTS, reactQueryKeys } from "../utils/constant";

const useGetExternalApiKeys = (itemsPerPage: number, currentPage: number, search: string, env: string) => {
  return useQuery(
    [reactQueryKeys.getExternalApiKeys, itemsPerPage, currentPage, search, env],
    async () => {
      try {        
        const response = await axios.get(
          `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard${env === API_KEY_ENVIRONMENTS.sandbox ? `/${API_KEY_ENVIRONMENTS.sandbox}` : ""}/external-api-keys?page=${currentPage}&limit=${itemsPerPage}&search=${search}`
        );
        if (response.data?.data) {
          if (
            typeof response.data.data === "object" &&
            "error_message" in response.data.data
          ) {
            throw new Error(response.data.data.error_message);
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error: any) {
        throw new Error(error?.message);
      }
    }
    , {staleTime: 0, 
      cacheTime: 0,   
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      retry: false,}
  );
};

export default useGetExternalApiKeys;
