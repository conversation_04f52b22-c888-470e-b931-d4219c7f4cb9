import { Dispatch, SetStateAction, useEffect } from "react";
import { Autocomplete, Tooltip, Fade, TextField, SelectChangeEvent } from "@mui/material";
import clsx from "clsx";
import { Control, FieldErrors, UseFormRegister, UseFormSetError, UseFormSetValue, UseFormWatch } from "react-hook-form";
import { useImmer } from "use-immer";
import MatSelect from "../../../../components/common/MatSelect";
import { ReferenceDataProduct, getFloatRemainder, getUnitPostScript, getValidSearchData, orderIncrementPrefix, priceUnits, searchProducts } from '@bryzos/giss-ui-library';
import { SchemaType } from "../EditLinesPopup";
import styles from '../../UpdateQtySendInvoiceEmail.module.scss'
import { useDebouncedValue } from "@mantine/hooks";
import { calculateLineWeight, calculateTotalWeightForProduct } from "../../../../utils/helper";

export type Props = {
    index: number;
    watch: UseFormWatch<SchemaType>;
    setValue: UseFormSetValue<SchemaType>;
    register: UseFormRegister<SchemaType>;
    control: Control<SchemaType>;
    errors: FieldErrors<SchemaType>;
    productData: any;
    isEdit: boolean;
    initialLength: number;
    removeLineItem: (index: number) => void;
    setIsDirty: (val: boolean) => void;
    calculateOrderTotalPurchase: () => void;
    isDirty: boolean;
    isCancelled: boolean;
    isBuyerClosed: boolean;
    isSellerClosed: boolean;
    isProductAvailable: boolean;
    setIsDisable: Dispatch<SetStateAction<boolean>>;
    setIsProductNull: Dispatch<SetStateAction<boolean>>;
    getProductPriceObj: (productIdList: any, totalWeight: any) => void;
    debouncedBuyerExtTotal: number;
    debouncedSellerExtTotal: number;
    productPriceMapping: any;
    debouncedTotalWeight: number;
};

const EditLine: React.FC<Props> = ({ index, watch, setValue, register, control, errors, productData, isEdit, initialLength, removeLineItem, setIsDirty, calculateOrderTotalPurchase, setIsDisable, isDirty, isCancelled, isBuyerClosed, isSellerClosed, setIsProductNull, isProductAvailable, getProductPriceObj, debouncedBuyerExtTotal, debouncedSellerExtTotal, productPriceMapping, debouncedTotalWeight }) => {
    const product = watch(`orderLines.${index}.product`);
    const [qtyUnitOptions, setqtyUnitOptions] = useImmer<string[] | null>(null);
    const [priceUnitOptions, setpriceUnitOptions] = useImmer<string[] | null>(null);
    const [descriptionInput, setDescriptionInput] = useImmer("");
    const [isDisableField, setIsDisableField] = useImmer<boolean>(isEdit);
    const [error, setError] = useImmer<string>('');
    const [multiplierChangeInBuyerPricing, setMultiplierChangeInBuyerPricing] = useImmer<number>(1);
    const [multiplierChangeInSellerPricing, setMultiplierChangeInSellerPricing] = useImmer<number>(1);
    const [isFocused, setIsFocused] = useImmer<boolean>(false);
    const [openOptions, setOpenOptions] = useImmer<boolean>(false);
    const [debouncedBuyerExt] = useDebouncedValue(watch(`orderLines.${index}.buyerExtended`), 500);
    const [debouncedSellerExt] = useDebouncedValue(watch(`orderLines.${index}.sellerExtended`), 500);
    const [debouncedQty] = useDebouncedValue(watch(`orderLines.${index}.quantity`), 500);
    const [debouncedWeight] = useDebouncedValue(watch(`orderLines.${index}.weight`), 500);
    const [productExists, setProductExists] = useImmer<boolean>(isProductAvailable);
    const [isLineDirty, setIsLineDirty] = useImmer<boolean>(false);
    const [isLineWeightDirty, setIsLineWeightDirty] = useImmer<boolean>(false);

    useEffect(() => {
        setDropDownOptions(product);
        // const buyerExtended= watch(`orderLines.${index}.buyerExtended`);
        // const sellerExtended= watch(`orderLines.${index}.sellerExtended`);
        // const qty =watch(`orderLines.${index}.quantity`);
        // const qtyUnit = watch(`orderLines.${index}.qtyUnit`);
        // if(buyerExtended && qty && qtyUnit && productExists)
        // pricingMultiplier("buyerPrice", buyerExtended, qty, qtyUnit.toLowerCase());
        // if(sellerExtended && qty && qtyUnit && productExists)
        // pricingMultiplier("sellerPrice", sellerExtended, qty, qtyUnit.toLowerCase());
    }, []);

    useEffect(()=>{
        if(isDirty)
        calculateOrderTotalPurchase();
    },[debouncedBuyerExt, debouncedSellerExt]);

    useEffect(()=>{
        if(isEdit && productPriceMapping && Object.keys(productPriceMapping).length > 1 && productPriceMapping[index + 1] && product){
            const productPriceMappingData = productPriceMapping[index + 1]?.prices;
            (product as any).priceObj = productPriceMappingData;
            qtyChangeHandler(product, true);
        }
    },[productPriceMapping, product])

    useEffect(()=>{
        if(product && productExists && isLineDirty){
            getSpecificLinePrices(product);
        }
    },[debouncedQty])

    useEffect(()=>{
        if(product && productExists && isLineWeightDirty){
            getSpecificLinePrices(product);
        }
    },[debouncedWeight])

    const setDropDownOptions = (product: any, reset?: boolean) => {
        if (product) {
            setDescriptionInput(product.UI_Description);
            const qtyunits: string[] = product.QUM_Dropdown_Options;
            const priceUnits: string[] = product.PUM_Dropdown_Options;
            setqtyUnitOptions(qtyunits);
            setpriceUnitOptions(priceUnits);
            if(reset){
                setValue(`orderLines.${index}.qtyUnit`, qtyunits[0]);
                setValue(`orderLines.${index}.priceUnit`, priceUnits[0]);
            }
        } 
    }

    function display(data: any) {
        const lines = data.UI_Description.split("\n");
        const firstLine = lines[0];
        const restLines = lines.slice(1);

        return (
            <>
                <span className="liHead">{firstLine}</span>
                {restLines.map((line: any, index: any) => (
                    <span key={index}>{line}</span>
                ))}
            </>
        );
    }

    const getProductPricePerUnit = (type: string, product: any, priceUnit: string | null)=> {
        if(!priceUnit) return 0;
        return product.priceObj?.[type]?.[priceUnit?.toLowerCase()];
    }

    const priceUnitChangeHandler = async (product: any, resetMulti?: boolean, shouldGetSpecificLinePrices?: boolean) => {
        if(shouldGetSpecificLinePrices && (!product?.priceObj || Object.keys(product?.priceObj || {}).length === 0)) await getSpecificLinePrices(product);
        const priceUnit = watch((`orderLines.${index}.priceUnit`))?.toLowerCase() || '';
        const buyerPrice = getProductPricePerUnit('buyerPrice', product, priceUnit);
        const sellerPrice = getProductPricePerUnit('sellerPrice', product, priceUnit);
        const buyer_multiplier = resetMulti ? 1 :multiplierChangeInBuyerPricing;
        const seller_multiplier = resetMulti ? 1 :multiplierChangeInSellerPricing;
        const precision = priceUnit?.toLowerCase() === priceUnits.lb ? 4 : 2;
        setValue(`orderLines.${index}.buyerPrice`, +(buyerPrice * buyer_multiplier).toFixed(precision) || 0);
        setValue(`orderLines.${index}.sellerPrice`, +(sellerPrice * seller_multiplier).toFixed(precision) || 0);
        // if(!resetMulti) qtyChangeHandler(product);
    }

    const onProductChange = async (_product:any) => {
        if(_product){
            setValue(`orderLines.${index}.product`, _product);
            await qtyChangeHandler(_product, false);

            // setProduct(_product);
            setValue(`orderLines.${index}.product`, _product);
            setDropDownOptions(_product, true);
            priceUnitChangeHandler(_product, true);
            setIsDisableField(false);
            setValue(`orderLines.${index}.quantity`, 0);
            setError('');
            setValue(`orderLines.${index}.domesticMaterialOnly`, false);
            setProductExists(true);
            setIsLineDirty(false);
        }
        else setIsProductNull(true);
    }

    const getExtendedPricing = (type: string, qty: number, qtyUnit: string) => {
        const pricePerQtyUnit = (product as any)?.priceObj?.[type]?.[qtyUnit.toLowerCase()];
        if(!qty || !pricePerQtyUnit) return 0;
        return qty * pricePerQtyUnit;
    }


    const qtyChangeHandler = async (product:any, isDisableGetSpecificLinePrices: boolean=true, qtyVal?: number, _qtyUnit?: string) => {
        if(product){
            setIsDirty(true);
            const qty = qtyVal ?? +(watch(`orderLines.${index}.quantity`) ?? 0);
            const qtyUnit = _qtyUnit?? watch(`orderLines.${index}.qtyUnit`) ?? '';
            if(!isDisableGetSpecificLinePrices) await getSpecificLinePrices(product);

            const orderIncrement = product[`${orderIncrementPrefix}${qtyUnit === priceUnits.ea ? priceUnits.pc : qtyUnit}`];
            if( qty && (isEdit || getFloatRemainder(qty??"" ,orderIncrement) === 0) ){
                const buyerExtendedValue = getExtendedPricing('buyerPrice', qty, qtyUnit);
                const sellerExtendedValue = getExtendedPricing('sellerPrice', qty, qtyUnit);

                if(!isLineWeightDirty){
                    const lineWeight = calculateLineWeight(watch(`orderLines.${index}`));
                    setValue(`orderLines.${index}.weight`, +lineWeight.toFixed(2));
                }

                setValue(`orderLines.${index}.buyerExtended`, +buyerExtendedValue.toFixed(2));
                setValue(`orderLines.${index}.sellerExtended`, +sellerExtendedValue.toFixed(2));
                setError('');
                setIsDisable(false);
            }
            else{
                if(qty && getFloatRemainder(qty ,orderIncrement)) setError(`Quantity can only be multiples of ${orderIncrement}`);
                setValue(`orderLines.${index}.weight`, 0);
                setValue(`orderLines.${index}.buyerExtended`, 0);
                setValue(`orderLines.${index}.sellerExtended`, 0);
                setIsDisable(true);
            }
            setMultiplierChangeInBuyerPricing(1);
            setMultiplierChangeInSellerPricing(1);
            priceUnitChangeHandler(product, true);
            calculateUnitPrice("buyerPrice", 1, product);
            calculateUnitPrice("sellerPrice", 1, product);
        }
    }

    const calculateUnitPrice = (type: string, changeOfValueInPercentageMultiplier: number, _product?: any)=>{
        const calcPricePerUnit = getProductPricePerUnit(type, _product ?? product, watch((`orderLines.${index}.qtyUnit`)));
        const target = type === 'buyerPrice' ? 'buyer_calculation_price_per_unit' : 'seller_calculation_price_per_unit';
        setValue(`orderLines.${index}.${target}`, +(calcPricePerUnit * changeOfValueInPercentageMultiplier).toFixed(2))
    }

    const onPriceChange = async (target: 'buyerExtended' | 'sellerExtended', value: number) => {
        setIsDirty(true);
        if(!product?.priceObj || Object.keys(product?.priceObj || {}).length === 0){
            await getSpecificLinePrices(product);
        }
        const qty = watch(`orderLines.${index}.quantity`);
        const qtyUnit = watch(`orderLines.${index}.qtyUnit`)?.toLowerCase() ?? '';
        if(qty){
            const type = target === 'buyerExtended' ? 'buyerPrice' : 'sellerPrice';
            const actualPricePerUnit = getProductPricePerUnit(type, product, watch((`orderLines.${index}.priceUnit`))?.toLowerCase() || '');
            const changeOfValueInPercentageMultiplier = (value / actualPricePerUnit);
            if(type === 'buyerPrice')
            setMultiplierChangeInBuyerPricing(changeOfValueInPercentageMultiplier);
            else
            setMultiplierChangeInSellerPricing(changeOfValueInPercentageMultiplier);
            const actualExtendedPricing = getExtendedPricing(type, qty, qtyUnit);
            setValue(`orderLines.${index}.${target}`, +(actualExtendedPricing*changeOfValueInPercentageMultiplier).toFixed(2));
            //calculate price per according to selected qty unit unit for server calculation
            calculateUnitPrice(type, changeOfValueInPercentageMultiplier);
        }
    }

    const pricingMultiplier = (type: 'buyerPrice' | 'sellerPrice', value: number, qty: number, qtyUnit: string) => {
        const actualExtendedPricing = getExtendedPricing(type, qty, qtyUnit);
        const changeOfValueInPercentageMultiplier = (value / actualExtendedPricing);
        if(type === 'buyerPrice')
        setMultiplierChangeInBuyerPricing(changeOfValueInPercentageMultiplier);
        else
        setMultiplierChangeInSellerPricing(changeOfValueInPercentageMultiplier);
        return changeOfValueInPercentageMultiplier;
    }

    const onExtendedPriceChange = async(target: 'buyerPrice' | 'sellerPrice', value: number) => {
        if(!product?.priceObj || Object.keys(product?.priceObj || {}).length === 0){
            await getSpecificLinePrices(product);
        }
        setIsDirty(true);
        const qty = watch(`orderLines.${index}.quantity`);
        const qtyUnit = watch(`orderLines.${index}.qtyUnit`) ?? '';
        if(qty){
            const changeOfValueInPercentageMultiplier = pricingMultiplier(target, value, qty, qtyUnit);
            const price = getProductPricePerUnit(target, product, watch((`orderLines.${index}.priceUnit`)));
            setValue(`orderLines.${index}.${target}`, +(price*(changeOfValueInPercentageMultiplier)).toFixed(2));
            calculateUnitPrice(target, changeOfValueInPercentageMultiplier);
        }
    }

    const textToNumberTransform = (inputValue: string) => {
        if(inputValue){
            inputValue = inputValue.replace(/[^0-9.]/g, '');
            const decimalCount = inputValue.split('.').length - 1;
            if (decimalCount > 1) {
                inputValue = inputValue.slice(0, -1);
            }
        }
        return inputValue;
    }

    const getSpecificLinePrices = async (product: any) => {
        let totalWeight = calculateTotalWeightForProduct(watch(`orderLines`));
        if(isEdit && isLineWeightDirty){
            const orders = watch('orderLines') ?? [];
            // Calculate total weight from order lines weight data
            const currentTotalWeight = orders.reduce((total: number, order: any) => {
                if(!order?.isCancelled) return total + (order.weight || 0);
                return total;
            }, 0);
            totalWeight = currentTotalWeight;
        }
        const cartItemsList = [
            {
                "po_line": index + 1,
                "product_id": product.Product_ID
            }
        ]
        const productPriceMapping = await getProductPriceObj(cartItemsList, totalWeight);
        (product as any).priceObj = (productPriceMapping as any)[index + 1]?.prices;
        qtyChangeHandler(product, true, debouncedQty ?? 0);
    }

    const disableRow = isCancelled || isBuyerClosed || isSellerClosed;
    const tooltipTitle = isCancelled? 'Cancelled' : !productExists ? 'Product not available' : (isBuyerClosed && isSellerClosed ? 'Buyer, Seller Closed' : isBuyerClosed ? 'Buyer Closed' : 'Seller Closed');

    return (
        <tr className={clsx((!isEdit && index < initialLength || disableRow) && styles.disabledRow , isCancelled && styles.cancelledRow, (isBuyerClosed || isSellerClosed) && styles.closedRow)} key={index}>
            <td>
                {(disableRow || !productExists)  && 
                    <Tooltip title={tooltipTitle}
                    classes={{
                        popper: clsx((isCancelled) ? styles.errorStyle : (isBuyerClosed ||isSellerClosed || !isProductAvailable) && styles.warningStyle),
                        tooltip: styles.tooltip,
                    }}>
                        <span className={clsx(styles.indicationTooltip, isCancelled  ? styles.cancelledTooltip : (isBuyerClosed || isSellerClosed || !isProductAvailable) && styles.closedTooltip)}></span>
                    </Tooltip> 
                }
            </td>
            <td>
                {index + 1}
            </td>
            <td>  
               { (product && descriptionInput) &&<>
               
                <Tooltip title={(product as any).UI_Description }
           placement='bottom-start'
            classes={{
                popper: styles.cassAutocompleteTooltipStyle,
                tooltip: styles.tooltip
            }}>
                     <span className={styles.editLineExclamation}>i</span>

                </Tooltip>
               
               </>
               } 
                </td>
            <td className="editLineSelectDropdown">
        
                <Autocomplete
                    id={`combo-box-demo`}
                    sx={{ width: '100%' }}
                    {...register(`orderLines.${index}.product`)}
                    open={openOptions}
                    options={productData}
                    value={product}
                    inputValue={descriptionInput}
                    onFocus={()=>setIsFocused(true)}
                    onBlur={(e)=> {
                        register(`orderLines.${index}.product`).onBlur(e);
                        setIsFocused(false);
                        setOpenOptions(false);
                        setDescriptionInput((watch(`orderLines.${index}.product`) as ReferenceDataProduct)?.UI_Description);
                    }}
                    onInputChange={(event, value, reason) => {
                        if(reason === 'reset'){
                            setDescriptionInput((watch(`orderLines.${index}.product`) as ReferenceDataProduct)?.UI_Description);
                            setIsProductNull(false);
                        }
                        else
                        setDescriptionInput(value);
                        if(isFocused && value.length > 0) setOpenOptions(true)
                        if(reason === 'clear'){
                            setValue(`orderLines.${index}.product`, null);
                            // setProduct(null)
                        }
                    }}
                    classes={{ root: styles.autoCompleteDesc, popper: styles.autocompleteDescPanel, paper: styles.autocompleteDescInnerPanel, listbox: styles.listAutoComletePanel }}
                    filterOptions={(options, state) => state.inputValue.trim().length > 1 ? searchProducts(options, getValidSearchData(state.inputValue), state.inputValue, null) : []}
                    isOptionEqualToValue={(option, value: any) => (option as any)?.UI_Description === (value as any)?.UI_Description }
                    getOptionLabel={(item: any) => item.UI_Description ? item.UI_Description : "" }
                    renderInput={(params) => <TextField {...params} placeholder="Click to Enter Line" />}
                    onChange={(e, item) => {
                        onProductChange(item)
                        setOpenOptions(false);
                        register(`orderLines.${index}.product`).onChange(e);
                    }}
                    renderOption={(props, option: any) => <li key={option.id} {...props}> {display(option)}</li>}
                />
            </td>
            <td className={clsx(styles.textCenter,styles.chkEditLines)}>
                {
                    (watch(`orderLines.${index}.product`) as ReferenceDataProduct)?.domestic_material_only &&
                    <input
                        type="checkbox"
                        checked={!!watch(`orderLines.${index}.domesticMaterialOnly`)}
                        {...register(`orderLines.${index}.domesticMaterialOnly`)}
                        onChange={(e)=>{
                            register(`orderLines.${index}.domesticMaterialOnly`).onChange(e);
                            setIsDirty(true);
                        }}
                    />
                }
            </td>
            <td>
            <Tooltip title={error}
             classes={{
                popper: styles.errorStyle,
                tooltip: styles.tooltip,
              }}
            >
            <input className={clsx(styles.inputEditLine, error && styles.inputError)} type="text" {...register(`orderLines.${index}.quantity`, { valueAsNumber: true })} onChange={(e)=>{
                    e.target.value = textToNumberTransform(e.target.value);
                    setIsLineDirty(true);
                    setIsLineWeightDirty(false);
                    if(!productExists) setIsDirty(true);
                    register(`orderLines.${index}.quantity`).onChange(e);
                }} />
            </Tooltip>
                
                <p className={styles.errorText}>{errors.orderLines?.[index]?.quantity?.type === "required" && "Required"}</p>
            </td>
            <td>
                {
                (qtyUnitOptions && qtyUnitOptions.length > 0 && <MatSelect className={clsx("editLinesDropdown", styles.dropdownUnitUppercase)} MenuProps={{
                    classes: {
                        paper: styles.Dropdownpaper,
                        list: styles.muiMenuList,
                    }
                }}
                    fieldName={register(`orderLines.${index}.qtyUnit`).name} control={control}
                    onChange={(e)=>{
                        if(productExists) qtyChangeHandler(product, false, undefined, e.target.value?.toString() ?? '');
                        else setIsDirty(true);
                        setIsLineDirty(true);
                        setIsLineWeightDirty(false);
                        register(`orderLines.${index}.qtyUnit`).onChange(e);
                    }}
                    options={qtyUnitOptions?.map((x) => ({ title: (x.toLowerCase() === priceUnits.ea) ? 'PC' : x, value: x })) ?? []} 
                    disabled={!productExists}/>)
                }
                <p className={styles.errorText}>{errors.orderLines?.[index]?.qtyUnit?.type === "required" && "Required"}</p>
            </td>
            <td>
                <input className={styles.inputEditLine} 
                {...register(`orderLines.${index}.sellerPrice`)} 
                type="text" 
                onChange={e => {
                    e.target.value = textToNumberTransform(e.target.value);
                    if(productExists) onPriceChange('sellerExtended', +e.target.value);
                    else setIsDirty(true);
                    register(`orderLines.${index}.sellerPrice`).onChange(e);
                }} 
                disabled={!isEdit} />
                <p className={styles.errorText}>{errors.orderLines?.[index]?.priceUnit?.type === "required" && "Required"}</p>
            </td>
            <td>
                <input 
                className={styles.inputEditLine} 
                {...register(`orderLines.${index}.buyerPrice`)} 
                type="text" 
                onChange={e => {
                    e.target.value = textToNumberTransform(e.target.value);
                    if(productExists) onPriceChange('buyerExtended', +e.target.value);
                    else setIsDirty(true);
                    register(`orderLines.${index}.buyerPrice`).onChange(e);
                }} 
                disabled={!isEdit} />
                <p className={styles.errorText}>{errors.orderLines?.[index]?.priceUnit?.type === "required" && "Required"}</p>
            </td>
            <td>
                {
                priceUnitOptions && priceUnitOptions.length > 0 && <MatSelect className={clsx("editLinesDropdown", styles.dropdownUnitUppercase)} MenuProps={{
                    classes: {
                        paper: styles.Dropdownpaper,
                        list: styles.muiMenuList,
                    }
                }}
                    fieldName={register(`orderLines.${index}.priceUnit`).name} control={control}
                    onChange={(e)=>{
                        register(`orderLines.${index}.priceUnit`).onChange(e);
                        if(productExists) priceUnitChangeHandler(product, false, true);
                        setIsDirty(true);
                    }}
                    options={priceUnitOptions?.map((x) => ({ title: (x.toLowerCase() === priceUnits.ea) ? 'PC' : x, value: x })) ?? []} 
                    disabled={!productExists}/>
                }
                <p className={styles.errorText}>{errors.orderLines?.[index]?.priceUnit?.type === "required" && "Required"}</p>
            </td>
            <td>
                <input 
                    className={styles.inputEditLine} 
                    {...register(`orderLines.${index}.weight`, { valueAsNumber: true })} 
                    onChange={e => {
                        e.target.value = textToNumberTransform(e.target.value);
                        register(`orderLines.${index}.weight`).onChange(e);
                        setIsDirty(true);
                        setIsLineDirty(false);
                        setIsLineWeightDirty(true);
                    }} 
                    type="text" 
                    disabled={!isEdit} />
               <p className={styles.errorText}>{errors.orderLines?.[index]?.weight?.type === "required"}</p>
            </td>
            <td>
                <input 
                className={styles.inputEditLine} 
                {...register(`orderLines.${index}.sellerExtended`, { valueAsNumber: true })} 
                type="text" 
                onChange={e => {
                    e.target.value = textToNumberTransform(e.target.value);
                    register(`orderLines.${index}.sellerExtended`).onChange(e);
                    if(productExists) onExtendedPriceChange('sellerPrice', +e.target.value);
                    else setIsDirty(true);
                }} 
                disabled={!isEdit}/>
                <p className={styles.errorText}>{errors.orderLines?.[index]?.sellerExtended?.type === "required" && "Required"}</p>
            </td>
            <td>
                <input 
                className={styles.inputEditLine} 
                {...register(`orderLines.${index}.buyerExtended`, { valueAsNumber: true })} 
                type="text" 
                onChange={e => {
                    e.target.value = textToNumberTransform(e.target.value);
                    register(`orderLines.${index}.buyerExtended`).onChange(e);
                    if(productExists) onExtendedPriceChange('buyerPrice', +e.target.value);
                    else setIsDirty(true);
                }}
                disabled={!isEdit} />
                <p className={styles.errorText}>{errors.orderLines?.[index]?.buyerExtended?.type === "required" && "Required"}</p>
            </td>
            {!isEdit && <td>
                {index >= initialLength && <button onClick={()=>{removeLineItem(index)}}> x </button>}
            </td>}
        </tr>
    );
};

export default EditLine;
