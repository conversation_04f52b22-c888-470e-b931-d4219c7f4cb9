import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../utils/constant";

const useGetNotificationEvents = () => {
  return useQuery([reactQueryKeys.getNotificationEvents], async () => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/widget-admin-dashboard/getNotificationEvents`
      );
      if (response.data?.data?.reference_notification_events) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data.reference_notification_events;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default useGetNotificationEvents;
