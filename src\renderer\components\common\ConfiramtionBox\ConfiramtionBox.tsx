import React from "react";
import MatPopup from "../MatPopup";
import styles from "./ConfiramtionBox.module.scss";

type Props = {
  openConfirmationPopup: boolean;
  confirmationYes: () => void;
  confirmationNo: () => void;
  confirmationText?: string;
};

const ConfiramtionBox: React.FC<Props> = ({
  openConfirmationPopup,
  confirmationYes,
  confirmationNo,
  confirmationText = 'Do you want to continue ?'
}) => {
  return (
    <div>
      <MatPopup
        className={styles.orderContinuePopup}
        open={openConfirmationPopup}
      >
        <div className={styles.continuePopup}>
          <p className={styles.continuetext}>{confirmationText}</p>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.okBtn} onClick={confirmationYes}>
              Yes
            </button>
            <button className={styles.okBtn} onClick={confirmationNo}>
              No
            </button>
          </div>
        </div>
      </MatPopup>
    </div>
  );
};

export default ConfiramtionBox;
